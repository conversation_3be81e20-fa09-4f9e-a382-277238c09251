# compile.py
import os
import subprocess
import sys
import logging
from logging.handlers import RotatingFileHandler
import shutil
import time
import socket

def setup_logging(log_dir):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "compile.log")

    # 创建一个格式化器
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')

    # 创建文件处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 获取根日志记录器并配置
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)


def run_command(command, cwd=None):
    """运行一个系统命令并实时显示输出"""
    try:
        logging.info(f"运行命令: {' '.join(command)}")

        # 使用 subprocess.run 执行命令并等待其完成
        result = subprocess.run(
            command,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            check=False  # 不自动抛出异常
        )

        # 逐行读取输出并记录
        for line in result.stdout.splitlines():
            logging.info(line)

        # 检查返回码
        if result.returncode != 0:
            logging.error(f"命令失败: {' '.join(command)}")
            # 根据需要决定是否抛出异常以停止后续命令
            raise subprocess.CalledProcessError(result.returncode, command)
    except subprocess.CalledProcessError as e:
        logging.error(f"命令 '{' '.join(e.cmd)}' 以返回码 {e.returncode} 失败。")
    except Exception as e:
        logging.error(f"执行命令时出错: {e}")



def find_executable(name):
    path = shutil.which(name)
    logging.info(f'find {name} path = {path}')
    if not path:
        # 添加一些常见的路径
        common_paths = [
            "/usr/local/tensorrt/bin",
            "/usr/bin",
            "/usr/local/bin",
            "/opt/tensorrt/bin"
        ]
        for common_path in common_paths:
            possible_path = os.path.join(common_path, name)
            if os.path.exists(possible_path) and os.access(possible_path, os.X_OK):
                logging.info(f"find {name} possible_path = {possible_path}")
                return possible_path
    return path



def find_tensorrt_paths():
    """搜索系统中所有可能的 TensorRT 库路径"""
    try:
        # 首先尝试使用 whereis 命令查找可执行文件位置
        whereis_cmd = ["whereis", "tensorrt"]
        whereis_result = subprocess.run(
            whereis_cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )

        # 使用 find 命令，但限制在几个主要目录下搜索，避免全盘扫描
        search_dirs = [
            "/usr/local",
            "/usr/lib",
            "/usr/lib64",
            "/usr/local/cuda",
            "/opt/tensorrt",
            "/usr/local/tensorrt"
        ]

        lib_paths = set()

        for search_dir in search_dirs:
            if not os.path.exists(search_dir):
                continue

            search_cmd = [
                "find",
                search_dir,
                "-name", "libnvinfer.so*",
                "-o",
                "-name", "libnvinfer_builder_resource.so*",
                "2>/dev/null"
            ]

            result = subprocess.run(
                search_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 获取所有找到的库文件路径
            lib_files = result.stdout.strip().split('\n')
            # 提取库文件所在的目录路径
            lib_paths.update(os.path.dirname(path) for path in lib_files if path)

        # 添加一些常见的路径作为备选
        common_paths = [
            "/usr/local/tensorrt/lib",
            "/usr/local/tensorrt/targets/x86_64-linux-gnu/lib",
            "/usr/lib/x86_64-linux-gnu",
            "/usr/local/cuda/lib64",
            "/usr/local/cuda/targets/x86_64-linux/lib",
            "/usr/local/cuda-*/lib64",
            "/usr/local/tensorrt/lib",
            "/usr/local/tensorrt-*/lib"
        ]

        # 展开通配符路径
        import glob
        expanded_paths = []
        for path in common_paths:
            if '*' in path:
                expanded_paths.extend(glob.glob(path))
            else:
                expanded_paths.append(path)

        # 将存在的常见路径添加到结果中
        lib_paths.update(path for path in expanded_paths if os.path.exists(path))

        if not lib_paths:
            logging.warning("通过搜索未找到 TensorRT 库路径")

        # 记录所有找到的路径
        for path in lib_paths:
            logging.info(f"找到 TensorRT 库路径: {path}")

        return list(lib_paths)
    except Exception as e:
        logging.error(f"搜索 TensorRT 库路径时出错: {e}")
        return []

def compile_dir(models_dir = "/workspace/hngpt/models"):
    logging.info("开始编译模型。")
    os.chdir(models_dir)
    logging.info(f"切换到目录: {models_dir}")

    # 自动搜索 TensorRT 库路径
    tensorrt_lib_paths = find_tensorrt_paths()

    if not tensorrt_lib_paths:
        logging.error("未找到任何 TensorRT 库路径")
        sys.exit(1)

    logging.info(f"找到的 TensorRT 库路径: {tensorrt_lib_paths}")

    # 创建配置文件并更新库路径
    with open("/etc/ld.so.conf.d/tensorrt.conf", "w") as f:
        for path in tensorrt_lib_paths:
            f.write(path + "\n")

    # 刷新库缓存
    run_command(["ldconfig"])

    # 验证必要的库文件是否存在
    required_libs = [
        "libnvinfer.so.8",
        "libnvinfer_builder_resource.so.8.6.1",
        "libcudart.so"
    ]

    missing_libs = []
    for lib in required_libs:
        found = False
        for path in tensorrt_lib_paths:
            if os.path.exists(os.path.join(path, lib)):
                found = True
                break
        if not found:
            missing_libs.append(lib)

    if missing_libs:
        logging.error(f"缺少必要的库文件: {', '.join(missing_libs)}")
        logging.error("请确保已正确安装 TensorRT 和 CUDA")
        sys.exit(1)

    trtexec_path = find_executable("trtexec")
    # 解析 ollama 可执行路径（优先 /usr/bin，其次 /usr/local/bin，最后 PATH）
    ollama_bin = "/usr/bin/ollama" if os.path.exists("/usr/bin/ollama") else (
        "/usr/local/bin/ollama" if os.path.exists("/usr/local/bin/ollama") else shutil.which("ollama")
    )
    if not trtexec_path:
        logging.error("未找到 trtexec 可执行文件，请确保已正确安装 TensorRT 并在 PATH 中")
        sys.exit(1)

    # 定义命令列表，每个命令是一个字典，包含输入文件、输出文件和命令
    commands = [
        # OCR v4 模型 - 最优固定形状配置
        {
            "input": "v4.det.onnx",
            "output": "v4.det.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v4.det.onnx",
                "--saveEngine=v4.det.trt",
                "--fp16"
            ]
        },
        {
            "input": "v4.cls.onnx",
            "output": "v4.cls.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v4.cls.onnx",
                "--saveEngine=v4.cls.trt",
                "--fp16"
            ]
        },
        {
            "input": "v4.rec.onnx",
            "output": "v4.rec.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v4.rec.onnx",
                "--saveEngine=v4.rec.trt",
                "--fp16"
            ]
        },
        {
            "input": "v5.det.onnx",
            "output": "v5.det.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v5.det.onnx",
                "--saveEngine=v5.det.trt",
                "--fp16"
            ]
        },
        {
            "input": "v5.cls.onnx",
            "output": "v5.cls.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v5.cls.onnx",
                "--saveEngine=v5.cls.trt",
                "--fp16"
            ]
        },
        {
            "input": "v5.rec.onnx",
            "output": "v5.rec.trt",
            "cmd": [
                trtexec_path,
                "--onnx=v5.rec.onnx",
                "--saveEngine=v5.rec.trt",
                "--fp16"
            ]
        },
        {
            "input": "doc_layout.onnx",
            "output": "doc_layout.trt",
            "cmd": [
                trtexec_path,
                "--onnx=doc_layout.onnx",
                "--saveEngine=doc_layout.trt",
                "--fp16"
            ]
        }
    ]

    # 运行所有命令
    for cmd_info in commands:
        input_file = cmd_info["input"]
        output_file = cmd_info["output"]
        cmd = cmd_info["cmd"]

        if not os.path.exists(input_file):
            logging.warning(f"输入文件不存在，跳过编译: {input_file}")
            continue

        if output_file and os.path.exists(output_file):
            logging.info(f"目标文件已存在，跳过编译: {output_file}")
            continue

        if "ollama" in cmd[0]:
            model_name = cmd[2]

            # 如果模型已存在，先删除
            try:
                delete_cmd = ["/usr/bin/ollama", "rm", model_name]
                run_command(delete_cmd)
                logging.info(f"已删除现有模型: {model_name}")
            except Exception as e:
                logging.warning(f"删除模型时出错: {e}")

            # 创建新模型
            try:
                run_command(cmd)

                # 验证模型是否创建成功
                check_cmd = ["/usr/bin/ollama", "list"]
                result = subprocess.run(check_cmd, stdout=subprocess.PIPE, text=True)
                model_list = [line.split()[0].split(':')[0] for line in result.stdout.splitlines() if line.strip()]

                if model_name not in model_list:
                    raise Exception(f"模型 {model_name} 创建失败")

                logging.info(f"模型 {model_name} 创建成功")
            except Exception as e:
                logging.error(f"创建或验证模型时出错: {e}")
                raise

            continue

        # 执行编译命令
        run_command(cmd, cwd=models_dir)
    logging.info("模型编译完成。")

if __name__ == "__main__":
    setup_logging("log")
    compile_dir()