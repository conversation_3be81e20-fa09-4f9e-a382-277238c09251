[program:compile_models]
command=python /workspace/hngpt/compile.py
environment=HOME=/root,USER=root,PATH=/usr/local/cuda/bin:/usr/local/tensorrt/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,LD_LIBRARY_PATH=/usr/local/cuda/lib64:/usr/local/tensorrt/lib:/usr/lib/x86_64-linux-gnu
directory=/workspace/hngpt
autostart=false
autorestart=false
startretries=1
startsecs=0
exitcodes=0
user=root
priority=1

[program:app]
command=python /workspace/hngpt/app.py
directory=/workspace/hngpt
environment=HOME=/root,USER=root,PATH=/usr/local/cuda/bin:/usr/local/tensorrt/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,PYTHONUNBUFFERED=1
autostart=true
autorestart=true
startretries=10
user=root
priority=100
stdout_logfile=/var/log/supervisor/app.log
stderr_logfile=/var/log/supervisor/app.err

[program:llm0]
command=/usr/bin/ollama serve
environment=HOME=/root,USER=root,PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,CUDA_VISIBLE_DEVICES="0",OLLAMA_KEEP_ALIVE=-1,OLLAMA_HOST=127.0.0.1:11434
autostart=true
autorestart=true
startretries=10
user=root
priority=100

[program:llm1]
command=/usr/bin/ollama serve
environment=HOME=/root,USER=root,PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,CUDA_VISIBLE_DEVICES="1",OLLAMA_KEEP_ALIVE=-1,OLLAMA_HOST=127.0.0.1:11433
autostart=true
autorestart=true
startretries=10
user=root
priority=100

[program:llm2]
command=/usr/bin/ollama serve
environment=HOME=/root,USER=root,PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,CUDA_VISIBLE_DEVICES="2",OLLAMA_KEEP_ALIVE=-1,OLLAMA_HOST=127.0.0.1:11432
autostart=true
autorestart=true
startretries=10
user=root
priority=100

[program:llm3]
command=/usr/bin/ollama serve
environment=HOME=/root,USER=root,PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin,CUDA_VISIBLE_DEVICES="3",OLLAMA_KEEP_ALIVE=-1,OLLAMA_HOST=127.0.0.1:11431
autostart=true
autorestart=true
startretries=10
user=root
priority=100