#!/usr/bin/env python3
"""
测试 rec 模块是否支持通过 version 和 gpu_id 参数获得 OCR 实例
"""
import sys
import os
from pathlib import Path

# 设置环境变量
os.environ.setdefault('OCR_VERSION', 'v5')
os.environ.setdefault('OCR_GPU_ID', '0')

# 设置路径
_dir = Path(__file__).parent
sys.path.insert(0, str(_dir))
sys.path.insert(0, str(_dir / 'cpp' / 'build'))

def test_rec_module():
    """测试 rec 模块的功能"""
    try:
        import rec
        print("✓ rec 模块导入成功")
        
        # 列出所有可用的函数
        print("\nrec 模块可用函数:")
        for attr in dir(rec):
            if not attr.startswith('_'):
                print(f"  - {attr}")
        
        # 测试获取版本和GPU ID
        try:
            version = rec.get_version()
            gpu_id = rec.get_gpu_id()
            print(f"\n默认配置: version={version}, gpu_id={gpu_id}")
        except Exception as e:
            print(f"✗ 获取默认配置失败: {e}")
        
        # 测试获取指定实例
        try:
            instance_available = rec.get_instance(0, 'v5')
            print(f"实例可用性 (GPU 0, v5): {instance_available}")
        except Exception as e:
            print(f"✗ 获取实例失败: {e}")
        
        # 测试是否有带参数的函数
        functions_to_check = [
            'rapid_ocr_with_params',
            'rapid_ocrm_with_params',
            'rapid_ocr',
            'rapid_ocrm',
            'detect_only'
        ]
        
        print("\n函数可用性检查:")
        for func_name in functions_to_check:
            if hasattr(rec, func_name):
                print(f"  ✓ {func_name} 可用")
            else:
                print(f"  ✗ {func_name} 不可用")
        
        return True
        
    except ImportError as e:
        print(f"✗ rec 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_ocr_with_params():
    """测试使用指定参数的 OCR 功能"""
    try:
        import rec
        import numpy as np
        
        # 创建一个简单的测试图像
        test_img = np.ones((100, 300, 3), dtype=np.uint8) * 255
        
        # 测试带参数的 OCR 函数
        if hasattr(rec, 'rapid_ocr_with_params'):
            print("\n测试 rapid_ocr_with_params:")
            try:
                result = rec.rapid_ocr_with_params(test_img, 0, 'v5')
                print(f"  ✓ 调用成功，结果: {result}")
                return True
            except Exception as e:
                print(f"  ✗ 调用失败: {e}")
                return False
        else:
            print("\n✗ rapid_ocr_with_params 函数不存在")
            return False
            
    except Exception as e:
        print(f"✗ OCR 参数测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== 测试 rec 模块参数支持 ===")
    
    # 测试模块基本功能
    if test_rec_module():
        print("\n=== 基本功能测试通过 ===")
        
        # 测试带参数的 OCR 功能
        if test_ocr_with_params():
            print("\n=== 参数化 OCR 测试通过 ===")
            print("\n✓ 结论: test.py 可以通过传递 version 和 gpu_id 获得 OCR 实例")
        else:
            print("\n=== 参数化 OCR 测试失败 ===")
            print("\n✗ 结论: test.py 无法通过传递 version 和 gpu_id 获得 OCR 实例")
    else:
        print("\n=== 基本功能测试失败 ===")
        print("\n✗ 结论: rec 模块存在问题")
