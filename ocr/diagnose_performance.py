#!/usr/bin/env python3
"""
OCR性能诊断工具
分析OCR处理时间的各个环节，找出性能瓶颈
"""

import sys
from pathlib import Path
import os
import cv2
import numpy as np
import time
import statistics

# 路径设置
_dir = Path(__file__).parent
sys.path.insert(0, str(_dir))
sys.path.insert(0, str(_dir / 'cpp' / 'build'))
project_root = _dir.parent
sys.path.append(str(project_root))

import rec

def create_test_image(size=(640, 480)):
    """创建标准测试图像"""
    img = np.ones((size[1], size[0], 3), dtype=np.uint8) * 255
    
    # 添加一些文本
    texts = [
        "Performance Test Image",
        "测试图像性能",
        "1234567890",
        "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    ]
    
    for i, text in enumerate(texts):
        y = 50 + i * 60
        cv2.putText(img, text, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
    
    return img

def diagnose_ocr_performance(version="v5", gpu_id=0, num_tests=10):
    """诊断OCR性能各个环节"""
    print(f"🔍 诊断OCR性能 (version={version}, gpu_id={gpu_id})")
    print("=" * 60)
    
    # 创建测试图像
    test_img = create_test_image()
    print(f"测试图像尺寸: {test_img.shape}")
    
    # 1. 测试实例获取时间
    print("\n📦 测试实例获取...")
    instance_times = []
    for i in range(5):
        start_time = time.time()
        try:
            success = rec.MultiGPUOCR.initInstance(gpu_id, version)
            end_time = time.time()
            instance_times.append(end_time - start_time)
            print(f"  第{i+1}次实例获取: {end_time - start_time:.4f}s, 成功: {success}")
        except Exception as e:
            print(f"  第{i+1}次实例获取失败: {e}")
    
    if instance_times:
        avg_instance_time = statistics.mean(instance_times)
        print(f"  平均实例获取时间: {avg_instance_time:.4f}s")
    
    # 2. 测试首次调用 vs 后续调用
    print(f"\n🚀 测试首次调用 vs 后续调用...")
    
    # 首次调用
    print("  首次调用...")
    start_time = time.time()
    try:
        result1 = rec.rapid_ocr_with_params(test_img, gpu_id, version)
        first_call_time = time.time() - start_time
        print(f"  首次调用时间: {first_call_time:.4f}s")
        print(f"  首次调用结果数量: {len(result1) if result1 else 0}")
    except Exception as e:
        print(f"  首次调用失败: {e}")
        return
    
    # 后续调用
    subsequent_times = []
    for i in range(num_tests):
        start_time = time.time()
        try:
            result = rec.rapid_ocr_with_params(test_img, gpu_id, version)
            call_time = time.time() - start_time
            subsequent_times.append(call_time)
            if i < 3:  # 只显示前3次
                print(f"  第{i+1}次后续调用: {call_time:.4f}s")
        except Exception as e:
            print(f"  第{i+1}次后续调用失败: {e}")
    
    if subsequent_times:
        avg_subsequent_time = statistics.mean(subsequent_times)
        min_time = min(subsequent_times)
        max_time = max(subsequent_times)
        std_dev = statistics.stdev(subsequent_times) if len(subsequent_times) > 1 else 0
        
        print(f"  后续调用统计 ({len(subsequent_times)}次):")
        print(f"    平均时间: {avg_subsequent_time:.4f}s")
        print(f"    最短时间: {min_time:.4f}s")
        print(f"    最长时间: {max_time:.4f}s")
        print(f"    标准差: {std_dev:.4f}s")
        
        # 分析首次调用 vs 后续调用的差异
        if 'first_call_time' in locals():
            speedup = first_call_time / avg_subsequent_time
            print(f"  首次调用 vs 后续调用:")
            print(f"    首次: {first_call_time:.4f}s")
            print(f"    后续平均: {avg_subsequent_time:.4f}s")
            print(f"    加速比: {speedup:.2f}x")
    
    # 3. 测试不同图像尺寸的影响
    print(f"\n📏 测试不同图像尺寸的影响...")
    sizes = [(320, 240), (640, 480), (960, 960), (1280, 720)]
    
    for size in sizes:
        test_img_sized = create_test_image(size)
        times = []
        
        for i in range(3):
            start_time = time.time()
            try:
                result = rec.rapid_ocr_with_params(test_img_sized, gpu_id, version)
                call_time = time.time() - start_time
                times.append(call_time)
            except Exception as e:
                print(f"    尺寸 {size} 测试失败: {e}")
                break
        
        if times:
            avg_time = statistics.mean(times)
            print(f"  尺寸 {size[0]}x{size[1]}: {avg_time:.4f}s")
    
    # 4. 内存使用分析
    print(f"\n💾 GPU内存使用分析...")
    try:
        import torch
        if torch.cuda.is_available():
            torch.cuda.set_device(gpu_id)
            memory_allocated = torch.cuda.memory_allocated(gpu_id) / 1024**2  # MB
            memory_reserved = torch.cuda.memory_reserved(gpu_id) / 1024**2   # MB
            print(f"  GPU {gpu_id} 内存使用:")
            print(f"    已分配: {memory_allocated:.1f} MB")
            print(f"    已保留: {memory_reserved:.1f} MB")
        else:
            print("  CUDA不可用，无法检查GPU内存")
    except Exception as e:
        print(f"  内存检查失败: {e}")
    
    return {
        'first_call_time': first_call_time if 'first_call_time' in locals() else None,
        'avg_subsequent_time': avg_subsequent_time if 'subsequent_times' else None,
        'min_time': min_time if 'subsequent_times' else None,
        'max_time': max_time if 'subsequent_times' else None,
        'std_dev': std_dev if 'subsequent_times' else None,
        'subsequent_times': subsequent_times if 'subsequent_times' else []
    }

def compare_gpu_performance():
    """比较不同GPU的性能"""
    print(f"\n⚖️ GPU性能对比")
    print("=" * 60)
    
    gpu_results = {}
    
    for gpu_id in [0, 1]:
        print(f"\n🔍 测试GPU {gpu_id}...")
        try:
            result = diagnose_ocr_performance("v5", gpu_id, 5)
            gpu_results[gpu_id] = result
        except Exception as e:
            print(f"GPU {gpu_id} 测试失败: {e}")
    
    # 对比结果
    if len(gpu_results) >= 2:
        print(f"\n📊 GPU性能对比:")
        for gpu_id, result in gpu_results.items():
            if result and result['avg_subsequent_time']:
                print(f"  GPU {gpu_id}: {result['avg_subsequent_time']:.4f}s")
        
        # 找出性能差异原因
        gpu0_time = gpu_results.get(0, {}).get('avg_subsequent_time')
        gpu1_time = gpu_results.get(1, {}).get('avg_subsequent_time')
        
        if gpu0_time and gpu1_time:
            ratio = gpu1_time / gpu0_time
            print(f"  GPU1 vs GPU0 性能比: {ratio:.2f}x")
            if ratio > 1.5:
                print(f"  ⚠️ GPU1明显慢于GPU0，可能存在:")
                print(f"    - 资源竞争")
                print(f"    - 内存碎片化")
                print(f"    - 模型加载问题")

def main():
    """主函数"""
    print("🔍 OCR性能诊断工具")
    print("=" * 60)
    
    try:
        # 单GPU详细诊断
        result = diagnose_ocr_performance("v5", 0, 10)
        
        # GPU对比
        compare_gpu_performance()
        
        # 总结和建议
        print(f"\n💡 优化建议:")
        if result and result['avg_subsequent_time']:
            if result['avg_subsequent_time'] > 0.2:
                print("  🔧 性能偏慢，建议:")
                print("    1. 检查TensorRT引擎是否正确复用")
                print("    2. 考虑使用FP16精度")
                print("    3. 优化图像预处理流程")
                print("    4. 检查GPU内存使用情况")
            elif result['avg_subsequent_time'] > 0.1:
                print("  ✅ 性能良好，可进一步优化:")
                print("    1. 考虑批处理优化")
                print("    2. 优化内存分配策略")
            else:
                print("  🚀 性能优秀！")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if hasattr(rec, 'MultiGPUOCR'):
                rec.MultiGPUOCR.finalizeAll()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理警告: {e}")

if __name__ == "__main__":
    main()
