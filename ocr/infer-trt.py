import os
import cv2
import math
import copy
import numpy as np
import tensorrt as trt
import pycuda.autoinit  # noqa: F401  (creates a CUDA context)
import pycuda.driver as cuda
import pyclipper
from shapely.geometry import Polygon
from pathlib import Path

# Reuse the existing nice visualization
try:
    from infer import draw_ocr_box_txt  # type: ignore
except Exception:
    draw_ocr_box_txt = None


# -------- TensorRT minimal runner --------
class TrtModel:
    def __init__(self, engine_path: str, logger_severity: int = trt.Logger.INFO):
        self.engine_path = engine_path
        self.logger = trt.Logger(logger_severity)
        with open(engine_path, 'rb') as f:
            runtime = trt.Runtime(self.logger)
            self.engine = runtime.deserialize_cuda_engine(f.read())
        self.context = self.engine.create_execution_context()
        # Map name->index
        self.bind_idx = {self.engine.get_binding_name(i): i for i in range(self.engine.num_bindings)}
        # First (and only) input index
        self.input_index = next(i for i in range(self.engine.num_bindings) if self.engine.binding_is_input(i))

    def infer(self, input_np: np.ndarray) -> list:
        # Ensure contiguous float32
        if not input_np.flags['C_CONTIGUOUS']:
            input_np = np.ascontiguousarray(input_np)
        # Dynamic shapes
        self.context.set_binding_shape(self.input_index, tuple(input_np.shape))

        # Allocate device/host for input
        d_inputs = []
        d_outputs = []
        host_outputs = []
        bindings = [None] * self.engine.num_bindings

        # Input
        d_in = cuda.mem_alloc(input_np.nbytes)
        cuda.memcpy_htod(d_in, input_np)
        bindings[self.input_index] = int(d_in)
        d_inputs.append(d_in)

        # Outputs
        for i in range(self.engine.num_bindings):
            if self.engine.binding_is_input(i):
                continue
            out_shape = tuple(self.context.get_binding_shape(i))
            out_dtype = trt.nptype(self.engine.get_binding_dtype(i))
            size = int(np.prod(out_shape))
            host_out = cuda.pagelocked_empty(size, out_dtype)
            d_out = cuda.mem_alloc(host_out.nbytes)
            bindings[i] = int(d_out)
            d_outputs.append(d_out)
            host_outputs.append((host_out, out_shape))

        # Execute
        self.context.execute_v2(bindings)

        # Copy back
        outputs = []
        for (host_out, out_shape), d_out in zip(host_outputs, d_outputs):
            cuda.memcpy_dtoh(host_out, d_out)
            outputs.append(host_out.reshape(out_shape))

        # Free device mem (host pagelocked arrays will be GC'd)
        for d in d_inputs + d_outputs:
            d.free()
        return outputs


# -------- Detector (DB) using TRT --------
class TextDetectorTRT:
    def __init__(self, engine_path: str, mask_thresh=0.3, box_thresh=0.6, max_candidates=1000, min_size=3, unclip_ratio=1.5):
        self.trt = TrtModel(engine_path)
        self.mask_thresh = mask_thresh
        self.box_thresh = box_thresh
        self.max_candidates = max_candidates
        self.min_size = min_size
        self.unclip_ratio = unclip_ratio

    def preprocess(self, img, tar_w=960, tar_h=960):
        img = cv2.resize(img, (int(tar_w), int(tar_h)))
        img = img.astype('float32') / 255.0
        mean = np.array([0.485, 0.456, 0.406], dtype=np.float32).reshape(1, 1, 3)
        std = np.array([0.229, 0.224, 0.225], dtype=np.float32).reshape(1, 1, 3)
        img = (img - mean) / std
        img = img.transpose(2, 0, 1)[None]
        return img

    def forward(self, input_np):
        # Expect output [1,1,960,960]
        return self.trt.infer(input_np)[0]

    def postprocess(self, pred, src_h, src_w):
        pred = pred[0, 0, :, :]
        mask = pred > self.mask_thresh
        boxes, _ = self._boxes_from_bitmap(pred, mask, src_w, src_h)
        boxes = self._filter_boxes(boxes, src_h, src_w)
        boxes = self._sorted_boxes(boxes)
        return boxes

    # ---- helpers copied from infer.py to ensure parity ----
    def _boxes_from_bitmap(self, pred, bitmap, dest_width, dest_height):
        height, width = bitmap.shape
        outs = cv2.findContours((bitmap * 255).astype(np.uint8), cv2.RETR_LIST, cv2.CHAIN_APPROX_SIMPLE)
        if len(outs) == 3:
            _, contours, _ = outs
        else:
            contours, _ = outs
        num_contours = min(len(contours), self.max_candidates)
        boxes, scores = [], []
        for idx in range(num_contours):
            contour = contours[idx]
            points, sside = self._get_mini_boxes(contour)
            if sside < self.min_size:
                continue
            points = np.array(points)
            score = self._box_score(pred, points.reshape(-1, 2))
            if score < self.box_thresh:
                continue
            box = self._unclip(points, self.unclip_ratio)
            if len(box) > 1:
                continue
            box = np.array(box).reshape(-1, 1, 2)
            box, sside = self._get_mini_boxes(box)
            if sside < self.min_size + 2:
                continue
            box = np.array(box)
            box[:, 0] = np.clip(np.round(box[:, 0] / width * dest_width), 0, dest_width)
            box[:, 1] = np.clip(np.round(box[:, 1] / height * dest_height), 0, dest_height)
            boxes.append(box.astype('int32'))
            scores.append(score)
        return np.array(boxes, dtype='int32'), scores

    def _get_mini_boxes(self, contour):
        rect = cv2.minAreaRect(contour)
        pts = sorted(list(cv2.boxPoints(rect)), key=lambda x: x[0])
        i1, i2, i3, i4 = 0, 1, 2, 3
        if pts[1][1] > pts[0][1]:
            i1, i4 = 0, 1
        else:
            i1, i4 = 1, 0
        if pts[3][1] > pts[2][1]:
            i2, i3 = 2, 3
        else:
            i2, i3 = 3, 2
        box = [pts[i1], pts[i2], pts[i3], pts[i4]]
        return box, min(rect[1])

    def _box_score(self, bitmap, _box):
        h, w = bitmap.shape[:2]
        box = _box.copy()
        xmin = np.clip(np.floor(box[:, 0].min()).astype('int32'), 0, w - 1)
        xmax = np.clip(np.ceil(box[:, 0].max()).astype('int32'), 0, w - 1)
        ymin = np.clip(np.floor(box[:, 1].min()).astype('int32'), 0, h - 1)
        ymax = np.clip(np.ceil(box[:, 1].max()).astype('int32'), 0, h - 1)
        mask = np.zeros((ymax - ymin + 1, xmax - xmin + 1), dtype=np.uint8)
        box[:, 0] -= xmin
        box[:, 1] -= ymin
        cv2.fillPoly(mask, box.reshape(1, -1, 2).astype('int32'), 1)
        return cv2.mean(bitmap[ymin: ymax + 1, xmin: xmax + 1], mask)[0]

    def _unclip(self, box, unclip_ratio):
        poly = Polygon(box)
        distance = poly.area * unclip_ratio / poly.length
        offset = pyclipper.PyclipperOffset()
        offset.AddPath(box, pyclipper.JT_ROUND, pyclipper.ET_CLOSEDPOLYGON)
        expanded = offset.Execute(distance)
        return expanded

    def _filter_boxes(self, boxes, src_h, src_w):
        boxes_filter = []
        for box in boxes:
            box = self._order_points_clockwise(box)
            box = self._clip(box, src_h, src_w)
            rect_w = int(np.linalg.norm(box[0] - box[1]))
            rect_h = int(np.linalg.norm(box[0] - box[3]))
            if rect_w <= 3 or rect_h <= 3:
                continue
            boxes_filter.append(box)
        return np.array(boxes_filter)

    def _order_points_clockwise(self, pts):
        rect = np.zeros((4, 2), dtype='float32')
        s = pts.sum(axis=1)
        rect[0] = pts[np.argmin(s)]
        rect[2] = pts[np.argmax(s)]
        tmp = np.delete(pts, (np.argmin(s), np.argmax(s)), axis=0)
        diff = np.diff(np.array(tmp), axis=1)
        rect[1] = tmp[np.argmin(diff)]
        rect[3] = tmp[np.argmax(diff)]
        return rect

    def _clip(self, points, img_h, img_w):
        for i in range(points.shape[0]):
            points[i, 0] = int(min(max(points[i, 0], 0), img_w - 1))
            points[i, 1] = int(min(max(points[i, 1], 0), img_h - 1))
        return points

    def _sorted_boxes(self, boxes):
        num_boxes = boxes.shape[0]
        boxes_sorted = sorted(boxes, key=lambda x: (x[0][1], x[0][0]))
        _boxes = list(boxes_sorted)
        for i in range(num_boxes - 1):
            for j in range(i, -1, -1):
                if abs(_boxes[j + 1][0][1] - _boxes[j][0][1]) < 10 and (_boxes[j + 1][0][0] < _boxes[j][0][0]):
                    _boxes[j], _boxes[j + 1] = _boxes[j + 1], _boxes[j]
                else:
                    break
        return _boxes


# -------- Classifier (angle) using TRT --------
class TextClassifierTRT:
    def __init__(self, engine_path: str, cls_thresh=0.9, cls_batch_num=6):
        self.trt = TrtModel(engine_path)
        self.cls_thresh = cls_thresh
        self.cls_batch_num = cls_batch_num

    def preprocess(self, img, boxes, tar_w=192, tar_h=48):
        img_crop_list = []
        for box in boxes:
            tmp_box = copy.deepcopy(box)
            img_crop = self._get_rotate_crop_image(img, tmp_box)
            img_crop_list.append(img_crop)
        img_num = len(img_crop_list)
        ratio_list = [im.shape[1] / float(im.shape[0]) for im in img_crop_list]
        indices = np.argsort(np.array(ratio_list))
        imgs_pre_batch = []
        for beg in range(0, img_num, self.cls_batch_num):
            end = min(img_num, beg + self.cls_batch_num)
            norm_img_batch = []
            for idx in range(beg, end):
                norm_img = self._resize_norm_img(img_crop_list[indices[idx]], tar_w, tar_h)[None]
                norm_img_batch.append(norm_img)
            if norm_img_batch:
                imgs_pre_batch.append(np.concatenate(norm_img_batch))
        return img_crop_list, imgs_pre_batch, indices

    def forward(self, inputs):
        return self.trt.infer(inputs)[0]

    def postprocess(self, img_list, imgs_pre_batch, indices):
        cls_res = [["", 0.0]] * len(img_list)
        for b, batch in enumerate(imgs_pre_batch):
            cls_pred = self.forward(batch)  # bx2
            pred_idxs = cls_pred.argmax(axis=1)
            label_list = ["0", "180"]
            cls_result = [(label_list[idx], cls_pred[i, idx]) for i, idx in enumerate(pred_idxs)]
            for i in range(len(cls_result)):
                label, score = cls_result[i]
                cls_res[indices[b * self.cls_batch_num + i]] = [label, float(score)]
                if "180" in label and score > self.cls_thresh:
                    img_list[indices[b * self.cls_batch_num + i]] = cv2.rotate(img_list[indices[b * self.cls_batch_num + i]], 1)
        return img_list, cls_res

    def _get_rotate_crop_image(self, img, points):
        img_crop_width = int(max(np.linalg.norm(points[0] - points[1]), np.linalg.norm(points[2] - points[3])))
        img_crop_height = int(max(np.linalg.norm(points[0] - points[3]), np.linalg.norm(points[1] - points[2])))
        pts_std = np.float32([[0, 0], [img_crop_width, 0], [img_crop_width, img_crop_height], [0, img_crop_height]])
        M = cv2.getPerspectiveTransform(points.astype(np.float32), pts_std)
        dst_img = cv2.warpPerspective(img, M, (img_crop_width, img_crop_height), borderMode=cv2.BORDER_REPLICATE, flags=cv2.INTER_CUBIC)
        h, w = dst_img.shape[:2]
        if (h * 1.0 / w) >= 1.5:
            dst_img = np.rot90(dst_img)
        return dst_img

    def _resize_norm_img(self, img, dest_w, dest_h):
        h, w, _ = img.shape
        ratio = w / float(h)
        resized_w = dest_w if math.ceil(dest_h * ratio) > dest_w else int(math.ceil(dest_h * ratio))
        resized = cv2.resize(img, (resized_w, dest_h))
        resized = resized.astype('float32').transpose(2, 0, 1) / 255.0
        resized -= 0.5
        resized /= 0.5
        padding = np.zeros((3, dest_h, dest_w), dtype=np.float32)
        padding[:, :, 0:resized_w] = resized
        return padding


# -------- Recognizer using TRT --------
class TextRecognizerTRT:
    def __init__(self, engine_path: str, character_dict_path: str, rec_batch_num=6):
        self.trt = TrtModel(engine_path)
        self.rec_batch_num = rec_batch_num
        self.character_str = []
        with open(character_dict_path, 'rb') as fin:
            for line in fin:
                line = line.decode('utf-8').strip('\n').strip('\r\n')
                self.character_str.append(line)
        self.character_str.append(' ')
        self.character_str = ['blank'] + self.character_str

    def preprocess(self, img_list, tar_w=640, tar_h=48):
        img_num = len(img_list)
        ratio_list = [im.shape[1] / float(im.shape[0]) for im in img_list]
        indices = np.argsort(np.array(ratio_list))
        imgs_pre_batch = []
        for beg in range(0, img_num, self.rec_batch_num):
            end = min(img_num, beg + self.rec_batch_num)
            norm_img_batch = []
            for idx in range(beg, end):
                norm_img = self._resize_norm_img(img_list[indices[idx]], tar_w, tar_h)[None]
                norm_img_batch.append(norm_img)
            imgs_pre_batch.append(np.concatenate(norm_img_batch))
        return imgs_pre_batch, indices

    def forward(self, inputs):
        return self.trt.infer(inputs)[0]

    def postprocess(self, imgs_pre_batch, indices):
        rec_res = [["", 0.0]] * len(indices)
        for b, batch in enumerate(imgs_pre_batch):
            pred = self.forward(batch)  # bx80xC
            preds_idx = pred.argmax(axis=2)
            preds_prob = pred.max(axis=2)
            text = self._decode(preds_idx, preds_prob)
            for i in range(len(text)):
                rec_res[indices[b * self.rec_batch_num + i]] = text[i]
        return rec_res

    def _resize_norm_img(self, img, dest_w, dest_h):
        h, w, _ = img.shape
        ratio = w / float(h)
        resized_w = dest_w if math.ceil(dest_h * ratio) > dest_w else int(math.ceil(dest_h * ratio))
        resized = cv2.resize(img, (resized_w, dest_h))
        resized = resized.astype('float32').transpose(2, 0, 1) / 255.0
        resized -= 0.5
        resized /= 0.5
        padding = np.zeros((3, dest_h, dest_w), dtype=np.float32)
        padding[:, :, 0:resized_w] = resized
        return padding

    def _decode(self, text_index, text_prob):
        result_list = []
        batch_size = len(text_index)
        for b in range(batch_size):
            sel = np.ones(len(text_index[0]), dtype=bool)
            sel[1:] = text_index[b][1:] != text_index[b][:-1]
            sel &= text_index[b] != 0
            chars = [self.character_str[idx] for idx in text_index[b][sel]]
            conf = text_prob[b][sel]
            if len(conf) == 0:
                conf = 0
            text = ''.join(chars)
            result_list.append((text, float(np.mean(conf))))
        return result_list


def main():
    # Paths
    models_dir = Path(os.environ.get('OCR_MODELS_DIR', '/workspace/hngpt/models'))
    img_path = os.environ.get('OCR_IMAGE', '/workspace/hngpt/ocr/images/receipt.jpg')
    out_path = os.environ.get('OCR_OUT', 'resultv5.png')

    det_engine = str(models_dir / 'v5.det.trt')
    cls_engine = str(models_dir / 'v5.cls.trt')
    rec_engine = str(models_dir / 'v5.rec.trt')
    char_dict = str(models_dir / 'ppocrv5.txt')

    image = cv2.imread(img_path)
    if image is None:
        raise FileNotFoundError(img_path)
    src_h, src_w = image.shape[:2]

    # 1) Detection
    det = TextDetectorTRT(det_engine)
    det_in = det.preprocess(image)
    det_pred = det.forward(det_in)
    det_boxes = det.postprocess(det_pred, src_h, src_w)
    if det_boxes is None or len(det_boxes) == 0:
        print('warning, no det_boxes found')
        return
    print(f'det_boxes num: {len(det_boxes)}')

    # 2) Angle classification
    cls = TextClassifierTRT(cls_engine, cls_batch_num=1)
    img_list, imgs_pre_batch, indices = cls.preprocess(image, det_boxes)
    img_list, _ = cls.postprocess(img_list, imgs_pre_batch, indices)

    # 3) Recognition
    rec = TextRecognizerTRT(rec_engine, char_dict, rec_batch_num=1)
    rec_batches, rec_indices = rec.preprocess(img_list)
    rec_txts = rec.postprocess(rec_batches, rec_indices)

    # 4) Visualization
    txts = [rec_txts[i][0] for i in range(len(rec_txts))]
    scores = [rec_txts[i][1] for i in range(len(rec_txts))]

    if draw_ocr_box_txt is None:
        # Fallback: simple left-only overlay
        img = image.copy()
        for i, box in enumerate(det_boxes):
            color = (0, 255, 0)
            pts = np.array(box, np.int32).reshape((-1, 1, 2))
            cv2.polylines(img, [pts], True, color, 2)
            cv2.putText(img, txts[i], (int(box[0][0]), int(box[0][1]-3)), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,0,255), 1)
        cv2.imwrite(out_path, img)
    else:
        draw_img = draw_ocr_box_txt(image, det_boxes, txts, scores, font_path=str(models_dir / 'simfang.ttf'))
        cv2.imwrite(out_path, draw_img[:, :, ::-1])

    print(f'Saved: {out_path}')


if __name__ == '__main__':
    main()

