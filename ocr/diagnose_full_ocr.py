#!/usr/bin/env python3
"""
完整OCR流程性能诊断
分析检测和识别两个阶段的性能
"""

import sys
from pathlib import Path
import os
import cv2
import numpy as np
import time
import statistics

# 路径设置
_dir = Path(__file__).parent
sys.path.insert(0, str(_dir))
sys.path.insert(0, str(_dir / 'cpp' / 'build'))
project_root = _dir.parent
sys.path.append(str(project_root))

import rec

def test_with_real_image():
    """使用真实图像测试完整OCR流程"""
    print("🔍 使用真实图像测试完整OCR流程")
    print("=" * 60)
    
    # 使用真实的receipt图像
    img_path = '/workspace/hngpt/ocr/images/receipt.jpg'
    
    if os.path.exists(img_path):
        img = cv2.imread(img_path)
        print(f"✅ 加载真实图像: {img_path}")
        print(f"   图像尺寸: {img.shape}")
    else:
        # 创建一个复杂的测试图像
        img = np.ones((800, 600, 3), dtype=np.uint8) * 255
        
        # 添加多行文本模拟复杂场景
        texts = [
            "Receipt #12345",
            "Store: ABC Market", 
            "Date: 2024-01-15",
            "Item 1: Apple      $2.50",
            "Item 2: Banana     $1.20", 
            "Item 3: Orange     $3.00",
            "Subtotal:          $6.70",
            "Tax:               $0.67",
            "Total:             $7.37",
            "Thank you for shopping!"
        ]
        
        for i, text in enumerate(texts):
            y = 50 + i * 60
            cv2.putText(img, text, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
        
        print(f"✅ 创建复杂测试图像")
        print(f"   图像尺寸: {img.shape}")
        print(f"   文本行数: {len(texts)}")
    
    # 测试完整OCR流程
    print(f"\n🚀 测试完整OCR流程...")
    
    times = []
    results = []
    
    for i in range(10):
        start_time = time.time()
        result = rec.rapid_ocr_with_params(img, 0, "v5")
        end_time = time.time()
        
        processing_time = end_time - start_time
        times.append(processing_time)
        results.append(len(result) if result else 0)
        
        print(f"  第{i+1}次: {processing_time:.4f}s, 检测到{len(result) if result else 0}个文本区域")
    
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        avg_regions = statistics.mean(results)
        
        print(f"\n📊 完整OCR性能统计:")
        print(f"  平均处理时间: {avg_time:.4f}s")
        print(f"  最短时间: {min_time:.4f}s")
        print(f"  最长时间: {max_time:.4f}s")
        print(f"  平均检测区域: {avg_regions:.1f}个")
        
        # 分析性能
        if avg_time > 0.2:
            print(f"  ⚠️ 性能偏慢，可能原因:")
            print(f"    - 文本区域过多需要逐个识别")
            print(f"    - 图像预处理开销")
            print(f"    - 识别模型性能瓶颈")
        elif avg_time > 0.1:
            print(f"  ✅ 性能良好")
        else:
            print(f"  🚀 性能优秀")
    
    return times, results

def compare_detection_vs_full_ocr():
    """对比纯检测 vs 完整OCR的性能"""
    print(f"\n🔍 对比纯检测 vs 完整OCR性能")
    print("=" * 60)
    
    # 创建测试图像
    img = np.ones((600, 800, 3), dtype=np.uint8) * 255
    texts = [
        "Performance Test Line 1",
        "Performance Test Line 2", 
        "Performance Test Line 3",
        "Performance Test Line 4",
        "Performance Test Line 5"
    ]
    
    for i, text in enumerate(texts):
        y = 80 + i * 80
        cv2.putText(img, text, (50, y), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 0, 0), 2)
    
    print(f"测试图像: {img.shape}, {len(texts)}行文本")
    
    # 测试纯检测（如果有detect_only函数）
    print(f"\n📍 测试纯检测性能...")
    detection_times = []
    
    try:
        for i in range(5):
            start_time = time.time()
            # 假设有detect_only函数，如果没有就跳过
            detection_result = rec.detect_only(img)
            end_time = time.time()
            
            detection_time = end_time - start_time
            detection_times.append(detection_time)
            print(f"  检测第{i+1}次: {detection_time:.4f}s")
    except Exception as e:
        print(f"  ⚠️ 纯检测测试失败: {e}")
        detection_times = []
    
    # 测试完整OCR
    print(f"\n📝 测试完整OCR性能...")
    full_ocr_times = []
    
    for i in range(5):
        start_time = time.time()
        ocr_result = rec.rapid_ocr_with_params(img, 0, "v5")
        end_time = time.time()
        
        ocr_time = end_time - start_time
        full_ocr_times.append(ocr_time)
        print(f"  OCR第{i+1}次: {ocr_time:.4f}s, 结果: {len(ocr_result) if ocr_result else 0}个区域")
    
    # 对比分析
    if detection_times and full_ocr_times:
        avg_detection = statistics.mean(detection_times)
        avg_full_ocr = statistics.mean(full_ocr_times)
        recognition_overhead = avg_full_ocr - avg_detection
        
        print(f"\n📊 性能对比:")
        print(f"  纯检测平均时间: {avg_detection:.4f}s")
        print(f"  完整OCR平均时间: {avg_full_ocr:.4f}s")
        print(f"  识别开销: {recognition_overhead:.4f}s ({recognition_overhead/avg_full_ocr*100:.1f}%)")
        
        if recognition_overhead > avg_detection:
            print(f"  🔍 识别阶段是主要瓶颈")
        else:
            print(f"  🔍 检测阶段是主要瓶颈")
    
    elif full_ocr_times:
        avg_full_ocr = statistics.mean(full_ocr_times)
        print(f"\n📊 完整OCR平均时间: {avg_full_ocr:.4f}s")

def analyze_text_region_impact():
    """分析文本区域数量对性能的影响"""
    print(f"\n🔍 分析文本区域数量对性能的影响")
    print("=" * 60)
    
    region_counts = [1, 3, 5, 10, 15]
    
    for count in region_counts:
        # 创建包含指定数量文本的图像
        img = np.ones((max(400, count * 50), 600, 3), dtype=np.uint8) * 255
        
        for i in range(count):
            y = 40 + i * 40
            text = f"Text Region {i+1:02d}"
            cv2.putText(img, text, (20, y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        
        # 测试性能
        times = []
        for _ in range(3):
            start_time = time.time()
            result = rec.rapid_ocr_with_params(img, 0, "v5")
            end_time = time.time()
            times.append(end_time - start_time)
        
        avg_time = statistics.mean(times)
        detected_regions = len(result) if result else 0
        
        print(f"  {count:2d}个文本区域 → {avg_time:.4f}s (检测到{detected_regions}个)")

def main():
    """主函数"""
    print("🔍 完整OCR流程性能诊断")
    print("=" * 60)
    
    try:
        # 1. 使用真实图像测试
        real_times, real_results = test_with_real_image()
        
        # 2. 对比检测vs完整OCR
        compare_detection_vs_full_ocr()
        
        # 3. 分析文本区域数量影响
        analyze_text_region_impact()
        
        # 4. 总结和建议
        if real_times:
            avg_real_time = statistics.mean(real_times)
            print(f"\n💡 性能分析总结:")
            print(f"  真实图像平均处理时间: {avg_real_time:.4f}s")
            
            if avg_real_time > 0.3:
                print(f"  🔧 性能优化建议:")
                print(f"    1. 检查是否有大量文本区域需要识别")
                print(f"    2. 考虑使用批处理识别")
                print(f"    3. 优化图像预处理流程")
                print(f"    4. 检查TensorRT引擎配置")
            elif avg_real_time > 0.1:
                print(f"  ✅ 性能良好，可考虑进一步优化")
            else:
                print(f"  🚀 性能优秀！")
        
    except Exception as e:
        print(f"❌ 诊断过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理资源
        try:
            if hasattr(rec, 'MultiGPUOCR'):
                rec.MultiGPUOCR.finalizeAll()
            print("✅ 资源清理完成")
        except Exception as e:
            print(f"⚠️ 资源清理警告: {e}")

if __name__ == "__main__":
    main()
