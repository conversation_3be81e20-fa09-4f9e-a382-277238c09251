import cv2
import imutils
import numpy as np
from imutils.perspective import four_point_transform
import rec
import time

def perform_ocr(img: np.ndarray):
    start_time = time.time()
    
    img_orig = img.copy()
    resize_time = time.time()
    image = imutils.resize(img_orig, width=500)
    print(f"Resize time: {time.time() - resize_time:.3f}s")
    ratio = img_orig.shape[1] / float(image.shape[1])

    # convert the image to grayscale, blur it slightly, and then apply
    # edge detection
    preprocess_time = time.time()
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edged = cv2.Canny(blurred, 75, 200)
    print(f"Preprocessing time: {time.time() - preprocess_time:.3f}s")

    # find contours in the edge map and sort them by size in descending
    # order
    contour_time = time.time()
    cnts = cv2.findContours(edged.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = imutils.grab_contours(cnts)
    cnts = sorted(cnts, key=cv2.contourArea, reverse=True)
    print(f"Contour processing time: {time.time() - contour_time:.3f}s")

    # initialize a contour that corresponds to the receipt outline
    receiptCnt = None
    # loop over the contours
    for c in cnts:
        # approximate the contour
        peri = cv2.arcLength(c, True)
        approx = cv2.approxPolyDP(c, 0.02 * peri, True)
        # if our approximated contour has four points, then we can
        # assume we have found the outline of the receipt
        if len(approx) == 4:
            receiptCnt = approx
            break

    # if the receipt contour is empty then our script could not find the
    # outline and we should be notified
    if receiptCnt is None:
        raise Exception(
            (
                "Could not find receipt outline. "
                "Try debugging your edge detection and contour steps."
            )
        )

    # apply a four-point perspective transform to the *original* image to
    # obtain a top-down bird's-eye view of the receipt
    receipt = four_point_transform(img_orig, receiptCnt.reshape(4, 2) * ratio)

    # apply OCR to the receipt image by assuming column data, ensuring
    # the text is *concatenated across the row* (additionally, for your
    # own images you may need to apply additional processing to cleanup
    # the image, including resizing, thresholding, etc.)
    ocr_time = time.time()
    text = rec.rapid_ocr(cv2.cvtColor(receipt, cv2.COLOR_BGR2RGB))
    print(f"OCR time: {time.time() - ocr_time:.3f}s")

    print(f"Total processing time: {time.time() - start_time:.3f}s")
    return text
