# include "rec.h"

namespace OCR{

int TextRec::Model_Init(string& engine_path){
    // 仅支持直接加载已存在的 .trt 引擎
    std::ifstream f(engine_path, std::ios::binary);
    if (!f.good()) {
        std::cerr << "rec engine not found: " << engine_path << std::endl;
        return -1;
    }
    f.close();

    // 反序列化引擎
    Logger gLogger;
    std::ifstream file(engine_path, std::ios::binary);
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);
    std::vector<char> trtModelStream(size);
    file.read(trtModelStream.data(), size);
    file.close();

    nvinfer1::IRuntime* runtime = nvinfer1::createInferRuntime(gLogger);
    nvinfer1::ICudaEngine* engine = runtime->deserializeCudaEngine(trtModelStream.data(), size);
    if (!engine) { std::cerr << "deserialize rec engine failed" << std::endl; return -2; }
    nvinfer1::IExecutionContext* context = engine->createExecutionContext();
    if (!context) { std::cerr << "create rec context failed" << std::endl; return -2; }

    // 暂存到成员（使用简单持有）
    this->m_runtime = runtime;
    this->m_engine = engine;
    this->m_context = context;

    return 0;
}

void TextRec::Model_Infer(vector<cv::Mat> img_list, vector<pair<string, double> > &rec_res, vector<int>& idx_map){
    // 确保在正确的 GPU 上执行
    cudaSetDevice(gpu_id_);

    int img_num = img_list.size();

    // 检查输入是否为空
    if (img_num == 0) {
        std::cout << "TextRec::Model_Infer: No input images, returning empty results" << std::endl;
        rec_res.clear();
        idx_map.clear();
        return;
    }
    // 真实推理实现开始（支持 batch，复用流与显存；T4 上充分利用 Tensor Core）
    // 绑定索引，优先通过属性枚举，避免名称不一致导致索引为 -1
    int nb = m_engine->getNbBindings();
    int inputIndex = -1, outputIndex = -1;
    for (int i = 0; i < nb; ++i) {
        if (m_engine->bindingIsInput(i)) inputIndex = i; else outputIndex = i;
    }
    if (inputIndex < 0) inputIndex = m_engine->getBindingIndex(INPUT_BLOB_NAME); // 兜底
    if (outputIndex < 0) outputIndex = m_engine->getBindingIndex(OUTPUT_BLOB_NAME);
    if (inputIndex < 0 || outputIndex < 0) {
        std::cerr << "[TextRec] FATAL: failed to locate binding indices. nb=" << nb
                  << ", inputIndex=" << inputIndex << ", outputIndex=" << outputIndex << std::endl;
        return;
    }
    m_context->setOptimizationProfile(0);

    const int height = this->rec_img_h_;
    const int batch_width = this->rec_img_w_;
    const int max_batch = 1; // 固定为 1，简化逻辑

    auto in_dims = m_context->getBindingDimensions(inputIndex);
    in_dims.d[1] = 3; in_dims.d[2] = height; in_dims.d[3] = batch_width;

    // 预估最大输出尺寸：固定 batch=1 查询输出维度
    in_dims.d[0] = 1;
    m_context->setBindingDimensions(inputIndex, in_dims);
    auto out_dims_max = m_context->getBindingDimensions(outputIndex);
    int out_size_max = 1;
    for (int j = 0; j < out_dims_max.nbDims; j++) out_size_max *= out_dims_max.d[j];

    // 类别/字典一致性：infer.py 约定 字典 = [blank] + 文件行 + " "
    int classes = out_dims_max.d[out_dims_max.nbDims - 1];
    auto try_rebuild_labels = [&]() {
        try {
            // 根据版本重新构建字典
            if (version_ == "v5") {
                this->label_list_ = getV5Dict();
            } else {
                this->label_list_ = getV4Dict();
            }
        } catch(...) {}
    };
    if (classes != (int)this->label_list_.size()) {
        std::cerr << "WARN: classes(" << classes << ") != label_list(" << this->label_list_.size() << "), try rebuild" << std::endl;
        try_rebuild_labels();
    }
    if (classes != (int)this->label_list_.size()) {
        std::cerr << "ERROR: rec classes(" << classes << ") != label_list size(" << this->label_list_.size() << ")" << std::endl;
        return;
    }

    // 预分配显存（按最大 batch）
    const int in_size_max = max_batch * 3 * height * batch_width;
    void* d_input = nullptr; void* d_output = nullptr;
    CHECK(cudaMalloc(&d_input, in_size_max * sizeof(float)));
    CHECK(cudaMalloc(&d_output, out_size_max * sizeof(float)));



    cudaStream_t stream; CHECK(cudaStreamCreate(&stream));

    std::vector<float> inBlob(in_size_max);
    std::vector<float> outBlob(out_size_max);

    // 固定 batch=1 的简化实现
    for (int i = 0; i < img_num; ++i) {
        // 预处理单张
        cv::Mat srcimg; img_list[i].copyTo(srcimg);
        cv::Mat resize_img;
        float wh_ratio = (float)std::max(1, srcimg.cols) / (float)std::max(1, srcimg.rows);
        resize_for_recognition(srcimg, resize_img, /*max_wh_ratio*/ wh_ratio, this->rec_image_shape_);
        normalize_image(&resize_img, this->mean_, this->scale_, true);

        // 设置形状为 batch=1
        in_dims.d[0] = 1; m_context->setBindingDimensions(inputIndex, in_dims);
        auto out_dims = m_context->getBindingDimensions(outputIndex);
        int T = out_dims.d[1]; int C = out_dims.d[2];
        int input_size = 1 * 3 * height * batch_width;
        int output_size = 1 * T * C;

        // 主机排布 -> 设备拷贝
        std::vector<cv::Mat> norm_img_batch{resize_img};
        permute_batch_hwc_to_chw(norm_img_batch, inBlob.data());
        // 构造按 binding 索引的 buffers 映射，确保输入/输出指针落在正确的位置
        int nb = m_engine->getNbBindings();
        std::vector<void*> buffers(nb, nullptr);
        buffers[inputIndex]  = d_input;
        buffers[outputIndex] = d_output;

        CHECK(cudaMemcpyAsync(d_input, inBlob.data(), input_size * sizeof(float), cudaMemcpyHostToDevice, stream));

        bool ok = m_context->enqueueV2(buffers.data(), stream, nullptr);
        if (!ok) std::cerr << "TextRec enqueueV2 failed" << std::endl;
        CHECK(cudaMemcpyAsync(outBlob.data(), d_output, output_size * sizeof(float), cudaMemcpyDeviceToHost, stream));
        cudaStreamSynchronize(stream);

        // postprocess 单张
        pair<string, double> temp_box_res; std::vector<std::string> str_res; std::string res;
        int last_index = 0; float score = 0.f; int count = 0;
        float* base = outBlob.data();
        for (int n = 0; n < T; n++) {
            float* row = base + n * C;
            int best = 0; float bestv = row[0];
            for (int k = 1; k < C; ++k) if (row[k] > bestv) { bestv = row[k]; best = k; }
            if (best > 0 && (!(n > 0 && best == last_index))) {
                score += bestv; count += 1;
                if (best < (int)this->label_list_.size()) str_res.push_back(this->label_list_[best]);
            }
            last_index = best;
        }
        if (count > 0) score /= count; else score = 0.f;
        for (auto &s : str_res) res += s;
        temp_box_res.first = res; temp_box_res.second = score; rec_res.push_back(temp_box_res);
        // 不移除任何框：idx_map 留空即可
    }

    // 释放一次性资源
    cudaStreamDestroy(stream);
    CHECK(cudaFree(d_input));
    CHECK(cudaFree(d_output));
}
TextRec::~TextRec(){
    // 释放 TensorRT 资源（顺序：context -> engine -> runtime）
    try { if (m_context) { m_context->destroy(); m_context = nullptr; } } catch(...) {}
    try { if (m_engine)  { m_engine->destroy();  m_engine  = nullptr; } } catch(...) {}
    try { if (m_runtime) { m_runtime->destroy(); m_runtime = nullptr; } } catch(...) {}
}

} // namespace OCR