#pragma once

#include <NvInfer.h>
#include "logging.h"
#include <opencv2/opencv.hpp>
#include <cstdlib>
#include <string>
#include <algorithm>
#include <vector>
#include <tuple>
#include <memory>

using namespace cv;

#define CHECK(status) \
    do\
    {\
        auto ret = (status);\
        if (ret != 0)\
        {\
            std::cerr << "Cuda failure: " << ret << std::endl;\
            abort();\
        }\
    } while (0)

namespace DocLayout {

// PP-DocLayout-L 的23个类别映射
const std::vector<std::string> CLASS_NAMES = {
    "paragraph_title",  // 0 - 段落标题
    "image",           // 1 - 图像
    "text",            // 2 - 文本
    "number",          // 3 - 数字
    "abstract",        // 4 - 摘要
    "content",         // 5 - 内容
    "chart",           // 6 - 图表
    "figure",          // 7 - 图形
    "figure_caption",  // 8 - 图形标题
    "formula",         // 9 - 公式
    "handwriting",     // 10 - 手写
    "doc_title",       // 11 - 文档标题
    "footnote",        // 12 - 脚注
    "header",          // 13 - 页眉
    "algorithm",       // 14 - 算法
    "reference",       // 15 - 参考文献
    "seal",            // 16 - 印章
    "list",            // 17 - 列表
    "table",           // 18 - 表格
    "code",            // 19 - 代码
    "footer",          // 20 - 页脚
    "footer_image",    // 21 - 页脚图像
    "table_caption"    // 22 - 表格标题
};

// 检测结果结构体
struct LayoutResult {
    int cls_id;                    // 类别ID
    std::string label;             // 类别名称
    float score;                   // 置信度
    std::vector<int> coordinate;   // 坐标 [x1, y1, x2, y2]
    int area;                      // 面积
    
    LayoutResult(int id, const std::string& name, float conf, 
                 const std::vector<int>& coord) 
        : cls_id(id), label(name), score(conf), coordinate(coord) {
        if (coord.size() >= 4) {
            area = (coord[2] - coord[0]) * (coord[3] - coord[1]);
        } else {
            area = 0;
        }
    }
};

class DocLayoutDetector {
public:
    DocLayoutDetector(int gpu_id = 0, float conf_threshold = 0.3f)
        : gpu_id_(gpu_id), conf_threshold_(conf_threshold), target_size_(640) {
        // 设置 GPU
        cudaSetDevice(gpu_id_);
        
        std::cout << "[DocLayoutDetector] Using GPU " << gpu_id_ 
                  << ", conf_threshold: " << conf_threshold_ << std::endl;
    }
    
    ~DocLayoutDetector();
    
    // 模型初始化
    int Model_Init(const std::string& engine_path);
    int Model_Init(); // 使用默认路径
    
    // 预测接口
    std::vector<LayoutResult> predict(const cv::Mat& image);
    
    // 获取配置信息
    int getGpuId() const { return gpu_id_; }
    float getConfThreshold() const { return conf_threshold_; }
    
private:
    int gpu_id_;
    float conf_threshold_;
    int target_size_;  // 网络输入尺寸 640x640
    
    // TensorRT 相关
    nvinfer1::IRuntime* runtime_;
    nvinfer1::ICudaEngine* engine_;
    nvinfer1::IExecutionContext* context_;
    
    // GPU 内存指针
    void* gpu_buffers_[5];  // 最多5个输入输出
    float* host_input_;
    float* host_output_;
    
    // 输入输出信息
    int input_size_;
    int output_size_;
    int batch_size_;
    
    // 内部方法
    void preprocess(const cv::Mat& image, float* input_data, 
                   float& scale_x, float& scale_y);
    std::vector<LayoutResult> postprocess(float* output_data, 
                                        int orig_width, int orig_height,
                                        float scale_x, float scale_y);
    std::vector<LayoutResult> apply_nms(const std::vector<LayoutResult>& results, 
                                      float iou_threshold = 0.5f);
    float calculate_iou(const std::vector<int>& box1, const std::vector<int>& box2);
    
    // 内存管理
    void allocate_memory();
    void free_memory();
};

} // namespace DocLayout
