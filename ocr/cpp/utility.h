

#pragma once

#include <dirent.h>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <ostream>
#include <stdlib.h>
#include <vector>

#include <algorithm>
#include <cstring>
#include <fstream>
#include <numeric>

#include <sys/stat.h>
#include <sys/types.h>

#include <opencv2/opencv.hpp>
//#include <opencv2/freetype.hpp>
#include "opencv2/core.hpp"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"

namespace OCR {

// 精简的 OCR 配置 - 只保留必要的参数传递
struct OCRConfig {
    std::string version = "v5";  // 默认 v5
    int gpu_id = 0;              // 默认 GPU 0

    // 从环境变量读取配置
    OCRConfig() {
        if (const char* v = std::getenv("OCR_VERSION")) {
            std::string ver(v);
            std::transform(ver.begin(), ver.end(), ver.begin(), ::tolower);
            version = (ver == "v4") ? "v4" : "v5";
        }

        if (const char* gpu = std::getenv("OCR_GPU_ID")) {
            try { gpu_id = std::stoi(gpu); } catch(...) {}
        }
    }

    // 固定的模型路径生成
    std::string getEnginePath(const std::string& model_type) const {
        return "/workspace/hngpt/models/" + version + "." + model_type + ".trt";
    }
};

// 全局配置实例
inline OCRConfig& getOCRConfig() {
    static OCRConfig config;
    return config;
}

class Utility {
public:
  static std::vector<std::string> ReadDict(const std::string &path);

  static void
  VisualizeBboxes(const cv::Mat &srcimg,
                  const std::vector<std::vector<std::vector<int>>> &boxes,
                  std::vector<std::pair<std::string,double>>&rec_res,
                  std::string &detimg);

  template <class ForwardIterator>
  inline static size_t argmax(ForwardIterator first, ForwardIterator last) {
    return std::distance(first, std::max_element(first, last));
  }

  static void GetAllFiles(const char *dir_name,
                          std::vector<std::string> &all_inputs);
    
  static cv::Mat GetRotateCropImage(const cv::Mat &srcimage,
                          std::vector<std::vector<int>> box);
    
  static std::vector<int> argsort(const std::vector<float>& array);


  static void SortAndLayoutBoxes(std::vector<std::vector<std::vector<int>>>& boxes) ;
  static void sortResult(std::vector<std::vector<std::vector<int>>>&result);
};

} // namespace OCR