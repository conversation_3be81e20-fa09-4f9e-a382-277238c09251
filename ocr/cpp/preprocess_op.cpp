
#include "opencv2/core.hpp"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
//#include <opencv2/dnn.hpp>
#include <chrono>
#include <iomanip>
#include <iostream>
#include <ostream>
#include <vector>

#include <cstring>
#include <fstream>
#include <numeric>

#include "preprocess_op.h"

namespace OCR {

// 🚀 简化的函数实现

// HWC -> CHW 转换函数
void permute_hwc_to_chw(const cv::Mat *im, float *data) {
  printf("[Preprocess] permute_hwc_to_chw called: %dx%dx%d\n", im->rows, im->cols, im->channels());
  int rh = im->rows;
  int rw = im->cols;
  int rc = im->channels();
  for (int i = 0; i < rc; ++i) {
    cv::extractChannel(*im, cv::Mat(rh, rw, CV_32FC1, data + i * rh * rw), i);
  }
}

// 批量 HWC -> CHW 转换函数
void permute_batch_hwc_to_chw(const std::vector<cv::Mat> imgs, float *data) {
    //printf("[Preprocess] permute_batch_hwc_to_chw called with %zu images\n", imgs.size());
    for (int j = 0; j < imgs.size(); j ++){
        int rh = imgs[j].rows;
        int rw = imgs[j].cols;
        int rc = imgs[j].channels();
        for (int i = 0; i < rc; ++i) {
            cv::extractChannel(imgs[j], cv::Mat(rh, rw, CV_32FC1, data + (j * rc + i) * rh * rw), i);
        }
    }
}

// 归一化函数
void normalize_image(cv::Mat *im, const std::vector<float> &mean,
                    const std::vector<float> &scale, const bool is_scale) {
  //printf("[Preprocess] normalize_image called: %dx%d\n", im->rows, im->cols);
  double e = 1.0;
  if (is_scale) {
    e /= 255.0;
  }
  (*im).convertTo(*im, CV_32FC3, e);
  std::vector<cv::Mat> bgr_channels(3);
  cv::split(*im, bgr_channels);
  for (auto i = 0; i < bgr_channels.size(); i++) {
    bgr_channels[i].convertTo(bgr_channels[i], CV_32FC1, 1.0 * scale[i],
                              (0.0 - mean[i]) * scale[i]);
  }
  cv::merge(bgr_channels, *im);
}

// 检测模型resize函数 - 支持动态尺寸
void resize_for_detection(const cv::Mat &img, cv::Mat &resize_img, int target_size,
                         float &ratio_h, float &ratio_w) {
  printf("[Preprocess] 🎯 resize_for_detection called: %dx%d -> %d\n", img.cols, img.rows, target_size);

  // 简单直接的resize到目标尺寸
  cv::resize(img, resize_img, cv::Size(target_size, target_size));

  ratio_h = float(target_size) / float(img.rows);
  ratio_w = float(target_size) / float(img.cols);

  printf("[Preprocess] ✅ Resized to %dx%d, ratios: %.3f, %.3f\n",
         resize_img.cols, resize_img.rows, ratio_w, ratio_h);
}


void resize2pad(const cv::Mat &img, cv::Mat &resize_img, int target_size) {
    int w = img.cols;
    int h = img.rows;
    float ratio = float(w) / float(h);
    int resize_w, resize_h;
    if (w > h) {  // 如果原始图像是宽屏
        resize_w = target_size;
        resize_h = static_cast<int>(target_size / ratio);
    } else {  // 如果原始图像是高屏或宽高相等
        resize_h = target_size;
        resize_w = static_cast<int>(target_size * ratio);
    }
    //除32，余下的超过16补全为32，不足16的长度舍弃
    resize_h = max(int(round(float(resize_h) / 32) * 32), 32);
    resize_w = max(int(round(float(resize_w) / 32) * 32), 32);

    // 调整图像大小
    cv::Mat temp_img;
    cv::resize(img, temp_img, cv::Size(resize_w, resize_h));

    // 创建960x960的黑色背景图像
    resize_img = cv::Mat::zeros(target_size, target_size, temp_img.type());

    // 将调整后的图像放置到背景图像的中心
    int x_offset = (target_size - resize_w) / 2;
    int y_offset = (target_size - resize_h) / 2;
    temp_img.copyTo(resize_img(cv::Rect(x_offset, y_offset, resize_w, resize_h)));
}


// 识别模型resize函数
void resize_for_recognition(const cv::Mat &img, cv::Mat &resize_img, float wh_ratio,
                           const std::vector<int> &rec_image_shape) {
  //printf("[Preprocess] resize_for_recognition called\n");
  int imgC, imgH, imgW;
  imgC = rec_image_shape[0];
  imgH = rec_image_shape[1];
  imgW = rec_image_shape[2];

  // 对齐 infer-trt.py 的 _resize_norm_img：
  // resized_w = dest_w if ceil(dest_h * ratio) > dest_w else ceil(dest_h * ratio)
  float ratio = static_cast<float>(img.cols) / std::max(1, img.rows);
  int expect_w = static_cast<int>(std::ceil(imgH * ratio));
  int resize_w = (expect_w > imgW) ? imgW : expect_w;

  cv::resize(img, resize_img, cv::Size(resize_w, imgH), 0.f, 0.f, cv::INTER_LINEAR);
  // 右侧 padding 到 imgW
  cv::copyMakeBorder(resize_img, resize_img, 0, 0, 0,
                     std::max(0, imgW - resize_img.cols), cv::BORDER_CONSTANT,
                     cv::Scalar(127, 127, 127));
}

// 分类模型resize函数
void resize_for_classification(const cv::Mat &img, cv::Mat &resize_img,
                              const std::vector<int> &rec_image_shape) {
  printf("[Preprocess] resize_for_classification called\n");
  int imgC, imgH, imgW;
  imgC = rec_image_shape[0];
  imgH = rec_image_shape[1];
  imgW = rec_image_shape[2];

  float ratio = float(img.cols) / float(img.rows);
  int resize_w, resize_h;
  if (ceilf(imgH * ratio) > imgW)
    resize_w = imgW;
  else
    resize_w = int(ceilf(imgH * ratio));

  cv::resize(img, resize_img, cv::Size(resize_w, imgH), 0.f, 0.f,
             cv::INTER_LINEAR);
  if (resize_w < imgW) {
    cv::copyMakeBorder(resize_img, resize_img, 0, 0, 0, imgW - resize_w,
                       cv::BORDER_CONSTANT, cv::Scalar(0, 0, 0));
  }
}



void resizeAndNormalize(const cv::Mat &img, cv::Mat &resize_img, int target_size) {
    // PaddleOCR v5 style: resize to target_size x target_size, then normalize: (img/255 - mean) / std
    printf("[Preprocess] 🎯 resizeAndNormalize called: %dx%d, target=%d\n", img.cols, img.rows, target_size);

    // 1) resize to target_size x target_size
    cv::resize(img, resize_img, cv::Size(target_size, target_size), 0, 0, cv::INTER_LINEAR);
    printf("[Preprocess] ✅ resized to %dx%d\n", resize_img.cols, resize_img.rows);

    // 2) normalize: BGR order, (x/255 - mean) / std
    resize_img.convertTo(resize_img, CV_32FC3, 1.0 / 255.0);
    std::vector<cv::Mat> bgr(3);
    cv::split(resize_img, bgr);
    const float mean[3] = {0.485f, 0.456f, 0.406f};
    const float stdv[3] = {0.229f, 0.224f, 0.225f};
    for (int c = 0; c < 3; ++c) {
        bgr[c] = (bgr[c] - mean[c]) / stdv[c];
    }
    cv::merge(bgr, resize_img);
}

} // namespace OCR
