
#include "utility.h"

namespace OCR {

std::vector<std::string> Utility::ReadDict(const std::string &path) {
  std::ifstream in(path);
  std::string line;
  std::vector<std::string> m_vec;
  if (in) {
    while (getline(in, line)) {
      m_vec.push_back(line);
    }
  } else {
    std::cout << "no such label file: " << path << ", exit the program..."
              << std::endl;
    exit(1);
  }
  return m_vec;
}

void Utility::VisualizeBboxes(const cv::Mat &srcimg, const std::vector<std::vector<std::vector<int>>> &boxes,
                            std::vector<std::pair<std::string,double>>&rec_res, std::string &detimg) {

    cv::Mat img_vis;
    srcimg.copyTo(img_vis);
    //std::cout<< boxes.size() << "," << boxes[0].size() << "," << boxes[0][0].size()<<std::endl;
    //std::cout<< rec_res.size() <<std::endl;
    //for (int n = 0; n < std::min(boxes.size(), rec_res.size()); n++) {
    for (int n = 0; n < rec_res.size(); n++) {
        cv::Point rook_points[4];
        for (int m = 0; m < boxes[n].size(); m++) {
            rook_points[m] = cv::Point(int(boxes[n][m][0]), int(boxes[n][m][1]));
        }

        const cv::Point *ppt[1] = {rook_points};
        int npt[] = {4};
        cv::polylines(img_vis, ppt, npt, 1, 1, CV_RGB(0, 255, 0), 2, 8, 0);

        std::string res=rec_res[n].first;
        res =res + " " + std::to_string(rec_res[n].second);

        //cv::putText(img_vis, res, rook_points[0], cv::FONT_HERSHEY_PLAIN, 1.2, cv::Scalar(0, 0, 255), 2);
        std::cout<<"res:"<<res<<std::endl;
        // cv::Ptr<cv::freetype::FreeType2> ft2 = cv::freetype::createFreeType2();
        // ft2->loadFontData("/usr/share/fonts/simsun.ttc", 0);
        // ft2->putText(img_vis, res, rook_points[0], 30, cv::Scalar(0, 0, 255), 1, 8, false);

    }
    //cv::imshow("ocr res", img_vis);
    //cv::waitKey(0);

    //cv::imwrite(detimg, img_vis);
    //std::cout << "The detection visualized image saved in ./ocr_vis.png" << std::endl;
}

// list all files under a directory
void Utility::GetAllFiles(const char *dir_name,
                          std::vector<std::string> &all_inputs) {
  if (NULL == dir_name) {
    std::cout << " dir_name is null ! " << std::endl;
    return;
  }
  struct stat s;
  lstat(dir_name, &s);
  if (!S_ISDIR(s.st_mode)) {
    std::cout << "dir_name is not a valid directory !" << std::endl;
    all_inputs.push_back(dir_name);
    return;
  } else {
    struct dirent *filename; // return value for readdir()
    DIR *dir;                // return value for opendir()
    dir = opendir(dir_name);
    if (NULL == dir) {
      std::cout << "Can not open dir " << dir_name << std::endl;
      return;
    }
    std::cout << "Successfully opened the dir !" << std::endl;
    while ((filename = readdir(dir)) != NULL) {
      if (strcmp(filename->d_name, ".") == 0 ||
          strcmp(filename->d_name, "..") == 0)
        continue;
      // img_dir + std::string("/") + all_inputs[0];
      all_inputs.push_back(dir_name + std::string("/") +
                           std::string(filename->d_name));
    }
  }
}

cv::Mat Utility::GetRotateCropImage(const cv::Mat &srcimage,
                            std::vector<std::vector<int>> box) {
  cv::Mat image;
  srcimage.copyTo(image);
  std::vector<std::vector<int>> points = box;

  // 检查输入有效性
  if (box.size() != 4) {
    std::cout << "Error: box should have 4 points, got " << box.size() << std::endl;
    return cv::Mat();
  }

  for (int i = 0; i < 4; i++) {
    if (box[i].size() != 2) {
      std::cout << "Error: each point should have 2 coordinates" << std::endl;
      return cv::Mat();
    }
  }

  int x_collect[4] = {box[0][0], box[1][0], box[2][0], box[3][0]};
  int y_collect[4] = {box[0][1], box[1][1], box[2][1], box[3][1]};
  int left = int(*std::min_element(x_collect, x_collect + 4));
  int right = int(*std::max_element(x_collect, x_collect + 4));
  int top = int(*std::min_element(y_collect, y_collect + 4));
  int bottom = int(*std::max_element(y_collect, y_collect + 4));

  // 健壮的边界检查和修正
  left = std::max(0, left);
  top = std::max(0, top);
  right = std::min(image.cols, right);
  bottom = std::min(image.rows, bottom);

  // 确保边界框有效（左 < 右，上 < 下）
  if (left >= right) {
    left = std::max(0, right - 5);
  }
  if (top >= bottom) {
    top = std::max(0, bottom - 5);
  }

  // 检查裁剪区域是否有效
  int crop_width = right - left;
  int crop_height = bottom - top;

  if (crop_width <= 0 || crop_height <= 0 || left < 0 || top < 0 || right > image.cols || bottom > image.rows) {
    std::cout << "Error: invalid crop region: " << crop_width << "x" << crop_height << std::endl;
    std::cout << "  Coordinates: left=" << left << ", right=" << right << ", top=" << top << ", bottom=" << bottom << std::endl;
    std::cout << "  Image size: " << image.cols << "x" << image.rows << std::endl;
    std::cout << "  Skipping this invalid region" << std::endl;
    return cv::Mat();
  }

  cv::Mat img_crop;
  image(cv::Rect(left, top, crop_width, crop_height)).copyTo(img_crop);

  for (int i = 0; i < points.size(); i++) {
    points[i][0] -= left;
    points[i][1] -= top;
  }

  int img_crop_width = int(sqrt(pow(points[0][0] - points[1][0], 2) +
                                pow(points[0][1] - points[1][1], 2)));
  int img_crop_height = int(sqrt(pow(points[0][0] - points[3][0], 2) +
                                 pow(points[0][1] - points[3][1], 2)));

  // 检查计算出的尺寸是否有效
  if (img_crop_width <= 0 || img_crop_height <= 0) {
    std::cout << "Error: invalid calculated dimensions: " << img_crop_width << "x" << img_crop_height << std::endl;
    return cv::Mat();
  }

  // 进行四点透视矫正
  std::vector<cv::Point2f> srcPts(4);
  for(int i=0;i<4;i++) srcPts[i] = cv::Point2f(points[i][0], points[i][1]);
  float w1 = std::hypot(srcPts[0].x - srcPts[1].x, srcPts[0].y - srcPts[1].y);
  float w2 = std::hypot(srcPts[2].x - srcPts[3].x, srcPts[2].y - srcPts[3].y);
  float h1 = std::hypot(srcPts[0].x - srcPts[3].x, srcPts[0].y - srcPts[3].y);
  float h2 = std::hypot(srcPts[1].x - srcPts[2].x, srcPts[1].y - srcPts[2].y);
  int dstW = std::max(1, (int)std::max(w1, w2));
  int dstH = std::max(1, (int)std::max(h1, h2));
  std::vector<cv::Point2f> dstPts{
    cv::Point2f(0,0), cv::Point2f(dstW-1,0), cv::Point2f(dstW-1,dstH-1), cv::Point2f(0,dstH-1)
  };
  cv::Mat M = cv::getPerspectiveTransform(srcPts, dstPts);
  cv::Mat warped;
  cv::warpPerspective(img_crop, warped, M, cv::Size(dstW, dstH), cv::INTER_CUBIC, cv::BORDER_REPLICATE);
  if (warped.empty() || warped.rows<=0 || warped.cols<=0) {
    std::cout << "Error: invalid warped image" << std::endl;
    return img_crop; // 回退
  }
  // 对齐 infer-trt.py：不做额外加宽画布处理，仅在高宽比大时旋转
  // 其他情况：与 infer.py 一致：若长宽比 >= 1.5，则顺时针旋转 90 度
  if (warped.rows * 1.0f / std::max(1, warped.cols) >= 1.5f) {
    cv::Mat rotated;
    // 对齐 infer-trt.py: np.rot90 默认逆时针 90 度
    cv::rotate(warped, rotated, cv::ROTATE_90_COUNTERCLOCKWISE);
    warped = rotated;
  }
  return warped;
}

std::vector<int> Utility::argsort(const std::vector<float>& array)
{
    const int array_len(array.size());
    std::vector<int> array_index(array_len, 0);
    for (int i = 0; i < array_len; ++i)
        array_index[i] = i;

    std::sort(array_index.begin(), array_index.end(),
        [&array](int pos1, int pos2) {return (array[pos1] < array[pos2]); });

    return array_index;
}



void Utility::sortResult(std::vector<std::vector<std::vector<int>>>& result){
  if(result.empty()) return;
  sort(result.begin(), result.end(), [](const std::vector<std::vector<int>>& a, const std::vector<std::vector<int>>& b) {
    return a[0][1] < b[0][1];
  });

  std::vector<std::vector<std::vector<int>>> sort_dots;
  std::vector<std::vector<int>> first_dot{result[0][0]};
  sort_dots.push_back(first_dot);

  int index = 0;
  for (int i = 1; i < result.size(); i++) {
    const std::vector<int>& dot = result[i][0];
    double mean_y = std::accumulate(sort_dots[index].begin(), sort_dots[index].end(), 0.0, [](double sum, const std::vector<int>& x) { return sum + x[1]; }) / sort_dots[index].size();
    if (sort_dots[index].empty() || abs(dot[1] - mean_y) < 30) {
      sort_dots[index].push_back(dot);  
    } else {
      std::vector<std::vector<int>> new_dot{dot};
      sort_dots.push_back(new_dot);
      index++;
    }
  }

  std::vector<std::vector<std::vector<int>>> sort_result;
  for (auto& dots : sort_dots) {
    sort(dots.begin(), dots.end(), [](const std::vector<int>& a, const std::vector<int>& b) {
      if (abs(a[0] - b[0]) < 5) {
          return a[1] == b[1] ? a[0] < b[0] : a[1] < b[1];
      } else {
          return a[0] < b[0];
      }
    });
    for (auto& dot : dots) {
      for (auto& res : result) {
        if (res[0] == dot) {
          sort_result.push_back(res);
        }
      }
    }
  }
  result = sort_result;
}

} // namespace OCR