#include "ocr.h"
#include "layout.h"
#include "ndarray_converter.h"
#include <dirent.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <pybind11/pybind11.h>
#include <pybind11/numpy.h>
#include <pybind11/stl.h>
#include <opencv2/core.hpp>
#include <map>
#include <string>
#include <mutex>
#include <atomic>
namespace py = pybind11;
using namespace OCR;
using namespace DocLayout;

class MultiGPUOCR {
private:
    static std::map<std::string, TrtOcr*> instances_;
    static std::mutex instances_mutex_;
public:
    // 获取指定GPU和版本的OCR实例
    static TrtOcr* getInstance(int gpu_id, const std::string& version) {
        std::string key = version + "_gpu" + std::to_string(gpu_id);

        // 检查是否已存在该配置的实例
        auto it = instances_.find(key);
        if (it != instances_.end()) {
            // 验证实例是否仍然有效
            TrtOcr* existing_instance = it->second;
            if (existing_instance != nullptr) {
                // 检查GPU ID是否匹配
                if (existing_instance->getGpuId() == gpu_id) {
                    printf("[MultiGPUOCR] Reusing existing ocr instance on GPU %d with conf %.2f (key: %s)\n",
                           gpu_id, key.c_str());
                    return existing_instance;
                } else {
                    printf("[MultiGPUOCR] GPU ID mismatch, removing invalid instance\n");
                    delete existing_instance;
                    instances_.erase(it);
                }
            } else {
                printf("[MultiGPUOCR] Found null instance, removing from cache\n");
                instances_.erase(it);
            }
        }

        // 创建新实例，传递版本和GPU ID参数
        printf("[MultiGPUOCR] Creating new OCR instance for %s on GPU %d\n", version.c_str(), gpu_id);

        TrtOcr* ocr_instance = new TrtOcr(version, gpu_id);

        // 直接构造模型路径（基于传入的版本参数）
        std::string model_dir = "/workspace/hngpt/models/";
        std::string det_engine_path = model_dir + version + ".det.trt";
        std::string rec_engine_path = model_dir + version + ".rec.trt";

        ocr_instance->Model_Init(det_engine_path, rec_engine_path);

        // 存储实例
        instances_[key] = ocr_instance;

        return ocr_instance;
    }

    // 获取当前配置的OCR实例（从环境变量读取）
    static TrtOcr* getInstance() {
        OCRConfig& config = getOCRConfig();
        return getInstance(config.gpu_id, config.version);
    }

    // 显式释放所有资源
    static void finalizeAll() {
        std::lock_guard<std::mutex> lock(instances_mutex_);
        for (auto& pair : instances_) {
            if (pair.second) {
                delete pair.second;
            }
        }
        instances_.clear();
    }

    // 兼容性方法：释放当前实例
    static void finalize() {
        OCRConfig& config = getOCRConfig();
        std::string key = config.version + "_gpu" + std::to_string(config.gpu_id);
        std::lock_guard<std::mutex> lock(instances_mutex_);
        auto it = instances_.find(key);
        if (it != instances_.end()) {
            delete it->second;
            instances_.erase(it);
        }
    }
};

// 静态成员变量定义
std::map<std::string, TrtOcr*> MultiGPUOCR::instances_;
std::mutex MultiGPUOCR::instances_mutex_;

// DocLayout 多GPU管理类
class MultiGPULayout {
private:
    static std::map<std::string, DocLayoutDetector*> instances_;
    static std::mutex instances_mutex_;
public:
    // 获取指定GPU的Layout实例
    static DocLayoutDetector* getInstance(int gpu_id, float conf_threshold = 0.3f) {
        std::string key = "gpu" + std::to_string(gpu_id) + "_conf" + std::to_string(conf_threshold);
        std::lock_guard<std::mutex> lock(instances_mutex_);
        auto it = instances_.find(key);
        if (it != instances_.end()) {
            // 验证实例是否仍然有效
            DocLayoutDetector* existing_instance = it->second;
            if (existing_instance != nullptr) {
                // 检查GPU ID是否匹配
                if (existing_instance->getGpuId() == gpu_id) {
                    printf("[MultiGPULayout] Reusing existing ocr instance on GPU %d with conf %.2f (key: %s)\n",
                           gpu_id, key.c_str());
                    return existing_instance;
                } else {
                    printf("[MultiGPULayout] GPU ID mismatch, removing invalid instance\n");
                    delete existing_instance;
                    instances_.erase(it);
                }
            } else {
                printf("[MultiGPULayout] Found null instance, removing from cache\n");
                instances_.erase(it);
            }
        }

        // 创建新实例
        printf("[MultiGPULayout] Creating new Layout instance on GPU %d with conf %.2f (key: %s)\n",
               gpu_id, conf_threshold, key.c_str());
        printf("[MultiGPULayout] Current instances count: %zu\n", instances_.size());

        DocLayoutDetector* layout_instance = new DocLayoutDetector(gpu_id, conf_threshold);

        // 初始化模型
        if (layout_instance->Model_Init() != 0) {
            printf("[MultiGPULayout] Failed to initialize model on GPU %d\n", gpu_id);
            delete layout_instance;
            return nullptr;
        }

        // 存储实例
        instances_[key] = layout_instance;
        printf("[MultiGPULayout] Stored instance, new count: %zu\n", instances_.size());

        return layout_instance;
    }

    // 获取默认实例 (GPU 0)
    static DocLayoutDetector* getInstance() {
        return getInstance(0, 0.3f);
    }

    // 显式释放所有资源
    static void finalizeAll() {
        std::lock_guard<std::mutex> lock(instances_mutex_);
        for (auto& pair : instances_) {
            if (pair.second) {
                delete pair.second;
            }
        }
        instances_.clear();
    }

    // 释放指定GPU的实例
    static void finalize(int gpu_id, float conf_threshold = 0.3f) {
        std::string key = "gpu" + std::to_string(gpu_id) + "_conf" + std::to_string(conf_threshold);
        std::lock_guard<std::mutex> lock(instances_mutex_);
        auto it = instances_.find(key);
        if (it != instances_.end()) {
            delete it->second;
            instances_.erase(it);
        }
    }
};

// 静态成员变量定义
std::map<std::string, DocLayoutDetector*> MultiGPULayout::instances_;
std::mutex MultiGPULayout::instances_mutex_;

class OCRWrapper {
private:
    TrtOcr* ocr_instance;

public:
    OCRWrapper() {
        ocr_instance = MultiGPUOCR::getInstance();
    }

    void print_buffer(py::buffer_info& buf) {
        printf("ptr: %p\n", buf.ptr);
        printf("itemsize: %d\n", buf.itemsize);
        printf("format: %s\n", buf.format);
        printf("ndim: %d\n", buf.ndim);

        printf("shape: [");
        for (auto dim : buf.shape) {
            printf("%ld, ", dim);
        }
        printf("]\n");

        printf("strides: [");
        for (auto stride : buf.strides) {
            printf("%ld, ", stride);
        }
        printf("]\n");
        printf("readonly: %s\n", buf.readonly ? "true" : "false");
        printf("size: %ld\n", buf.size);
    }

    cv::Mat numpyToMatRGB(pybind11::array_t<uint8_t>& array) {
        py::buffer_info buf = array.request();
        int rows = static_cast<int>(buf.shape[0]);
        int cols = static_cast<int>(buf.shape[1]);
        int channels = static_cast<int>(buf.shape[2]);
        // 不修改原始 numpy 缓冲区，避免颜色被就地改变
        if (channels == 3) {
            cv::Mat src_ref(rows, cols, CV_8UC3, (unsigned char*)array.data());
            cv::Mat mat;
            cv::cvtColor(src_ref, mat, cv::COLOR_BGR2RGB);
            return mat; // 新的独立缓冲
        } else if (channels == 4) {
            cv::Mat src_ref(rows, cols, CV_8UC4, (unsigned char*)array.data());
            cv::Mat mat;
            cv::cvtColor(src_ref, mat, cv::COLOR_BGRA2RGB);
            return mat;
        } else {
            cv::Mat src_ref(rows, cols, CV_8UC1, (unsigned char*)array.data());
            cv::Mat mat;
            cv::cvtColor(src_ref, mat, cv::COLOR_GRAY2RGB);
            return mat;
        }
        return cv::Mat();
    }



    py::list rapid_ocr(pybind11::array_t<uint8_t>& array) {
        try {
            cv::Mat test_img = numpyToMatRGB(array);
            //cv::imwrite("test.jpg", test_img);
            std::vector<std::tuple<std::vector<std::vector<int>>, std::string, double>> res = ocr_instance->Model_Infer(test_img);
            py::list list;
            for(auto re : res){
                py::list list_;
                list_.append(std::get<0>(re));
                list_.append(std::get<1>(re));
                list_.append(std::get<2>(re));
                list.append(list_);
            }
            return list;
        } catch (const std::exception& e) {
            std::cerr << "rapid_ocr An exception occurred: " << e.what() << std::endl;
            return py::list();
        } catch (...) {
            std::cerr << "rapid_ocr An unknown exception occurred." << std::endl;
            return py::list();
        }


    }



    py::list rapid_ocrm(cv::Mat& img) {
        try {
            std::vector<std::tuple<std::vector<std::vector<int>>, std::string, double>> res = ocr_instance->Model_Infer(img);
            py::list list;
            for(auto re : res){
                py::list list_;
                list_.append(std::get<0>(re));
                list_.append(std::get<1>(re));
                list_.append(std::get<2>(re));
                list.append(list_);
            }
            return list;
        } catch (const std::exception& e) {
            std::cerr << "rapid_ocr An exception occurred: " << e.what() << std::endl;
            return py::list();
        } catch (...) {
            std::cerr << "rapid_ocr An unknown exception occurred." << std::endl;
            return py::list();
        }


    }

        py::list detect_only(py::array_t<uint8_t>& array) {
            try {
                cv::Mat img = numpyToMatRGB(array);
                auto boxes = ocr_instance->Model_Detect(img);
                py::list out;
                for (auto &b : boxes) {
                    py::list bb;
                    for (auto &pt : b) bb.append(pt);
                    out.append(bb);
                }
                return out;
            } catch (const std::exception& e) {
                std::cerr << "detect_only exception: " << e.what() << std::endl;
                return py::list();
            } catch (...) {
                std::cerr << "detect_only unknown exception" << std::endl;
                return py::list();
            }
        }


};

// DocLayout 包装类
class LayoutWrapper {
private:
    DocLayoutDetector* layout_instance;

public:
    LayoutWrapper() {
        layout_instance = MultiGPULayout::getInstance();
    }

    cv::Mat numpyToMatBGR(pybind11::array_t<uint8_t>& array) {
        py::buffer_info buf = array.request();
        int rows = static_cast<int>(buf.shape[0]);
        int cols = static_cast<int>(buf.shape[1]);
        int channels = static_cast<int>(buf.shape[2]);

        if (channels == 3) {
            // 假设输入是BGR格式，直接使用
            cv::Mat mat(rows, cols, CV_8UC3, (unsigned char*)array.data());
            return mat.clone(); // 返回独立副本
        } else if (channels == 4) {
            cv::Mat src_ref(rows, cols, CV_8UC4, (unsigned char*)array.data());
            cv::Mat mat;
            cv::cvtColor(src_ref, mat, cv::COLOR_BGRA2BGR);
            return mat;
        } else {
            cv::Mat src_ref(rows, cols, CV_8UC1, (unsigned char*)array.data());
            cv::Mat mat;
            cv::cvtColor(src_ref, mat, cv::COLOR_GRAY2BGR);
            return mat;
        }
        return cv::Mat();
    }

    py::list rapid_layout(pybind11::array_t<uint8_t>& array) {
        try {
            cv::Mat img = numpyToMatBGR(array);
            auto results = layout_instance->predict(img);

            py::list list;
            for (const auto& result : results) {
                py::dict item;
                item["cls_id"] = result.cls_id;
                item["label"] = result.label;
                item["score"] = result.score;
                item["coordinate"] = result.coordinate;
                item["area"] = result.area;
                list.append(item);
            }
            return list;
        } catch (const std::exception& e) {
            std::cerr << "rapid_layout exception: " << e.what() << std::endl;
            return py::list();
        } catch (...) {
            std::cerr << "rapid_layout unknown exception" << std::endl;
            return py::list();
        }
    }
};



py::list rapid_ocr(py::array_t<uint8_t>& img_array) {
    static OCRWrapper wrapper;
    return wrapper.rapid_ocr(img_array);
}

py::list rapid_ocrm(cv::Mat& img) {
    static OCRWrapper wrapper;
    return wrapper.rapid_ocrm(img);
}

// 使用指定 GPU 和版本的 rapid_ocr
py::list rapid_ocr_with_params(py::array_t<uint8_t>& img_array, int gpu_id, const std::string& version) {
    try {
        auto* ocr_instance = MultiGPUOCR::getInstance(gpu_id, version);
        if (!ocr_instance) {
            std::cerr << "Failed to get OCR instance for GPU " << gpu_id << " version " << version << std::endl;
            return py::list();
        }

        // 转换 numpy array 到 cv::Mat
        py::buffer_info buf_info = img_array.request();
        cv::Mat img(buf_info.shape[0], buf_info.shape[1], CV_8UC3, (unsigned char*)buf_info.ptr);

        // 执行 OCR - 使用 Model_Infer 方法
        auto ocr_results = ocr_instance->Model_Infer(img);

        // 转换结果为 Python list
        py::list result;
        for (const auto& ocr_result : ocr_results) {
            py::list item;

            // 添加 box 坐标
            py::list box_list;
            for (const auto& point : std::get<0>(ocr_result)) {
                py::list point_list;
                for (int coord : point) {
                    point_list.append(coord);
                }
                box_list.append(point_list);
            }
            item.append(box_list);

            // 添加文本和置信度
            item.append(std::get<1>(ocr_result));
            item.append(std::get<2>(ocr_result));

            result.append(item);
        }

        return result;
    } catch (const std::exception& e) {
        std::cerr << "rapid_ocr_with_params exception: " << e.what() << std::endl;
        return py::list();
    } catch (...) {
        std::cerr << "rapid_ocr_with_params unknown exception" << std::endl;
        return py::list();
    }
}

// 使用指定 GPU 和版本的 rapid_ocrm
py::list rapid_ocrm_with_params(cv::Mat& img, int gpu_id, const std::string& version) {
    try {
        auto* ocr_instance = MultiGPUOCR::getInstance(gpu_id, version);
        if (!ocr_instance) {
            std::cerr << "Failed to get OCR instance for GPU " << gpu_id << " version " << version << std::endl;
            return py::list();
        }

        // 执行 OCR - 使用 Model_Infer 方法
        auto ocr_results = ocr_instance->Model_Infer(img);

        // 转换结果为 Python list
        py::list result;
        for (const auto& ocr_result : ocr_results) {
            py::list item;

            // 添加 box 坐标
            py::list box_list;
            for (const auto& point : std::get<0>(ocr_result)) {
                py::list point_list;
                for (int coord : point) {
                    point_list.append(coord);
                }
                box_list.append(point_list);
            }
            item.append(box_list);

            // 添加文本和置信度
            item.append(std::get<1>(ocr_result));
            item.append(std::get<2>(ocr_result));

            result.append(item);
        }

        return result;
    } catch (const std::exception& e) {
        std::cerr << "rapid_ocrm_with_params exception: " << e.what() << std::endl;
        return py::list();
    } catch (...) {
        std::cerr << "rapid_ocrm_with_params unknown exception" << std::endl;
        return py::list();
    }
}

// 文档分析和OCR综合函数
py::dict rapid_document_analysis(py::array_t<uint8_t>& img_array, int gpu_id = 0) {
    try {
        // 转换numpy数组为cv::Mat
        py::buffer_info buf_info = img_array.request();
        cv::Mat image(buf_info.shape[0], buf_info.shape[1], CV_8UC3, (unsigned char*)buf_info.ptr);
        cv::Mat img_copy = image.clone();

        // 1. 版面分析
        auto* layout_instance = MultiGPULayout::getInstance(gpu_id, 0.3f);
        if (!layout_instance) {
            std::cerr << "Failed to get Layout instance for GPU " << gpu_id << std::endl;
            return py::dict();
        }

        auto layout_results = layout_instance->predict(img_copy);

        // 2. OCR识别
        auto* ocr_instance = MultiGPUOCR::getInstance(gpu_id, "v5");
        if (!ocr_instance) {
            std::cerr << "Failed to get OCR instance for GPU " << gpu_id << std::endl;
            return py::dict();
        }

        // 按类别分组处理文本区域
        py::dict result;
        py::list layout_info;
        py::list text_regions;
        std::string full_text = "";

        // 按Y坐标排序，实现从上到下的阅读顺序
        std::sort(layout_results.begin(), layout_results.end(),
                  [](const DocLayout::LayoutResult& a, const DocLayout::LayoutResult& b) {
                      return a.coordinate[1] < b.coordinate[1]; // 按y1坐标排序
                  });

        for (const auto& layout_result : layout_results) {
            py::dict layout_item;
            layout_item["cls_id"] = layout_result.cls_id;
            layout_item["label"] = layout_result.label;
            layout_item["score"] = layout_result.score;
            layout_item["coordinate"] = layout_result.coordinate;
            layout_item["area"] = layout_result.area;

            // 如果是文本类型，进行OCR识别
            if (layout_result.label == "text" ||
                layout_result.label == "paragraph_title" ||
                layout_result.label == "doc_title" ||
                layout_result.label == "number") {

                // 裁剪文本区域
                int x1 = std::max(0, layout_result.coordinate[0]);
                int y1 = std::max(0, layout_result.coordinate[1]);
                int x2 = std::min(img_copy.cols, layout_result.coordinate[2]);
                int y2 = std::min(img_copy.rows, layout_result.coordinate[3]);

                if (x2 > x1 && y2 > y1) {
                    cv::Rect roi(x1, y1, x2 - x1, y2 - y1);
                    cv::Mat text_region = img_copy(roi);

                    // OCR识别
                    auto ocr_results = ocr_instance->Model_Infer(text_region);

                    py::list ocr_texts;
                    std::string region_text = "";

                    for (const auto& ocr_result : ocr_results) {
                        std::string text = std::get<1>(ocr_result);
                        double confidence = std::get<2>(ocr_result);

                        if (confidence > 0.3 && !text.empty()) { // 置信度过滤 - 降低阈值以识别更多文本
                            py::dict ocr_item;
                            ocr_item["text"] = text;
                            ocr_item["confidence"] = confidence;
                            ocr_texts.append(ocr_item);

                            region_text += text + " ";
                        }
                    }

                    layout_item["ocr_results"] = ocr_texts;
                    layout_item["text_content"] = region_text;

                    // 根据类型添加到完整文本
                    if (layout_result.label == "doc_title") {
                        full_text += "【标题】" + region_text + "\n\n";
                    } else if (layout_result.label == "paragraph_title") {
                        full_text += "【段落标题】" + region_text + "\n";
                    } else {
                        full_text += region_text + "\n";
                    }

                    text_regions.append(layout_item);
                }
            }

            layout_info.append(layout_item);
        }

        // 构建返回结果
        result["layout_results"] = layout_info;
        result["text_regions"] = text_regions;
        result["full_text"] = full_text;
        result["total_regions"] = static_cast<int>(layout_results.size());
        result["text_regions_count"] = static_cast<int>(text_regions.size());

        return result;

    } catch (const std::exception& e) {
        std::cerr << "rapid_document_analysis exception: " << e.what() << std::endl;
        return py::dict();
    } catch (...) {
        std::cerr << "rapid_document_analysis unknown exception" << std::endl;
        return py::dict();
    }
}

// DocLayout 导出函数
py::list rapid_layout(py::array_t<uint8_t>& img_array) {
    static LayoutWrapper wrapper;
    return wrapper.rapid_layout(img_array);
}

// 使用指定 GPU 的 rapid_layout
py::list rapid_layout_with_params(py::array_t<uint8_t>& img_array, int gpu_id) {
    try {
        // 确保CUDA上下文正确
        cudaError_t cuda_status = cudaSetDevice(gpu_id);
        if (cuda_status != cudaSuccess) {
            std::cerr << "Failed to set CUDA device " << gpu_id << ": " << cudaGetErrorString(cuda_status) << std::endl;
            return py::list();
        }

        auto* layout_instance = MultiGPULayout::getInstance(gpu_id, 0.3f);
        if (!layout_instance) {
            std::cerr << "Failed to get Layout instance for GPU " << gpu_id << std::endl;
            return py::list();
        }

        // 验证实例的GPU ID
        if (layout_instance->getGpuId() != gpu_id) {
            std::cerr << "Layout instance GPU ID mismatch: expected " << gpu_id
                      << ", got " << layout_instance->getGpuId() << std::endl;
            return py::list();
        }

        // 转换 numpy array 到 cv::Mat
        py::buffer_info buf_info = img_array.request();
        cv::Mat img(buf_info.shape[0], buf_info.shape[1], CV_8UC3, (unsigned char*)buf_info.ptr);

        // 执行 Layout 检测
        auto layout_results = layout_instance->predict(img);

        // 转换结果为 Python list
        py::list result;
        for (const auto& layout_result : layout_results) {
            py::dict item;
            item["cls_id"] = layout_result.cls_id;
            item["label"] = layout_result.label;
            item["score"] = layout_result.score;
            item["coordinate"] = layout_result.coordinate;
            item["area"] = layout_result.area;
            result.append(item);
        }

        return result;
    } catch (const std::exception& e) {
        std::cerr << "rapid_layout_with_params exception: " << e.what() << std::endl;
        return py::list();
    } catch (...) {
        std::cerr << "rapid_layout_with_params unknown exception" << std::endl;
        return py::list();
    }
}


PYBIND11_MODULE(rec, m) {
    NDArrayConverter::init_numpy();
    // 注意：不在这里初始化实例，让Python代码控制初始化时机
    m.doc() = "Multi-GPU OCR module with version switching"; // optional module docstring

    // 导出底层类型
    py::class_<TrtOcr>(m, "TrtOcr")
        .def("getGpuId", &TrtOcr::getGpuId, "Get GPU ID of this OCR instance");

    py::class_<DocLayoutDetector>(m, "DocLayoutDetector")
        .def("getGpuId", &DocLayoutDetector::getGpuId, "Get GPU ID of this Layout instance");

    // 导出 MultiGPUOCR 类
    py::class_<MultiGPUOCR>(m, "MultiGPUOCR")
        .def_static("initInstance",
                   [](int gpu_id, const std::string& version) -> bool {
                       auto* instance = MultiGPUOCR::getInstance(gpu_id, version);
                       return instance != nullptr;
                   },
                   "Initialize OCR instance for specific GPU and version",
                   py::arg("gpu_id") = 0, py::arg("version") = "v5")
        .def_static("initInstance",
                   []() -> bool {
                       auto* instance = MultiGPUOCR::getInstance();
                       return instance != nullptr;
                   },
                   "Initialize default OCR instance")
        .def_static("finalize", &MultiGPUOCR::finalize,
                   "Release current TensorRT engines and CUDA resources")
        .def_static("finalizeAll", &MultiGPUOCR::finalizeAll,
                   "Release all TensorRT engines and CUDA resources");

    // 导出 MultiGPULayout 类
    py::class_<MultiGPULayout>(m, "MultiGPULayout")
        .def_static("initInstance",
                   [](int gpu_id, float conf_threshold) -> bool {
                       auto* instance = MultiGPULayout::getInstance(gpu_id, conf_threshold);
                       return instance != nullptr;
                   },
                   "Initialize Layout instance for specific GPU and confidence threshold",
                   py::arg("gpu_id") = 0, py::arg("conf_threshold") = 0.3f)
        .def_static("initInstance",
                   []() -> bool {
                       auto* instance = MultiGPULayout::getInstance();
                       return instance != nullptr;
                   },
                   "Initialize default Layout instance")
        .def_static("finalizeAll", &MultiGPULayout::finalizeAll,
                   "Release all Layout instances and resources");
    // 核心OCR函数
    m.def("rapid_ocr", &rapid_ocr, "Perform rapid OCR on image array");
    m.def("rapid_ocrm", &rapid_ocrm, "Perform rapid OCR on image");
    m.def("rapid_ocr_with_params", &rapid_ocr_with_params,
          "Perform rapid OCR with specific GPU and version",
          py::arg("img_array"), py::arg("gpu_id"), py::arg("version"));
    m.def("rapid_ocrm_with_params", &rapid_ocrm_with_params,
          "Perform rapid OCR on cv::Mat with specific GPU and version",
          py::arg("img"), py::arg("gpu_id"), py::arg("version"));

    // 检测专用函数
    m.def("detect_only", [](py::array_t<uint8_t>& arr){
        static OCRWrapper wrapper;
        return wrapper.detect_only(arr);
    }, "Detect text boxes only (no recognition)");

    // DocLayout 相关函数
    m.def("rapid_layout", &rapid_layout, "Perform rapid layout analysis on image array");
    m.def("rapid_layout_with_params", &rapid_layout_with_params,
          "Perform rapid layout analysis with specific GPU",
          py::arg("img_array"), py::arg("gpu_id"));

    // 文档分析综合函数
    m.def("rapid_document_analysis", &rapid_document_analysis,
          "Perform comprehensive document analysis (layout + OCR)",
          py::arg("img_array"), py::arg("gpu_id") = 0);

    // 在 Python 解释器退出时优雅释放资源，避免 TensorRT 在 CUDA 已销毁时清理引发的内部错误
    m.add_object("__cleanup__", py::capsule([](){
        try {
            MultiGPUOCR::finalizeAll();
            MultiGPULayout::finalizeAll();
        } catch(...) {}
    }));
}
