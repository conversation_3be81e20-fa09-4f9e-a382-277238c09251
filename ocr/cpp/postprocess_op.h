

#pragma once

#include "opencv2/core.hpp"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
#include <chrono>
#include <iomanip>
#include <iostream>
#include <ostream>
#include <vector>

#include <cstring>
#include <fstream>
#include <numeric>

#include "utility.h"
#include "clipper.h"

using namespace std;

namespace OCR {
std::vector<std::vector<int>> OrderPointsClockwise(std::vector<std::vector<int>> pts);
std::vector<std::vector<std::vector<int>>> box4rec(std::vector<std::vector<std::vector<int>>> boxes,cv::Mat& img);
std::vector<std::vector<std::vector<int>>> box2src(std::vector<std::vector<std::vector<int>>>& boxes, cv::Mat& srcimg);

using namespace cv;
using namespace std;
using namespace ClipperLib;

vector<vector<float>> mat_to_vector(const Mat& mat);

tuple<vector<vector<float>>, float> get_mini_boxes(const cv::RotatedRect& box);

float box_score(const Mat& pred, const vector<vector<float>>& box_array);

float get_contour_area(const vector<vector<float>>& box, float unclip_ratio);

cv::RotatedRect box_unclip(const vector<vector<float>>& box, float unclip_ratio);

void boxes_from_bitmap(const Mat& pred, const Mat& bitmap, vector<vector<vector<int>>>& box_array,
    float box_thresh, float unclip_ratio, int min_size, int max_candidates);

vector<vector<int>> order_points_clockwise(vector<vector<int>>& box);

vector<vector<vector<int>>> filter_boxes(vector<vector<vector<int>>>& boxes, int src_h, int src_w, float ratio_h, float ratio_w);

// NMS functions
float calculate_iou(const vector<vector<int>>& box1, const vector<vector<int>>& box2);
vector<vector<vector<int>>> apply_nms(vector<vector<vector<int>>>& boxes, float nms_threshold = 0.3);

void detector_postprocess(const Mat& pred_map, vector<vector<vector<int>>>& boxes, int src_h, int src_w, int dst_h,
    int dst_w, float mask_thresh, float box_thresh, float unclip_ratio, int min_size, int max_candidates);




} // namespace OCR
