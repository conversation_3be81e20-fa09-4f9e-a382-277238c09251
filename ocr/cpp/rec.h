# include <NvInfer.h>
# include "logging.h"
# include "postprocess_op.h"
# include "preprocess_op.h"
# include <opencv2/opencv.hpp>
# include "utility.h"
# include "ocr_dict.h"
# include <cuda_runtime.h>
# include <cstdlib>
# include <string>
# include <algorithm>

using namespace cv;

#define CHECK(status) \
    do\
    {\
        auto ret = (status);\
        if (ret != 0)\
        {\
            std::cerr << "Cuda failure: " << ret << std::endl;\
            abort();\
        }\
    } while (0)

namespace OCR {

class TextRec {
public:
    TextRec(const string& version = "v5", int gpu_id = 0)
    : version_(version), gpu_id_(gpu_id)
    {
        // 使用精简配置
        auto& config = getOCRConfig();

        // 固定的识别模型配置
        this->rec_img_h_ = 48;
        this->rec_img_w_ = 640;
        this->rec_batch_num_ = 1;  // 固定 batch=1
        this->rec_image_shape_ = {3, rec_img_h_, rec_img_w_};

        // 设置 GPU
        cudaSetDevice(gpu_id_);

        // 根据版本选择字典：使用传入的版本参数而不是全局配置
        // getV4Dict() 和 getV5Dict() 已经包含了所有必要的字符，包括 "#", 字典内容, " ", ""
        if (version_ == "v5") {
            this->label_list_ = getV5Dict();
        } else {
            this->label_list_ = getV4Dict();
        }

        // 打印配置信息
        std::cout << "[TextRec] Using " << version_
                  << " model on GPU " << gpu_id_
                  << ", shape: " << rec_img_h_ << "x" << rec_img_w_
                  << ", batch: " << rec_batch_num_ << std::endl;
    };

    // 使用精简路径的初始化方法
    int Model_Init() {
        auto& config = getOCRConfig();
        std::string engine_path = config.getEnginePath("rec");
        return Model_Init(engine_path);
    }

    int Model_Init(string& engine_path);
    void Model_Infer(vector<cv::Mat> Input_Image, vector<pair<string, double> > &rec_res, vector<int>& idx_map);
    ~TextRec();

private:
    string version_;  // 模型版本
    int gpu_id_ = 0;  // GPU 设备 ID
    //task
    std::vector<std::string> label_list_;
    // input/output layer 
    const char *INPUT_BLOB_NAME = "images";
    const char *OUTPUT_BLOB_NAME= "output";
    // input image

    int rec_img_h_ = 48;
    int rec_img_w_ = 640;
    std::vector<int> rec_image_shape_ = {3, rec_img_h_, rec_img_w_};
    int rec_batch_num_=8;
    // 与 v5 / infer.py 对齐的归一化
    std::vector<float> mean_ = {0.5f, 0.5f, 0.5f};
    std::vector<float> scale_ = {2.0f, 2.0f, 2.0f};


    // TensorRT handles (simplified, since we removed Convert)
    nvinfer1::IRuntime* m_runtime{nullptr};
    nvinfer1::ICudaEngine* m_engine{nullptr};
    nvinfer1::IExecutionContext* m_context{nullptr};
};

}// namespace OCR