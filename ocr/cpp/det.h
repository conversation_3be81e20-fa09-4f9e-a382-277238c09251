# include <NvInfer.h>
# include "logging.h"
# include "postprocess_op.h"
# include "preprocess_op.h"
# include <opencv2/opencv.hpp>
# include <cstdlib>
# include <string>
# include <algorithm>

using namespace cv;

#define CHECK(status) \
    do\
    {\
        auto ret = (status);\
        if (ret != 0)\
        {\
            std::cerr << "Cuda failure: " << ret << std::endl;\
            abort();\
        }\
    } while (0)

namespace OCR {



class TextDetect{
public:
    TextDetect(int gpu_id = 0)
    : gpu_id_(gpu_id)
    {
        // 使用精简配置
        auto& config = getOCRConfig();

        this->max_side_len_ = 960;  // 固定使用 960

        // 设置 GPU
        cudaSetDevice(gpu_id_);

        // 打印配置信息
        std::cout << "[TextDetect] Using " << config.version
                  << " model on GPU " << gpu_id_
                  << ", max_side: " << max_side_len_ << std::endl;
    };

    // 使用统一路径的初始化方法
    int Model_Init() {
        auto& config = getOCRConfig();
        std::string engine_path = config.getEnginePath("det");
        return Model_Init(engine_path);
    }

    int Model_Init(string& engine_path);
    void Model_Infer(cv::Mat& Input_Image, vector<vector<vector<int>>> &boxes);
    ~TextDetect();

private:
    //config
    int gpu_id_ = 0;  // GPU 设备 ID

    //task
    double det_db_thresh_ = 0.3;
    double det_db_box_thresh_ = 0.5;
    double det_db_unclip_ratio_ = 2.0;
    bool use_polygon_score_ = false;

    // input/output layer 
    const char *INPUT_BLOB_NAME = "images";
    //const char *OUTPUT_BLOB_NAME = "sigmoid_0.tmp_0";
    const char *OUTPUT_BLOB_NAME = "output";
    // input image
    int max_side_len_ = 320;  // 将在构造函数中设置

    vector<float> mean_ = {0.485f, 0.456f, 0.406f};
    vector<float> scale_ = {1 / 0.229f, 1 / 0.224f, 1 / 0.225f};

    // ===== TensorRT runtime and buffers (merged from TextDetector) =====
    nvinfer1::ICudaEngine* m_engine{nullptr};
    nvinfer1::IExecutionContext* m_context{nullptr};
    nvinfer1::IRuntime* m_runtime{nullptr};
    cudaStream_t m_stream{};
    int m_inputIndex{-1};
    int m_outputIndex{-1};
    int m_inC{3}, m_inH{0}, m_inW{0};
    int m_outC{1}, m_outH{0}, m_outW{0};
    int m_origW{0}, m_origH{0};
    double m_scaleX{1.0}, m_scaleY{1.0};
    int m_offsetX{0}, m_offsetY{0};
    size_t m_outputNumel{0};

    int m_allocatedInH{0}, m_allocatedInW{0};
    int m_allocatedOutH{0}, m_allocatedOutW{0};
    float* m_inputDeviceBuffer{nullptr};
    float* m_outputDeviceBuffer{nullptr};
    float* m_inputBuffer{nullptr};
    float* m_outputBuffer{nullptr};

    cv::Mat m_inputImage;
    std::vector<std::vector<cv::Point>> m_polys4;

    // ===== Methods (merged) =====
    bool allocateBuffers(int inH, int inW, int outH, int outW);
    void freeBuffers();
    void preprocess(const cv::Mat& image);
    void forward();
    void postprocess();
    bool infer(const cv::Mat& image);
    std::vector<std::vector<cv::Point>> getPolys();
    float calculate_iou_cv(const std::vector<cv::Point>& box1, const std::vector<cv::Point>& box2);
    std::vector<std::vector<cv::Point>> apply_nms_cv(std::vector<std::vector<cv::Point>>& boxes, float nms_threshold);

};

}// namespace OCR

