cmake_minimum_required(VERSION 3.11)
project(trtrec)

add_definitions(-std=c++11)

option(CUDA_USE_STATIC_CUDA_RUNTIME OFF)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_BUILD_TYPE Debug)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -g3")
set(CMAKE_BUILD_TYPE Debug)

# 添加当前目录到包含路径，以便找到 ocr_dict.h 和 utility.h
include_directories(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${PROJECT_SOURCE_DIR}/include)
# include and link dirs of cuda and tensorrt, you need adapt them if yours are different
# cuda cudnn
include_directories(/usr/local/cuda/include)
link_directories(/usr/local/cuda/lib64)
# tensorrt
include_directories("/usr/local/tensorrt/include")
link_directories("/usr/local/tensorrt/lib")

# project
include_directories(include)

#set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11 -Wall -O0 -Wfatal-errors -D_MWAITXINTRIN_H_INCLUDED")

# opencv
find_package(OpenCV REQUIRED)
MESSAGE( " *** OpenCV_INCLUDE_DIRS : " ${OpenCV_INCLUDE_DIRS} )
MESSAGE( " *** OpenCV_LIB_DIRS : " ${OpenCV_LIB_DIRS} )
MESSAGE( " *** OpenCV_LIBS : " ${OpenCV_LIBS} )

# find_package(pybind11 CONFIG REQUIRED)
# MESSAGE( " *** PYTHON_INCLUDE_DIRS : " ${PYTHON_INCLUDE_DIRS} )
# MESSAGE( " *** PYTHON_LIBRARIES : " ${PYTHON_LIBRARIES} )

include_directories(${OpenCV_INCLUDE_DIRS})

# add_executable(rec4 Convert.cpp 
#                 det.cpp 
#                 rec.cpp 
#                 ocr.cpp 
#                 postprocess_op.cpp 
#                 preprocess_op.cpp 
#                 utility.cpp 
#                 main2.cpp)
# target_link_libraries(rec4 nvinfer nvonnxparser)
# target_link_libraries(rec4 cudart)
# target_link_libraries(rec4 ${OpenCV_LIBS})
#add_definitions(-O2 -pthread)

set(NUMPY_INCLUDE_DIR "/usr/local/lib/python3.10/dist-packages/numpy/core/include")
include_directories(${NUMPY_INCLUDE_DIR})
set(PYTHON3_INC_DIR "/usr/include/python3.10/" CACHE PATH "")
set(PYTHON EXECUTABLE "/usr/bin/python3.10")
include_directories("/usr/include/python3.10")
# cmake_policy(SET CMP0148 NEW)
find_package(Python 3.10 COMPONENTS Interpreter Development REQUIRED)


# find_package(pybind11 REQUIRED) 
add_subdirectory(pybind11)
# pybind11_add_module(trtocr main.cpp)

add_library(rec MODULE utility.cpp
                det.cpp
                rec.cpp
                ocr.cpp
                layout.cpp
                postprocess_op.cpp
                preprocess_op.cpp
                ndarray_converter.cpp
                main.cpp)

target_link_libraries(rec pybind11::module pybind11::lto pybind11::windows_extras)
target_link_libraries(rec nvinfer nvonnxparser nvinfer_plugin)
target_link_libraries(rec cudart)
target_link_libraries(rec ${OpenCV_LIBS})
pybind11_extension(rec)
if(NOT MSVC AND NOT ${CMAKE_BUILD_TYPE} MATCHES Debug|RelWithDebInfo)
    # Strip unnecessary sections of the binary on Linux/macOS
    pybind11_strip(rec)
endif()

set_target_properties(rec PROPERTIES
    CXX_VISIBILITY_PRESET "hidden"
    CUDA_VISIBILITY_PRESET "hidden"
    # 设置输出名称为 rec.so（去掉 Python 版本后缀）
    OUTPUT_NAME "rec"
    SUFFIX ".so"
)

# 添加自定义命令：编译完成后自动拷贝到 /workspace/hngpt 目录
add_custom_command(TARGET rec POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy
        $<TARGET_FILE:rec>
        /workspace/hngpt/rec.so
    COMMENT "Copying rec.so to /workspace/hngpt/"
)