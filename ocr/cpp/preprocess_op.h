
#pragma once

#include "opencv2/core.hpp"
#include "opencv2/imgcodecs.hpp"
#include "opencv2/imgproc.hpp"
#include <iostream>
#include <vector>
#include <cstring>

using namespace std;

namespace OCR {

// 🚀 简化的预处理函数架构 - 替换复杂的类

// 归一化函数
void normalize_image(cv::Mat *im, const std::vector<float> &mean,
                    const std::vector<float> &scale, const bool is_scale = true);

// HWC -> CHW 转换
void permute_hwc_to_chw(const cv::Mat *im, float *data);

// 批量 HWC -> CHW 转换
void permute_batch_hwc_to_chw(const std::vector<cv::Mat> imgs, float *data);

// 检测模型resize - 支持动态尺寸
void resize_for_detection(const cv::Mat &img, cv::Mat &resize_img, int target_size,
                         float &ratio_h, float &ratio_w);

// 识别模型resize
void resize_for_recognition(const cv::Mat &img, cv::Mat &resize_img, float wh_ratio,
                           const std::vector<int> &rec_image_shape = {3, 48, 640});

// 分类模型resize
void resize_for_classification(const cv::Mat &img, cv::Mat &resize_img,
                              const std::vector<int> &rec_image_shape = {3, 48, 192});

// 通用resize with padding
void resize_with_padding(const cv::Mat &img, cv::Mat &resize_img, int target_size = 960);

// 一体化预处理函数
void resize_and_normalize(const cv::Mat &img, cv::Mat &resize_img, int target_size = 960);

} // namespace OCR