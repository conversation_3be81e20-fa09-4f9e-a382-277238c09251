# include "det.h"
# include "rec.h"

using namespace OCR;

class TrtOcr{
public:
    TrtOcr(const string& version = "v5", int gpu_id = 0) : version_(version), gpu_id_(gpu_id) {
        // 默认使用v5模型和GPU 0
        // 可以通过参数显式指定版本和GPU ID
    };
    void Model_Init(string det_engine_path, string rec_engine_path);  // 只需要engine路径
    void Model_Init();  // 根据版本自动选择模型路径
    // End-to-end OCR (detect+recognize) - 简化接口，移除时间参数
    std::vector<std::tuple<std::vector<std::vector<int>>, std::string, double>> Model_Infer(cv::Mat& inputImg);
    // Detect only (return all detected quadrilaterals, no recognition filtering) - 简化接口，移除时间参数
    std::vector<std::vector<std::vector<int>>> Model_Detect(cv::Mat& inputImg);
    string TaskProcess(vector<pair< vector<string>, double>> &result);
    string MultiFrameSmooth(string door_result, int step);
    ~TrtOcr();

    // 获取版本和GPU信息
    string getVersion() const { return version_; }
    bool isV5() const { return version_ == "v5"; }
    int getGpuId() const { return gpu_id_; }

private:
    string version_;  // 模型版本 "v4" 或 "v5"
    int gpu_id_;      // GPU设备ID

    TextDetect * td = NULL;
    TextRec * tr = NULL;

    // tast
    vector<int> REC_RANGE_ = {400, 599};
    float REC_THR_ = 0.85;

    //MultiFrameSmooth
    int count_img_ = 0;
    unordered_map<string, int> results_;

    bool visualize_= false;
    int count_name_ = 0;
};