FormatStyle: file

Checks: |
  *bugprone*,
  *performance*,
  clang-analyzer-optin.cplusplus.VirtualCall,
  clang-analyzer-optin.performance.Padding,
  cppcoreguidelines-init-variables,
  cppcoreguidelines-prefer-member-initializer,
  cppcoreguidelines-pro-type-static-cast-downcast,
  cppcoreguidelines-slicing,
  google-explicit-constructor,
  llvm-namespace-comment,
  misc-definitions-in-headers,
  misc-misplaced-const,
  misc-non-copyable-objects,
  misc-static-assert,
  misc-throw-by-value-catch-by-reference,
  misc-uniqueptr-reset-release,
  misc-unused-parameters,
  modernize-avoid-bind,
  modernize-loop-convert,
  modernize-make-shared,
  modernize-redundant-void-arg,
  modernize-replace-auto-ptr,
  modernize-replace-disallow-copy-and-assign-macro,
  modernize-replace-random-shuffle,
  modernize-shrink-to-fit,
  modernize-use-auto,
  modernize-use-bool-literals,
  modernize-use-default-member-init,
  modernize-use-emplace,
  modernize-use-equals-default,
  modernize-use-equals-delete,
  modernize-use-noexcept,
  modernize-use-nullptr,
  modernize-use-override,
  modernize-use-using,
  readability-avoid-const-params-in-decls,
  readability-braces-around-statements,
  readability-const-return-type,
  readability-container-size-empty,
  readability-delete-null-pointer,
  readability-else-after-return,
  readability-implicit-bool-conversion,
  readability-inconsistent-declaration-parameter-name,
  readability-make-member-function-const,
  readability-misplaced-array-index,
  readability-non-const-parameter,
  readability-qualified-auto,
  readability-redundant-function-ptr-dereference,
  readability-redundant-smartptr-get,
  readability-redundant-string-cstr,
  readability-simplify-subscript-expr,
  readability-static-accessed-through-instance,
  readability-static-definition-in-anonymous-namespace,
  readability-string-compare,
  readability-suspicious-call-argument,
  readability-uniqueptr-delete-release,
  -bugprone-easily-swappable-parameters,
  -bugprone-exception-escape,
  -bugprone-reserved-identifier,
  -bugprone-unused-raii,

CheckOptions:
- key:             modernize-use-equals-default.IgnoreMacros
  value:           false
- key:             performance-for-range-copy.WarnOnAllAutoCopies
  value:           true
- key:             performance-inefficient-string-concatenation.StrictMode
  value:           true
- key:             performance-unnecessary-value-param.AllowedTypes
  value:           'exception_ptr$;'
- key:             readability-implicit-bool-conversion.AllowPointerConditions
  value:           true

HeaderFilterRegex: 'pybind11/.*h'
