#include <iostream>
#include <fstream>
#include <vector>
#include <string>
#include <chrono>
#include <opencv2/opencv.hpp>
#include <NvInfer.h>
#include <NvOnnxParser.h>
#include <cuda_runtime.h> // Include CUDA runtime API header
#include <cstdlib> // for std::getenv

#include "clipper.h"  // ClipperLib for robust polygon offset (unclip)
#include <array>
#include "det.h"

using namespace std;
// Dynamic shape support - remove fixed constants
// These will be determined dynamically based on input
// Env reader stubs to eliminate environment impact while keeping existing calls compile-time safe
static inline int read_env_i(const char* /*name*/, int defv){ return defv; }
static inline float read_env_f(const char* /*name*/, float defv){ return defv; }

// 固定常量，对齐 infer-trt.py；不再读取环境变量以避免不一致
constexpr int   DET_SIZE      = 960;
constexpr float MASK_THRESH   = 0.3f;
constexpr float BOX_THRESH    = 0.6f;
constexpr int   MAX_CANDIDATES= 1000;
constexpr int   MIN_SIZE      = 3;
constexpr float UNCLIP_RATIO  = 1.5f;

// Dynamic shape selection function
static std::pair<int, int> selectOptimalDetectionSize(int orig_h, int orig_w, int target_size = 0) {
    if (target_size == 0) target_size = DET_SIZE;

    // Support dynamic shapes: 320, 640, 960, 1280, 1536
    std::vector<int> supported_sizes = {320, 640, 960, 1280, 1536};

    // If target_size is specified and supported, use it directly
    for (int size : supported_sizes) {
        if (size == target_size) {
            std::cout << "[Det] Using target size: " << target_size << std::endl;
            return {target_size, target_size};
        }
    }

    // Otherwise, find the best size based on input image dimensions
    int max_dim = std::max(orig_h, orig_w);
    int selected_size = supported_sizes.back(); // default to largest

    for (int size : supported_sizes) {
        if (max_dim <= size) {
            selected_size = size;
            break;
        }
    }

    std::cout << "[Det] Auto-selected size for " << orig_w << "x" << orig_h << ": " << selected_size << std::endl;
    return {selected_size, selected_size};
}
// --- Robust polygon offset (Clipper) wrappers for db_unclip ---
static std::vector<cv::Point> db_unclip_polygon(const std::vector<cv::Point>& contour, float unclip_ratio) {
    if (contour.size() < 3) return contour;
    const double SCALE = 100.0;
    ClipperLib::Path p; p.reserve(contour.size());
    for (auto &pt : contour) p << ClipperLib::IntPoint((ClipperLib::cInt)std::llround(pt.x * SCALE), (ClipperLib::cInt)std::llround(pt.y * SCALE));
    double area = 0.0, perim = 0.0;
    for (size_t i=0;i<p.size();++i) {
        auto a = p[i]; auto b = p[(i+1)%p.size()];
        area += (double)a.X * (double)b.Y - (double)a.Y * (double)b.X;
        perim += std::hypot((double)b.X - a.X, (double)b.Y - a.Y);
    }
    area = std::fabs(area * 0.5);
    double d = std::max(1e-6, area * (double)unclip_ratio / std::max(1e-6, perim));
    ClipperLib::ClipperOffset co; co.AddPath(p, ClipperLib::jtRound, ClipperLib::etClosedPolygon);
    ClipperLib::Paths solution; co.Execute(solution, d);
    if (solution.size() != 1) return {};
    std::vector<cv::Point> out; out.reserve(solution[0].size());
    for (auto &ip : solution[0]) out.emplace_back((int)std::lround((double)ip.X / SCALE), (int)std::lround((double)ip.Y / SCALE));
    return out;
}
// 浮点输入版本，避免对 seed 四点提前取整导致的外扩失败
static std::vector<cv::Point2f> db_unclip_polygon_f(const std::vector<cv::Point2f>& contour, float unclip_ratio) {
    if (contour.size() < 3) return contour;
    const double SCALE = 100.0;
    ClipperLib::Path p; p.reserve(contour.size());
    for (auto &pt : contour) p << ClipperLib::IntPoint((ClipperLib::cInt)std::llround(pt.x * SCALE), (ClipperLib::cInt)std::llround(pt.y * SCALE));
    double area = 0.0, perim = 0.0;
    for (size_t i=0;i<p.size();++i) {
        auto a = p[i]; auto b = p[(i+1)%p.size()];
        area += (double)a.X * (double)b.Y - (double)a.Y * (double)b.X;
        perim += std::hypot((double)b.X - a.X, (double)b.Y - a.Y);
    }
    area = std::fabs(area * 0.5);
    double d = std::max(1e-6, area * (double)unclip_ratio / std::max(1e-6, perim));
    ClipperLib::ClipperOffset co; co.AddPath(p, ClipperLib::jtRound, ClipperLib::etClosedPolygon);
    ClipperLib::Paths solution; co.Execute(solution, d);
    if (solution.size() != 1) return {};
    std::vector<cv::Point2f> out; out.reserve(solution[0].size());
    for (auto &ip : solution[0]) out.emplace_back((float)((double)ip.X / SCALE), (float)((double)ip.Y / SCALE));
    return out;
}
// --- end robust wrappers ---
// Order 4 points clockwise: TL, TR, BR, BL (like infer.py)
static std::array<cv::Point,4> order_points_clockwise_cv(const std::vector<cv::Point>& quad) {
    std::array<cv::Point,4> rect{cv::Point(0,0),cv::Point(0,0),cv::Point(0,0),cv::Point(0,0)};
    if (quad.size() != 4) return rect;
    std::array<cv::Point2f,4> ptsf;
    for (int i=0;i<4;++i) ptsf[i] = cv::Point2f((float)quad[i].x, (float)quad[i].y);
    float sums[4], diffs[4];
    for (int i=0;i<4;++i) { sums[i] = ptsf[i].x + ptsf[i].y; diffs[i] = ptsf[i].x - ptsf[i].y; }
    int idx_tl = 0, idx_br = 0;
    for (int i=1;i<4;++i) { if (sums[i] < sums[idx_tl]) idx_tl = i; if (sums[i] > sums[idx_br]) idx_br = i; }
    int rem[2]; int r=0; for (int i=0;i<4;++i) if (i!=idx_tl && i!=idx_br) rem[r++]=i;
    int idx_tr = rem[0], idx_bl = rem[1];
    if (diffs[idx_tr] > diffs[idx_bl]) {
        // ok
    } else {
        std::swap(idx_tr, idx_bl);
    }
    rect[0] = cv::Point((int)std::round(ptsf[idx_tl].x), (int)std::round(ptsf[idx_tl].y));
    rect[1] = cv::Point((int)std::round(ptsf[idx_tr].x), (int)std::round(ptsf[idx_tr].y));
    rect[2] = cv::Point((int)std::round(ptsf[idx_br].x), (int)std::round(ptsf[idx_br].y));
    rect[3] = cv::Point((int)std::round(ptsf[idx_bl].x), (int)std::round(ptsf[idx_bl].y));
    return rect;
}





// Define Logger instance
Logger gLogger;

// Define CUDA error check macro
#define CUDA_CHECK(call) \
{ \
    cudaError_t err = call; \
    if (err != cudaSuccess) \
    { \
        fprintf(stderr, "CUDA error at %s:%d code=%d(%s) \"%s\" \n", __FILE__, __LINE__, err, cudaGetErrorName(err), cudaGetErrorString(err)); \
        exit(EXIT_FAILURE); \
    } \
}
namespace OCR {

// TextDetect class implementation (merged from TextDetector)

int TextDetect::Model_Init(string& engine_path) {
    std::cout << "TextDetect::Model_Init called with engine: " << engine_path << std::endl;
    std::cout << "[Det] 🚀 TextDetect initialization started with: " << engine_path << std::endl;
    std::cout.flush();

    try {

        std::string trtModelPath = engine_path.substr(0, engine_path.find_last_of(".")) + ".trt";
        std::cout << "[Det] TRT model path: " << trtModelPath << std::endl;

        // Check if the .trt file already exists
        std::ifstream trtFile(trtModelPath, std::ios::binary);
        if (trtFile.good()) {
            std::cout << "[Det] ✅ TRT file exists, loading directly..." << std::endl;
            // .trt file exists, so we will load it directly
            trtFile.close();

            // Load serialized TRT engine from file
            trtFile.open(trtModelPath, std::ios::binary);
            if (!trtFile.is_open()) {
                std::cerr << "Failed to open TRT file: " << trtModelPath << std::endl;
                exit(1);
            }

            trtFile.seekg(0, std::ios::end);
            int trtFileSize = trtFile.tellg();
            trtFile.seekg(0, std::ios::beg);
            char* trtBuffer = new char[trtFileSize];
            trtFile.read(trtBuffer, trtFileSize);
            trtFile.close();

            m_runtime = nvinfer1::createInferRuntime(gLogger);
            m_engine = m_runtime->deserializeCudaEngine(trtBuffer, trtFileSize, nullptr);
            m_context = m_engine->createExecutionContext();

            // Enumerate bindings to find input/output and dimensions
            int nb = m_engine->getNbBindings();
            for (int i = 0; i < nb; ++i) {
                auto dims = m_engine->getBindingDimensions(i);
                const char* name = m_engine->getBindingName(i);
                bool isInput = m_engine->bindingIsInput(i);
                std::cout << "[Det] Binding " << i << " (" << (name?name:"?") << ") is "
                          << (isInput?"INPUT":"OUTPUT") << ", dims nbDims=" << dims.nbDims << std::endl;
                if (isInput) m_inputIndex = i; else m_outputIndex = i;
            }
            printf("[Det] DEBUG: After binding loop\n");
            fflush(stdout);
            std::cout << "[Det] 🔍 Starting dimension analysis..." << std::endl;

            auto inDims = m_engine->getBindingDimensions(m_inputIndex);
            auto outDims = m_engine->getBindingDimensions(m_outputIndex);

            fprintf(stderr, "[Det] Input dims: [%d, %d, %d, %d]\n", inDims.d[0], inDims.d[1], inDims.d[2], inDims.d[3]);

            // Dynamic shape support
            m_inC = (inDims.nbDims>=4)? inDims.d[1] : 3;

            // Check for dynamic dimensions (-1 indicates dynamic)
            bool isDynamic = (inDims.nbDims >= 4 && (inDims.d[2] == -1 || inDims.d[3] == -1));

            fprintf(stderr, "[Det] isDynamic = %s, m_inC = %d\n", isDynamic ? "true" : "false", m_inC);

            // FORCE FIXED MODE FOR STABILITY
            isDynamic = false; // Force fixed 960x960 mode
            m_inH = m_inW = 960; // Fixed 960x960 input
            fprintf(stderr, "[Det] ✅ FORCED Fixed shape mode: 960x960\n");

            // Original logic (commented out for testing)
            /*
            if (isDynamic) {
                // Dynamic shape engine
                m_inH = m_inW = 0; // Will be set during inference
                fprintf(stderr, "[Det] ✅ Dynamic shape engine detected, m_inW=%d, m_inH=%d\n", m_inW, m_inH);
            } else {
                // Fixed shape engine
                m_inH = (inDims.nbDims>=4)? inDims.d[2] : 960;
                m_inW = (inDims.nbDims>=4)? inDims.d[3] : 960;
                fprintf(stderr, "[Det] Fixed shape engine: %dx%d\n", m_inW, m_inH);
            }
            */

            m_outC = (outDims.nbDims>=4)? outDims.d[1] : 1;
            // Fixed output dimensions for 960x960 input
            m_outH = (outDims.nbDims>=4)? outDims.d[2] : 960;
            m_outW = (outDims.nbDims>=4)? outDims.d[3] : 960;

            // Fixed shape engine - allocate buffers now
            allocateBuffers(m_inH, m_inW, m_outH, m_outW);
            m_outputNumel = static_cast<size_t>(m_outC) * m_outH * m_outW;
            std::cout << "[Det] Fixed shape buffers allocated: " << m_inW << "x" << m_inH << std::endl;
            // 预创建单个 stream，复用
            CUDA_CHECK(cudaStreamCreate(&m_stream));

            // Destroy temporary objects
            delete[] trtBuffer;
        } else {
            // ONNX to TensorRT build path - will be handled after engine creation

            // .trt file does not exist, so we will build it from the .onnx file
            trtFile.close();

            // Load ONNX model
            std::ifstream modelFile(engine_path, std::ios::binary);
            if (!modelFile.is_open()) {
                std::cerr << "Failed to open model file: " << engine_path << std::endl;
                return -1;
            }

            // Create TensorRT engine
            nvinfer1::IBuilder* builder = nvinfer1::createInferBuilder(gLogger);
            auto explicitBatch = 1U << static_cast<uint32_t>(nvinfer1::NetworkDefinitionCreationFlag::kEXPLICIT_BATCH);
            nvinfer1::INetworkDefinition* network = builder->createNetworkV2(explicitBatch);
            nvonnxparser::IParser* parser = nvonnxparser::createParser(*network, gLogger);

            // Parse the ONNX model
            modelFile.seekg(0, std::ios::end);
            int modelSize = modelFile.tellg();
            modelFile.seekg(0, std::ios::beg);
            char* modelBuffer = new char[modelSize];
            modelFile.read(modelBuffer, modelSize);
            modelFile.close();

            parser->parse(modelBuffer, modelSize);

            // Get the first input tensor
            nvinfer1::ITensor* inputTensor = network->getInput(0);
            nvinfer1::Dims inputDims = inputTensor->getDimensions();
            std::cout << "Input tensor dimensions: " << inputDims.nbDims << " dims" << std::endl;
            // Set the input dimensions
            inputDims.d[0] = -1; // batch size can vary
            inputDims.d[1] = 3;  // RGB images
            inputDims.d[2] = 960;  // Default height
            inputDims.d[3] = 960;  // Default width
            inputTensor->setDimensions(inputDims);

            // Build the engine
            builder->setMaxBatchSize(1);
            nvinfer1::IOptimizationProfile* profile = builder->createOptimizationProfile();
            profile->setDimensions(inputTensor->getName(), nvinfer1::OptProfileSelector::kMIN, nvinfer1::Dims4(1, 3, 320, 320));
            profile->setDimensions(inputTensor->getName(), nvinfer1::OptProfileSelector::kOPT, nvinfer1::Dims4(1, 3, 640, 640));
            profile->setDimensions(inputTensor->getName(), nvinfer1::OptProfileSelector::kMAX, nvinfer1::Dims4(1, 3, 960, 960));

            nvinfer1::IBuilderConfig* config = builder->createBuilderConfig();
            config->addOptimizationProfile(profile);
            if (builder->platformHasFastFp16()) {
                config->setFlag(nvinfer1::BuilderFlag::kFP16);
            }
            nvinfer1::ICudaEngine* engine = builder->buildEngineWithConfig(*network, *config);

            // Serialize the engine to a file
            nvinfer1::IHostMemory* engineData = engine->serialize();
            std::ofstream trtFile(trtModelPath, std::ios::binary);
            if (!trtFile.is_open()) {
                std::cerr << "Failed to open TRT file: " << trtModelPath << std::endl;
                exit(1);
            }
            trtFile.write(reinterpret_cast<const char*>(engineData->data()), engineData->size());
            trtFile.close();
            engineData->destroy();
            // Build engine done; assign
            m_runtime = nvinfer1::createInferRuntime(gLogger);
            m_engine = engine;
            m_context = m_engine->createExecutionContext();

            // Enumerate bindings
            int nb2 = m_engine->getNbBindings();
            for (int i = 0; i < nb2; ++i) {
                auto dims = m_engine->getBindingDimensions(i);
                const char* name = m_engine->getBindingName(i);
                bool isInput = m_engine->bindingIsInput(i);
                std::cout << "[Det] Binding " << i << " (" << (name?name:"?") << ") is "
                          << (isInput?"INPUT":"OUTPUT") << ", dims nbDims=" << dims.nbDims << std::endl;
                if (isInput) m_inputIndex = i; else m_outputIndex = i;
            }
            // Handle dynamic shape for ONNX-built engine
            auto inDims2 = m_engine->getBindingDimensions(m_inputIndex);
            auto outDims2 = m_engine->getBindingDimensions(m_outputIndex);
            m_inC = (inDims2.nbDims>=4)? inDims2.d[1] : 3;

            // Check for dynamic dimensions
            bool isDynamic2 = (inDims2.nbDims >= 4 && (inDims2.d[2] == -1 || inDims2.d[3] == -1));

            // FORCE DYNAMIC MODE FOR TESTING (ONNX branch)
            isDynamic2 = true; // Force dynamic detection
            m_inH = m_inW = 0; // Force dynamic mode
            fprintf(stderr, "[Det] ✅ FORCED ONNX Dynamic shape mode, isDynamic2=%s, m_inW=%d, m_inH=%d\n",
                   isDynamic2 ? "true" : "false", m_inW, m_inH);

            m_outC = (outDims2.nbDims>=4)? outDims2.d[1] : 1;
            if (isDynamic2) {
                m_outH = m_outW = 0;
            } else {
                m_outH = (outDims2.nbDims>=4)? outDims2.d[2] : 960;
                m_outW = (outDims2.nbDims>=4)? outDims2.d[3] : 960;
            }

            // Allocate buffers for ONNX-built engine
            if (isDynamic2) {
                std::cout << "[Det] 🔄 ONNX-built dynamic shape - buffers will be allocated during inference" << std::endl;
            } else {
                // Fixed shape engine - allocate buffers now
                allocateBuffers(m_inH, m_inW, m_outH, m_outW);
                m_outputNumel = static_cast<size_t>(m_outC) * m_outH * m_outW;
                std::cout << "[Det] ONNX-built fixed shape buffers allocated: " << m_inW << "x" << m_inH << std::endl;
            }
            // 预创建单个 stream，复用
            CUDA_CHECK(cudaStreamCreate(&m_stream));

            // Destroy temporary objects
            delete[] modelBuffer;
            parser->destroy();
            network->destroy();
            builder->destroy();
            config->destroy();
        }

        std::cout << "TextDetect: Successfully initialized with TensorRT engine: " << engine_path << std::endl;
        return 0;
    } catch (const std::exception& e) {
        std::cout << "TextDetect: Error initializing model: " << e.what() << std::endl;
        return -1;
    }
}

TextDetect::~TextDetect() {
    freeBuffers(); // Use the new dynamic buffer management
    if (m_context != nullptr) { try { m_context->destroy(); } catch(...) {} }
    if (m_engine != nullptr) { try { m_engine->destroy(); } catch(...) {} }
    if (m_runtime != nullptr) { try { m_runtime->destroy(); } catch(...) {} }
    if (m_stream) { cudaStreamDestroy(m_stream); m_stream = nullptr; }
    std::cout << "TextDetect destructor called" << std::endl;
}
void TextDetect::preprocess(const cv::Mat& image) {
        printf("[Det] PREPROCESS START: m_inW=%d, m_inH=%d\n", m_inW, m_inH);
        fflush(stdout);

        // Check original image dimensions
        if (image.empty()) {
            std::cerr << "Error: Input image is empty." << std::endl;
            return;
        }

        m_origW = image.cols;
        m_origH = image.rows;
        printf("[Det] Original: %dx%d, using SIMPLE %dx%d resize\n", m_origW, m_origH, DET_SIZE, DET_SIZE);
        fflush(stdout);
        // 简单直接的DET_SIZE x DET_SIZE resize - 移除复杂逻辑
        cv::Mat resized;
        cv::resize(image, resized, cv::Size(DET_SIZE, DET_SIZE));

        printf("[Det] ✅ SIMPLE resize: %dx%d -> %dx%d\n", m_origW, m_origH, resized.cols, resized.rows);
        fflush(stdout);

        // 固定960x960模式，缓冲区已在初始化时分配

        m_inputImage = resized;
        m_scaleX = 960.0 / static_cast<double>(m_origW);
        m_scaleY = 960.0 / static_cast<double>(m_origH);
        m_offsetX = m_offsetY = 0;

        // 简化的归一化和格式转换
        cv::Mat normalized;
        m_inputImage.convertTo(normalized, CV_32FC3, 1.0f/255.0f);

        // ImageNet标准化 (RGB)
        cv::Scalar mean(0.485f, 0.456f, 0.406f);
        cv::Scalar stdv(0.229f, 0.224f, 0.225f);
        normalized = (normalized - mean) / stdv;

        printf("[Det] ✅ Normalized image\n");

        // HWC -> CHW 转换
        std::vector<cv::Mat> channels(3);
        cv::split(normalized, channels);

        int H = 960, W = 960;
        for(int c = 0; c < 3; ++c){
            memcpy(m_inputBuffer + c * H * W, channels[c].ptr<float>(), H * W * sizeof(float));
        }

        printf("[Det] ✅ Converted to CHW format\n");

        // 拷贝到GPU
        size_t inNumel = 3 * H * W;
        CUDA_CHECK(cudaMemcpyAsync(m_inputDeviceBuffer, m_inputBuffer, inNumel * sizeof(float), cudaMemcpyHostToDevice, m_stream));
    }
void TextDetect::forward() {
        // Fixed 960x960 inference - no dynamic shape setting needed
        std::cout << "[Det] Fixed inference: 960x960 -> 960x960" << std::endl;

        // Set input and output device buffers
        int nb = m_engine->getNbBindings();
        std::vector<void*> bindings(nb, nullptr);
        bindings[m_inputIndex] = m_inputDeviceBuffer;
        bindings[m_outputIndex] = m_outputDeviceBuffer;
        bool ok = m_context->enqueueV2(bindings.data(), m_stream, nullptr);
        if (!ok) {
            fprintf(stderr, "TensorRT enqueueV2 failed in detector.forward()\n");
        }
        CUDA_CHECK(cudaGetLastError());
        CUDA_CHECK(cudaStreamSynchronize(m_stream));
        CUDA_CHECK(cudaGetLastError());
    }

void TextDetect::postprocess() {
        size_t outBytes = m_outputNumel * sizeof(float);
        CUDA_CHECK(cudaMemcpyAsync(m_outputBuffer, m_outputDeviceBuffer, outBytes, cudaMemcpyDeviceToHost, m_stream));
        CUDA_CHECK(cudaStreamSynchronize(m_stream));

        // 1) 概率图（取通道0）
        cv::Mat prob(m_outH, m_outW, CV_32FC1, const_cast<float*>(m_outputBuffer));

        // 2) 二值化（mask_thresh）
        cv::Mat mask;
        cv::threshold(prob, mask, MASK_THRESH, 1.0, cv::THRESH_BINARY);

        // 2.5) 不做形态学后处理，直接使用二值 mask（对齐 infer.py）
        cv::Mat mask_u8; mask.convertTo(mask_u8, CV_8UC1, 255.0);


	        // 2.6) 可选的轻量形态学膨胀（默认关闭，可通过环境变量开启），改善细长/轻微倾斜文本的连通性
	        //    OCR_DB_DILATE_X, OCR_DB_DILATE_Y 控制核大小（建议 X=2,Y=1 或 X=3,Y=1）
	        int dilx = read_env_i("OCR_DB_DILATE_X", 0);
	        int dily = read_env_i("OCR_DB_DILATE_Y", 0);
	        if (dilx > 0 || dily > 0) {
	            int kx = std::max(1, dilx);
	            int ky = std::max(1, dily);
	            cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(kx, ky));
	            cv::dilate(mask_u8, mask_u8, kernel, cv::Point(-1,-1), 1);
	        }

        // 3) 连通域/轮廓
        std::vector<std::vector<cv::Point>> contours;
        cv::findContours(mask_u8, contours, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE); // infer.py 用 RETR_LIST

        // 4) 逐候选评分 + 形态外扩（近似 unclip）+ 最小外接矩形
        struct Candidate { std::vector<cv::Point> contour; double score; cv::Rect r; };
        std::vector<Candidate> cands; cands.reserve(contours.size());
        for (const auto& contour : contours) {
            // 对齐 infer-trt.py：不对 contour 点数做过滤，直接用 minAreaRect(contour) 检查最短边
            cv::RotatedRect rr0 = cv::minAreaRect(contour);
            float sside0 = std::min(rr0.size.width, rr0.size.height);
            if (sside0 < MIN_SIZE) continue;

            // 使用 minAreaRect 的四点作为 box，用于评分（严格对齐 infer-trt.py 的 _box_score 浮点边界处理）
            cv::Point2f pts0[4]; rr0.points(pts0);
            std::vector<cv::Point2f> rectPolyF; rectPolyF.reserve(4);
            for (int i=0;i<4;++i) rectPolyF.emplace_back(pts0[i].x, pts0[i].y);
            // 计算 xmin/xmax/ymin/ymax（floor/ceil），并使用 +1 的高度宽度（与 python 的 ymax+1, xmax+1 对齐）
            float xmin_f = rectPolyF[0].x, xmax_f = rectPolyF[0].x;
            float ymin_f = rectPolyF[0].y, ymax_f = rectPolyF[0].y;
            for (int i=1;i<4;++i){
                xmin_f = std::min(xmin_f, rectPolyF[i].x);
                xmax_f = std::max(xmax_f, rectPolyF[i].x);
                ymin_f = std::min(ymin_f, rectPolyF[i].y);
                ymax_f = std::max(ymax_f, rectPolyF[i].y);
            }
            int xmin = std::max(0, std::min(m_outW-1, (int)std::floor(xmin_f)));
            int xmax = std::max(0, std::min(m_outW-1, (int)std::ceil (xmax_f)));
            int ymin = std::max(0, std::min(m_outH-1, (int)std::floor(ymin_f)));
            int ymax = std::max(0, std::min(m_outH-1, (int)std::ceil (ymax_f)));
            int w = std::max(1, xmax - xmin + 1);
            int h = std::max(1, ymax - ymin + 1);
            cv::Rect r(xmin, ymin, std::min(w, m_outW - xmin), std::min(h, m_outH - ymin));

            cv::Mat roiProb = prob(r);
            cv::Mat roiMask = cv::Mat::zeros(r.height, r.width, CV_8UC1);
            std::vector<std::vector<cv::Point>> polyShifted(1);
            polyShifted[0].reserve(4);
            for (auto &p : rectPolyF) polyShifted[0].push_back(cv::Point((int)(p.x - r.x), (int)(p.y - r.y))); // 向零取整
            cv::fillPoly(roiMask, polyShifted, cv::Scalar(255));
            double meanScore = cv::mean(roiProb, roiMask)[0];

            // 自适应阈值：对细长、轻微倾斜的文本行适当放宽，其他场景保持偏紧
            float ar0 = std::max(rr0.size.width, rr0.size.height) / std::max(1.0f, std::min(rr0.size.width, rr0.size.height));
            // 计算长边相对水平的倾斜角（deg），用于识别轻微倾斜的文本行
            float ang0 = rr0.angle; // [-90,0)
            if (rr0.size.width < rr0.size.height) ang0 += 90.0f; // 转为以长边为基准
            float tilt = std::fabs(ang0); // 0~90，越大越倾斜

            // 对齐 infer-trt.py：使用固定阈值 BOX_THRESH，不做自适应调整
            if (meanScore < (double)BOX_THRESH) continue;
            cands.push_back({contour, meanScore, r});
        }
        // infer.py 没有按分数排序，保持 contour 顺序处理
        // std::sort(cands.begin(), cands.end(), [](const Candidate& a, const Candidate& b){ return a.score > b.score; });

        std::vector<std::vector<cv::Point>> poly4s; poly4s.reserve(cands.size());
        for (const auto& cand : cands) {
            const auto& contour = cand.contour;
            // 对齐 infer-trt.py / infer2.py：先取 contour 的最小外接矩形四点，再对这四点做 unclip
            double local_unclip = (double)UNCLIP_RATIO;
            cv::RotatedRect rr_seed = cv::minAreaRect(contour);
            cv::Point2f seedPts2f[4]; rr_seed.points(seedPts2f);
            std::vector<cv::Point2f> rectPolySeed; rectPolySeed.reserve(4);
            for (int i=0;i<4;++i) rectPolySeed.emplace_back(seedPts2f[i].x, seedPts2f[i].y);

            // 使用四点做外扩（浮点版本，避免取整误差）；失败则放弃
            std::vector<cv::Point2f> unclippedRectF = db_unclip_polygon_f(rectPolySeed, (float)local_unclip);
            if (unclippedRectF.size() < 3) continue;

            // 对外扩后的多边形再做最小外接矩形
            cv::RotatedRect rr = cv::minAreaRect(unclippedRectF);
            cv::Point2f pts[4]; rr.points(pts);
            std::vector<cv::Point> quad; quad.reserve(4);
            for (int i=0;i<4;++i) quad.push_back(cv::Point((int)std::round(pts[i].x), (int)std::round(pts[i].y)));

            // 计算几何特征
            float w = std::hypot(pts[1].x - pts[0].x, pts[1].y - pts[0].y);
            float h = std::hypot(pts[3].x - pts[0].x, pts[3].y - pts[0].y);
            float ar = std::max(w, h) / std::max(1.0f, std::min(w, h));
            float ang = rr.angle; if (w < h) ang += 90.0f; float tilt2 = std::fabs(ang);

            // 行级切分：若框过高且基本水平（避免跨多行），在概率谷值处分裂
            // 对齐 infer-trt.py：默认关闭行切分（可用 OCR_DB_SPLIT_LINES 打开）
            bool didSplit = false;
            const int split_enable = 0; // 方案A：严格对齐 infer2.py，禁用行切分
            if (split_enable) {
                if (tilt2 <= 12.0f && (h > w ? (h/w >= 1.6f) : (w/h >= 1.6f)) && (int)std::max(w,h) >= (MIN_SIZE+2)*2) {
                    cv::Rect r2 = cv::boundingRect(quad) & cv::Rect(0,0,m_outW,m_outH);
                    if (r2.width > 0 && r2.height > 0) {
                        cv::Mat roiProb2 = prob(r2);
                        cv::Mat roiMask2 = cv::Mat::zeros(r2.height, r2.width, CV_8UC1);
                        std::vector<std::vector<cv::Point>> poly2(1);
                        poly2[0].reserve(4);
                        for (auto p : quad) poly2[0].push_back(cv::Point(p.x - r2.x, p.y - r2.y));
                        cv::fillPoly(roiMask2, poly2, cv::Scalar(255));
                        std::vector<float> rowMean(r2.height, 0.0f);
                        for (int y=0; y<r2.height; ++y) {
                            const float* pr = roiProb2.ptr<float>(y);
                            const uchar* pm = roiMask2.ptr<uchar>(y);
                            double s=0.0; int c=0;
                            for (int x=0; x<r2.width; ++x) { if (pm[x]) { s += pr[x]; ++c; } }
                            rowMean[y] = c>0 ? (float)(s/c) : 0.0f;
                        }
                        for (int y=1; y<r2.height-1; ++y) rowMean[y] = (rowMean[y-1]+rowMean[y]+rowMean[y+1])/3.0f;
                        float maxv = 0.0f; for (float v: rowMean) maxv = std::max(maxv, v);
                        int gap_y = -1; int gap_min = 3; float valley = 1e9f;
                        for (int y=gap_min; y<r2.height-gap_min; ++y) {
                            if (rowMean[y] < valley && rowMean[y] <= 0.25f*maxv) {
                                if (rowMean[y-2] > 0.5f*maxv && rowMean[y+2] > 0.5f*maxv) { valley = rowMean[y]; gap_y = y; }
                            }
                        }
                        if (gap_y >= gap_min && gap_y <= r2.height-gap_min) {
                            std::vector<cv::Point> q1{ {r2.x, r2.y}, {r2.x+r2.width, r2.y}, {r2.x+r2.width, r2.y+gap_y}, {r2.x, r2.y+gap_y} };
                            std::vector<cv::Point> q2{ {r2.x, r2.y+gap_y}, {r2.x+r2.width, r2.y+gap_y}, {r2.x+r2.width, r2.y+r2.height}, {r2.x, r2.y+r2.height} };
                            auto push_if_valid = [&](const std::vector<cv::Point>& q){
                                cv::Point2f p4[4]; for(int i=0;i<4;++i) p4[i]=cv::Point2f((float)q[i].x,(float)q[i].y);
                                float w2 = std::hypot(p4[1].x - p4[0].x, p4[1].y - p4[0].y);
                                float h2 = std::hypot(p4[3].x - p4[0].x, p4[3].y - p4[0].y);
                                float s2 = std::min(w2, h2);
                                // 对齐 infer-trt.py：统一使用 min_size + 2
                                if (s2 >= MIN_SIZE + 2) poly4s.push_back(q);
                            };
                            push_if_valid(q1); push_if_valid(q2);
                            didSplit = true;
                        }
                    }
                }
            }

            if (!didSplit) {
                // 严格对齐 infer-trt.py 的第二次 min_size 检查：sside < min_size + 2
                float sside = std::min(w, h);
                if (sside < MIN_SIZE + 2) continue;
                poly4s.push_back(quad);
            }
        }

        // 5) 映射到原图尺度（反变换：去 Padding，再按 1/scale 还原；支持非等比缩放）
        std::vector<std::vector<cv::Point>> temp_polys4;
        for (auto &quad : poly4s) {
            std::vector<cv::Point> mapped; mapped.reserve(4);
            for (auto &p : quad) {
                float dx = (p.x - m_offsetX) / std::max(1e-6, m_scaleX);
                float dy = (p.y - m_offsetY) / std::max(1e-6, m_scaleY);
                int xi = (int)std::lround(dx);
                int yi = (int)std::lround(dy);
                // 对齐 infer-trt.py：先允许坐标达到宽/高（不减 1），后续再统一裁剪
                xi = std::max(0, std::min(m_origW, xi));
                yi = std::max(0, std::min(m_origH, yi));
                mapped.push_back(cv::Point(xi, yi));
            }
            // 裁剪到图像范围（对齐 infer-trt.py 的 _clip：上界为 src_w / src_h）
            for (auto &pt : mapped) {
                pt.x = std::max(0, std::min(m_origW, pt.x));
                pt.y = std::max(0, std::min(m_origH, pt.y));
            }
            // 顺时针排序
            auto ord = order_points_clockwise_cv(mapped);
            // 计算最终宽高
            int rect_width  = (int)std::lround(std::hypot(ord[1].x - ord[0].x, ord[1].y - ord[0].y));
            int rect_height = (int)std::lround(std::hypot(ord[3].x - ord[0].x, ord[3].y - ord[0].y));
            // 进一步放宽过滤，确保细小数字"1"不被过滤（与 infer-trt.py 一致）
            if (rect_width <= 1 || rect_height <= 1) continue;
            // 保留排序后的四点
            std::vector<cv::Point> ord_vec(4);
            ord_vec[0]=ord[0]; ord_vec[1]=ord[1]; ord_vec[2]=ord[2]; ord_vec[3]=ord[3];
            temp_polys4.push_back(ord_vec);
        }

        // 6) 对齐 infer2.py 的排序：先按 top-left (y,x) 排序，再对近似同一行(y差<10)做左到右调整
        auto top_left = [](const std::vector<cv::Point>& b){ return b[0]; };
        std::sort(temp_polys4.begin(), temp_polys4.end(), [&](const std::vector<cv::Point>& a, const std::vector<cv::Point>& b){
            const auto pa = top_left(a); const auto pb = top_left(b);
            if (pa.y != pb.y) return pa.y < pb.y; return pa.x < pb.x;
        });
        for (size_t i=0; i+1<temp_polys4.size(); ++i){
            for (int j=(int)i; j>=0; --j){
                const auto p1 = top_left(temp_polys4[j]);
                const auto p2 = top_left(temp_polys4[j+1]);
                if (std::abs(p2.y - p1.y) < 10 && (p2.x < p1.x)) std::swap(temp_polys4[j], temp_polys4[j+1]);
                else break;
            }
        }
        m_polys4 = temp_polys4;
    }

    // NMS for cv::Point format
float TextDetect::calculate_iou_cv(const std::vector<cv::Point>& box1, const std::vector<cv::Point>& box2) {
        // 计算两个四边形框的IoU
        int x1_min = std::min({box1[0].x, box1[1].x, box1[2].x, box1[3].x});
        int y1_min = std::min({box1[0].y, box1[1].y, box1[2].y, box1[3].y});
        int x1_max = std::max({box1[0].x, box1[1].x, box1[2].x, box1[3].x});
        int y1_max = std::max({box1[0].y, box1[1].y, box1[2].y, box1[3].y});

        int x2_min = std::min({box2[0].x, box2[1].x, box2[2].x, box2[3].x});
        int y2_min = std::min({box2[0].y, box2[1].y, box2[2].y, box2[3].y});
        int x2_max = std::max({box2[0].x, box2[1].x, box2[2].x, box2[3].x});
        int y2_max = std::max({box2[0].y, box2[1].y, box2[2].y, box2[3].y});

        // 计算交集
        int inter_x_min = std::max(x1_min, x2_min);
        int inter_y_min = std::max(y1_min, y2_min);
        int inter_x_max = std::min(x1_max, x2_max);
        int inter_y_max = std::min(y1_max, y2_max);

        if (inter_x_max <= inter_x_min || inter_y_max <= inter_y_min) {
            return 0.0f;
        }

        float inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min);
        float area1 = (x1_max - x1_min) * (y1_max - y1_min);
        float area2 = (x2_max - x2_min) * (y2_max - y2_min);
        float union_area = area1 + area2 - inter_area;

        return union_area > 0 ? inter_area / union_area : 0.0f;
    }

std::vector<std::vector<cv::Point>> TextDetect::apply_nms_cv(std::vector<std::vector<cv::Point>>& boxes, float nms_threshold) {
        if (boxes.empty()) return boxes;

        // 计算每个框的面积，用于排序
        std::vector<std::pair<float, int>> area_indices;
        for (int i = 0; i < boxes.size(); i++) {
            const auto& box = boxes[i];
            int x_min = std::min({box[0].x, box[1].x, box[2].x, box[3].x});
            int y_min = std::min({box[0].y, box[1].y, box[2].y, box[3].y});
            int x_max = std::max({box[0].x, box[1].x, box[2].x, box[3].x});
            int y_max = std::max({box[0].y, box[1].y, box[2].y, box[3].y});
            float area = (x_max - x_min) * (y_max - y_min);
            area_indices.push_back({area, i});
        }

        // 按面积从大到小排序
        std::sort(area_indices.begin(), area_indices.end(), std::greater<std::pair<float, int>>());

        std::vector<bool> suppressed(boxes.size(), false);
        std::vector<std::vector<cv::Point>> result;

        for (const auto& item : area_indices) {
            int i = item.second;
            if (suppressed[i]) continue;

            result.push_back(boxes[i]);

            // 抑制与当前框重叠度高的其他框
            for (int j = 0; j < boxes.size(); j++) {
                if (i == j || suppressed[j]) continue;

                float iou = calculate_iou_cv(boxes[i], boxes[j]);
                if (iou > nms_threshold) {
                    suppressed[j] = true;
                }
            }
        }

        printf("[Det NMS] Input boxes: %zu, Output boxes: %zu (threshold: %.2f)\n",
               boxes.size(), result.size(), nms_threshold);

        return result;
    }

    // Dynamic memory management
bool TextDetect::allocateBuffers(int inH, int inW, int outH, int outW) {
        // Check if reallocation is needed
        if (m_allocatedInH == inH && m_allocatedInW == inW &&
            m_allocatedOutH == outH && m_allocatedOutW == outW) {
            return true; // Already allocated with correct size
        }

        // Free existing buffers
        freeBuffers();

        // Allocate input buffers
        size_t inputSize = static_cast<size_t>(m_inC) * inH * inW;
        CUDA_CHECK(cudaMalloc(&m_inputDeviceBuffer, inputSize * sizeof(float)));
        CUDA_CHECK(cudaMallocHost(&m_inputBuffer, inputSize * sizeof(float)));

        // Allocate output buffers
        size_t outputSize = static_cast<size_t>(m_outC) * outH * outW;
        CUDA_CHECK(cudaMalloc(&m_outputDeviceBuffer, outputSize * sizeof(float)));
        CUDA_CHECK(cudaMallocHost(&m_outputBuffer, outputSize * sizeof(float)));

        // Update allocated sizes
        m_allocatedInH = inH;
        m_allocatedInW = inW;
        m_allocatedOutH = outH;
        m_allocatedOutW = outW;

        std::cout << "[Dynamic] Allocated buffers: input=" << inH << "x" << inW
                  << ", output=" << outH << "x" << outW << std::endl;

        return true;
    }

void TextDetect::freeBuffers() {
        if (m_inputDeviceBuffer) { cudaFree(m_inputDeviceBuffer); m_inputDeviceBuffer = nullptr; }
        if (m_outputDeviceBuffer) { cudaFree(m_outputDeviceBuffer); m_outputDeviceBuffer = nullptr; }
        if (m_inputBuffer) { cudaFreeHost(m_inputBuffer); m_inputBuffer = nullptr; }
        if (m_outputBuffer) { cudaFreeHost(m_outputBuffer); m_outputBuffer = nullptr; }
        m_allocatedInH = m_allocatedInW = m_allocatedOutH = m_allocatedOutW = 0;
    }

    // Main inference method with dynamic shape support
bool TextDetect::infer(const cv::Mat& image) {
        if (image.empty()) {
            std::cerr << "[Det] Input image is empty" << std::endl;
            return false;
        }

        try {
            // Preprocess with dynamic size selection
            preprocess(image);

            // Forward inference
            forward();

            // Postprocess
            postprocess();

            return true;
        } catch (const std::exception& e) {
            std::cerr << "[Det] Inference failed: " << e.what() << std::endl;
            return false;
        }
    }

std::vector<std::vector<cv::Point>> TextDetect::getPolys() { return m_polys4; }


void TextDetect::Model_Infer(cv::Mat& Input_Image, vector<vector<vector<int>>> &boxes) {
    // 确保在正确的 GPU 上执行
    cudaSetDevice(gpu_id_);

    std::cout << "TextDetect::Model_Infer called with image size: "
              << Input_Image.cols << "x" << Input_Image.rows << std::endl;

    printf("[Det] 🔥 STEP 1: After size output\n");
    fflush(stdout);

    // 与 infer-trt.py 对齐：不做等比缩放+画布，直接把原图交给内部推理，
    // 其内部会 resize 到 960x960 并在 postprocess 中映射回原图坐标。
    printf("[Det] Using direct 960x960 resize inside detector (no canvas)\n");
    fflush(stdout);

    // 清空输出
    boxes.clear();

    try {
        // 记录开始时间
        auto start_time = std::chrono::high_resolution_clock::now();

        // 预处理时间
        auto preprocess_end = std::chrono::high_resolution_clock::now();
        double preprocess_time = std::chrono::duration<double, std::milli>(preprocess_end - start_time).count();

        // 推理时间
        auto inference_end = std::chrono::high_resolution_clock::now();
        double inference_time = std::chrono::duration<double, std::milli>(inference_end - preprocess_end).count();

        // 后处理时间
        auto postprocess_end = std::chrono::high_resolution_clock::now();
        double postprocess_time = std::chrono::duration<double, std::milli>(postprocess_end - inference_end).count();

        // 使用合并后的推理方法
        if (!this->infer(Input_Image)) {
            std::cout << "TextDetect: Inference failed" << std::endl;
            return;
        }
        auto polys = this->getPolys();

        // 直接使用内部已映射回原图的坐标
        boxes.clear();
        for (const auto& poly : polys) {
            if (poly.size() != 4) continue;
            vector<vector<int>> box;
            for (const auto& p : poly) box.push_back({p.x, p.y});
            boxes.push_back(box);
        }
        printf("[Det] ✅ Collected %zu boxes from detector (original coordinates)\n", boxes.size());

        std::cout << "TextDetect::Model_Infer completed" << std::endl;
        std::cout << "Detected " << boxes.size() << " text regions" << std::endl;
        std::cout << "Preprocessing: " << preprocess_time << "ms, "
                  << "Inference: " << inference_time << "ms, "
                  << "Postprocessing: " << postprocess_time << "ms" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "TextDetect: Error during inference: " << e.what() << std::endl;
    }
}

}// namespace OCR