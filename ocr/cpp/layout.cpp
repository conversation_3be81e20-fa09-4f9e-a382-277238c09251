#include "layout.h"
#include <fstream>
#include <iostream>
#include <cuda_runtime_api.h>
#include <algorithm>
#include <cmath>
#include <map>

namespace DocLayout {

DocLayoutDetector::~DocLayoutDetector() {
    free_memory();
    
    if (context_) {
        context_->destroy();
        context_ = nullptr;
    }
    if (engine_) {
        engine_->destroy();
        engine_ = nullptr;
    }
    if (runtime_) {
        runtime_->destroy();
        runtime_ = nullptr;
    }
}

int DocLayoutDetector::Model_Init() {
    // 使用默认路径
    std::string engine_path = "/workspace/hngpt/models/doc_layout.trt";
    return Model_Init(engine_path);
}

int DocLayoutDetector::Model_Init(const std::string& engine_path) {
    // 检查文件是否存在
    std::ifstream file(engine_path, std::ios::binary);
    if (!file.good()) {
        std::cerr << "[DocLayoutDetector] Engine file not found: " << engine_path << std::endl;
        return -1;
    }
    
    // 读取引擎文件
    file.seekg(0, std::ios::end);
    size_t size = file.tellg();
    file.seekg(0, std::ios::beg);
    
    std::vector<char> engine_data(size);
    file.read(engine_data.data(), size);
    file.close();
    
    // 创建 TensorRT 运行时
    Logger logger;
    runtime_ = nvinfer1::createInferRuntime(logger);
    if (!runtime_) {
        std::cerr << "[DocLayoutDetector] Failed to create TensorRT runtime" << std::endl;
        return -1;
    }
    
    // 反序列化引擎
    engine_ = runtime_->deserializeCudaEngine(engine_data.data(), size);
    if (!engine_) {
        std::cerr << "[DocLayoutDetector] Failed to deserialize TensorRT engine" << std::endl;
        return -1;
    }
    
    // 创建执行上下文
    context_ = engine_->createExecutionContext();
    if (!context_) {
        std::cerr << "[DocLayoutDetector] Failed to create execution context" << std::endl;
        return -1;
    }
    
    // 获取输入输出信息
    batch_size_ = 1;
    
    // 分配内存
    allocate_memory();
    
    std::cout << "[DocLayoutDetector] Model initialized successfully: " << engine_path << std::endl;
    return 0;
}

void DocLayoutDetector::allocate_memory() {
    // 输入: [1, 3, 640, 640] = 1,228,800 floats
    input_size_ = batch_size_ * 3 * target_size_ * target_size_;

    // 输出: [300, 6] = 1,800 floats
    output_size_ = 300 * 6;

    // 分配主机内存
    host_input_ = new float[input_size_];
    host_output_ = new float[output_size_];

    // 分配GPU内存 - 按照TensorRT引擎的绑定顺序
    // 绑定 0: im_shape (1, 2)
    CHECK(cudaMalloc(&gpu_buffers_[0], 2 * sizeof(float)));
    // 绑定 1: image (1, 3, 640, 640)
    CHECK(cudaMalloc(&gpu_buffers_[1], input_size_ * sizeof(float)));
    // 绑定 2: scale_factor (1, 2)
    CHECK(cudaMalloc(&gpu_buffers_[2], 2 * sizeof(float)));
    // 绑定 3: fetch_name_1 (1,) - 检测数量
    CHECK(cudaMalloc(&gpu_buffers_[3], sizeof(int)));
    // 绑定 4: fetch_name_0 (300, 6) - 检测结果
    CHECK(cudaMalloc(&gpu_buffers_[4], output_size_ * sizeof(float)));
}

void DocLayoutDetector::free_memory() {
    if (host_input_) {
        delete[] host_input_;
        host_input_ = nullptr;
    }
    if (host_output_) {
        delete[] host_output_;
        host_output_ = nullptr;
    }
    
    for (int i = 0; i < 5; ++i) {
        if (gpu_buffers_[i]) {
            cudaFree(gpu_buffers_[i]);
            gpu_buffers_[i] = nullptr;
        }
    }
}

std::vector<LayoutResult> DocLayoutDetector::predict(const cv::Mat& image) {
    if (!context_) {
        std::cerr << "[DocLayoutDetector] Model not initialized" << std::endl;
        return {};
    }
    
    int orig_height = image.rows;
    int orig_width = image.cols;
    
    float scale_x, scale_y;
    
    // 预处理
    preprocess(image, host_input_, scale_x, scale_y);
    
    // 准备输入数据
    float im_shape[2] = {static_cast<float>(orig_height), static_cast<float>(orig_width)};
    float scale_factor[2] = {scale_y, scale_x};  // [h_scale, w_scale]

    // 复制数据到GPU - 按照正确的绑定顺序
    // 绑定 0: im_shape
    CHECK(cudaMemcpy(gpu_buffers_[0], im_shape, 2 * sizeof(float), cudaMemcpyHostToDevice));
    // 绑定 1: image
    CHECK(cudaMemcpy(gpu_buffers_[1], host_input_, input_size_ * sizeof(float), cudaMemcpyHostToDevice));
    // 绑定 2: scale_factor
    CHECK(cudaMemcpy(gpu_buffers_[2], scale_factor, 2 * sizeof(float), cudaMemcpyHostToDevice));
    
    // 执行推理
    bool success = context_->executeV2(gpu_buffers_);
    if (!success) {
        std::cerr << "[DocLayoutDetector] Inference failed" << std::endl;
        return {};
    }
    
    // 复制结果回主机
    // 绑定 4: fetch_name_0 (300, 6) - 主要检测结果
    CHECK(cudaMemcpy(host_output_, gpu_buffers_[4], output_size_ * sizeof(float), cudaMemcpyDeviceToHost));

    // 可选：获取检测数量 (绑定 3: fetch_name_1)
    int num_detections = 0;
    CHECK(cudaMemcpy(&num_detections, gpu_buffers_[3], sizeof(int), cudaMemcpyDeviceToHost));
    std::cout << "[DocLayoutDetector] 检测数量: " << num_detections << std::endl;
    
    // 后处理
    return postprocess(host_output_, orig_width, orig_height, scale_x, scale_y);
}

void DocLayoutDetector::preprocess(const cv::Mat& image, float* input_data, 
                                  float& scale_x, float& scale_y) {
    int orig_height = image.rows;
    int orig_width = image.cols;
    
    // 计算缩放比例
    scale_x = static_cast<float>(target_size_) / orig_width;
    scale_y = static_cast<float>(target_size_) / orig_height;
    
    // 直接缩放到640x640，不保持宽高比
    cv::Mat resized;
    cv::resize(image, resized, cv::Size(target_size_, target_size_));
    
    // BGR -> RGB
    cv::Mat rgb_image;
    cv::cvtColor(resized, rgb_image, cv::COLOR_BGR2RGB);
    
    // 归一化到 [0, 1]
    rgb_image.convertTo(rgb_image, CV_32F, 1.0 / 255.0);
    
    // HWC -> CHW 并复制到输入缓冲区
    int hw = target_size_ * target_size_;
    for (int c = 0; c < 3; ++c) {
        for (int h = 0; h < target_size_; ++h) {
            for (int w = 0; w < target_size_; ++w) {
                input_data[c * hw + h * target_size_ + w] = 
                    rgb_image.at<cv::Vec3f>(h, w)[c];
            }
        }
    }
}

std::vector<LayoutResult> DocLayoutDetector::postprocess(float* output_data, 
                                                        int orig_width, int orig_height,
                                                        float scale_x, float scale_y) {
    std::vector<LayoutResult> results;
    
    // 计算精确的反向缩放因子
    float coord_scale_factor_x = 1.0f / scale_x;  // 原始宽度/640
    float coord_scale_factor_y = 1.0f / scale_y;  // 原始高度/640
    
    // 解析检测结果 (假设输出格式为 [300, 6])
    for (int i = 0; i < 300; ++i) {
        float* detection = output_data + i * 6;
        
        int class_id = static_cast<int>(detection[0]);
        float confidence = detection[1];
        float x1 = detection[2];
        float y1 = detection[3];
        float x2 = detection[4];
        float y2 = detection[5];
        
        // 过滤低置信度检测
        if (confidence < 0.1f) continue;
        
        // 坐标转换：模型输出 -> 原始图像坐标
        int orig_x1 = static_cast<int>(x1 / coord_scale_factor_x);
        int orig_y1 = static_cast<int>(y1 / coord_scale_factor_y);
        int orig_x2 = static_cast<int>(x2 / coord_scale_factor_x);
        int orig_y2 = static_cast<int>(y2 / coord_scale_factor_y);
        
        // 确保坐标在合理范围内
        orig_x1 = std::max(0, std::min(orig_x1, orig_width));
        orig_y1 = std::max(0, std::min(orig_y1, orig_height));
        orig_x2 = std::max(orig_x1, std::min(orig_x2, orig_width));
        orig_y2 = std::max(orig_y1, std::min(orig_y2, orig_height));
        
        // 确保边界框有效
        if (orig_x2 <= orig_x1 || orig_y2 <= orig_y1) continue;
        
        // 获取类别名称
        std::string class_name = (class_id < CLASS_NAMES.size()) ? 
                                CLASS_NAMES[class_id] : 
                                ("class_" + std::to_string(class_id));
        
        // 创建结果
        std::vector<int> coordinate = {orig_x1, orig_y1, orig_x2, orig_y2};
        results.emplace_back(class_id, class_name, confidence, coordinate);
    }
    
    // 按置信度排序
    std::sort(results.begin(), results.end(), 
              [](const LayoutResult& a, const LayoutResult& b) {
                  return a.score > b.score;
              });
    
    // 应用置信度过滤
    std::vector<LayoutResult> filtered_results;
    for (const auto& result : results) {
        if (result.score >= conf_threshold_) {
            filtered_results.push_back(result);
        }
    }
    
    // 应用NMS
    return apply_nms(filtered_results);
}

std::vector<LayoutResult> DocLayoutDetector::apply_nms(const std::vector<LayoutResult>& results,
                                                      float iou_threshold) {
    if (results.empty()) return results;

    // 按类别分组
    std::map<int, std::vector<LayoutResult>> class_groups;
    for (const auto& result : results) {
        class_groups[result.cls_id].push_back(result);
    }

    std::vector<LayoutResult> final_results;

    // 对每个类别进行NMS
    for (auto& pair : class_groups) {
        int cls_id = pair.first;
        std::vector<LayoutResult>& group = pair.second;
        // 按置信度排序
        std::sort(group.begin(), group.end(),
                  [](const LayoutResult& a, const LayoutResult& b) {
                      return a.score > b.score;
                  });

        std::vector<LayoutResult> keep;

        for (const auto& current : group) {
            bool should_keep = true;

            for (const auto& kept : keep) {
                if (calculate_iou(current.coordinate, kept.coordinate) > iou_threshold) {
                    should_keep = false;
                    break;
                }
            }

            if (should_keep) {
                keep.push_back(current);
            }
        }

        final_results.insert(final_results.end(), keep.begin(), keep.end());
    }

    // 重新按置信度排序
    std::sort(final_results.begin(), final_results.end(),
              [](const LayoutResult& a, const LayoutResult& b) {
                  return a.score > b.score;
              });

    return final_results;
}

float DocLayoutDetector::calculate_iou(const std::vector<int>& box1, const std::vector<int>& box2) {
    if (box1.size() < 4 || box2.size() < 4) return 0.0f;

    int x1_1 = box1[0], y1_1 = box1[1], x2_1 = box1[2], y2_1 = box1[3];
    int x1_2 = box2[0], y1_2 = box2[1], x2_2 = box2[2], y2_2 = box2[3];

    // 计算交集
    int inter_x1 = std::max(x1_1, x1_2);
    int inter_y1 = std::max(y1_1, y1_2);
    int inter_x2 = std::min(x2_1, x2_2);
    int inter_y2 = std::min(y2_1, y2_2);

    if (inter_x2 <= inter_x1 || inter_y2 <= inter_y1) {
        return 0.0f;
    }

    int inter_area = (inter_x2 - inter_x1) * (inter_y2 - inter_y1);

    // 计算并集
    int area1 = (x2_1 - x1_1) * (y2_1 - y1_1);
    int area2 = (x2_2 - x1_2) * (y2_2 - y1_2);
    int union_area = area1 + area2 - inter_area;

    return union_area > 0 ? static_cast<float>(inter_area) / union_area : 0.0f;
}

} // namespace DocLayout
