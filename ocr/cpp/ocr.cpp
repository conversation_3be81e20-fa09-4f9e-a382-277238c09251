# include "ocr.h"
# include <stdio.h>
# include "utility.h"

void TrtOcr::Model_Init(string det_engine_path, string rec_engine_path){
    printf("[TrtOcr] Initializing models on GPU %d\n", gpu_id_);
    printf("[TrtOcr] Det Engine: %s\n", det_engine_path.c_str());
    printf("[TrtOcr] Rec Engine: %s\n", rec_engine_path.c_str());

    // 设置 GPU 设备
    cudaSetDevice(gpu_id_);

    this->td = new TextDetect(gpu_id_);  // 传递 GPU ID
    this->tr = new TextRec(version_, gpu_id_);  // 传递版本信息和 GPU ID
    this->td->Model_Init(det_engine_path);  // 只传递engine路径
    this->tr->Model_Init(rec_engine_path);  // 只传递engine路径
}

// 重载的Model_Init方法，根据版本自动选择模型路径
void TrtOcr::Model_Init(){
    // 根据版本获取模型路径
    string model_dir = "/workspace/hngpt/models/";
    string det_engine_path = model_dir + version_ + ".det.trt";
    string rec_engine_path = model_dir + version_ + ".rec.trt";

    printf("[TrtOcr] Using %s model\n", version_.c_str());

    // 调用简化的Model_Init方法
    Model_Init(det_engine_path, rec_engine_path);
}

std::vector<std::vector<std::vector<int>>> TrtOcr::Model_Detect(cv::Mat& inputImg){
    vector<vector<vector<int>>> boxes;
    this->td->Model_Infer(inputImg, boxes);
    return boxes;
}

std::vector<std::tuple<std::vector<std::vector<int>>, std::string, double>> TrtOcr::Model_Infer(cv::Mat& inputImg){
    // 使用成员变量中的GPU ID
    cudaError_t status = cudaSetDevice(gpu_id_);
    //cv::imwrite(to_string(this->count_name_)+"test.png", inputImg);
    if(inputImg.channels()==4){
        cv::Mat _chs[4];
        split(inputImg, _chs);

        cv::Mat new_img(inputImg.rows, inputImg.cols, CV_8UC3);
        cv::Mat _new_chas[3];
        split(new_img, _new_chas);
        for(int i=0; i<3; i++){
            _new_chas[i] = 255 - _chs[3];
        }
        cv::Mat _dst;
        merge(_new_chas, 3, _dst);
        inputImg = _dst;
    }

    if(inputImg.channels()==1){
        cv::Mat new_img(inputImg.rows, inputImg.cols, CV_8UC3);
        cv::Mat _new_chas[3];
        split(new_img, _new_chas);
        for(int i=0; i<3; i++){
            _new_chas[i] = 255 - inputImg;
        }
        cv::Mat _dst;
        merge(_new_chas, 3, _dst);
        inputImg = _dst;
    }
    //cv::imshow(to_string(this->count_name_) + "_input_img", inputImg);
    //cv::waitKey(0);

    Mat srcImg;
    inputImg.copyTo(srcImg);

    vector<vector<vector<int>>> boxes;
    this->td->Model_Infer(inputImg, boxes);

    vector<Mat> img_list;
    for (int j = 0; j < (int)boxes.size(); j++) {
        // 与 infer-trt.py 对齐：不对竖向窄框做任何横向外扩，直接使用检测框进行裁剪
        cv::Mat crop_img = Utility::GetRotateCropImage(srcImg, boxes[j]);
        if (!crop_img.empty() && crop_img.rows > 0 && crop_img.cols > 0) {
            img_list.push_back(crop_img);
        } else {
            std::cout << "Skipping invalid crop image for box " << j << std::endl;
        }
    }
    //cout<<"finish detect"<<endl;

    vector<pair<string, double>> rec_res;
    vector<int> idx_map;
    vector<double> rec_times;
    this->tr->Model_Infer(img_list, rec_res, idx_map); 
   
    std::vector<char> removed(boxes.size(), 0);
    for (int idx : idx_map) {
        if (idx >= 0 && idx < (int)removed.size()) removed[idx] = 1;
    }

    this->count_name_ ++;
    if(this->count_name_%100000 == 0)
        this->count_name_ = 0;

    std::vector<std::tuple<std::vector<std::vector<int>>, std::string, double>> res;
    res.reserve(boxes.size());
    int ri = 0; // index into rec_res (成功的识别结果按顺序排列)
    for (size_t i = 0; i < boxes.size(); ++i) {
        if (removed[i] || ri >= (int)rec_res.size()) {
            res.push_back(std::make_tuple(boxes[i], std::string(), 0.0));
        } else {
            res.push_back(std::make_tuple(boxes[i], rec_res[ri].first, rec_res[ri].second));
            ++ri;
        }
    }
    return res;
}

bool isNumber(const string& str){
    for(char const &c : str){
        if(isdigit(c)==0)
            return false;         
    }
    return true;
}

bool isChinese(const string& str)
{
  unsigned char utf[4] = {0};
  unsigned char unicode[3] = {0};
  bool res = false;
  for (int i = 0; i < str.length(); i++) {
    if ((str[i] & 0x80) == 0) {   //ascii begin with 0
      res = false;
    }
    else /*if ((str[i] & 0x80) == 1) */{
      utf[0] = str[i];
      utf[1] = str[i + 1];
      utf[2] = str[i + 2];
      i++;
      i++;
      unicode[0] = ((utf[0] & 0x0F) << 4) | ((utf[1] & 0x3C) >>2);
      unicode[1] = ((utf[1] & 0x03) << 6) | (utf[2] & 0x3F);
//      printf("%x,%x\n",unicode[0],unicode[1]);
//      printf("aaaa %x,%x,%x\n\n",utf[0],utf[1],utf[2]);
      if(unicode[0] >= 0x4e && unicode[0] <= 0x9f){
         if (unicode[0] == 0x9f && unicode[1] >0xa5)
                res = false;
         else         
               res = true;
      }else
         res = false;
    }
  }
  return res;
}


string TrtOcr::TaskProcess(vector<pair< vector<string>, double>> &result){
    for(int i=0; i<result.size(); i++){
        if((result[i].second > REC_THR_)&&(result[i].first.size()>=3)){ // 置信度较高
            vector<string> res_vec;
            for(int j=0; j<3; j++){
                if(!isChinese(result[i].first[j])){ // 不是汉字
                    if(isNumber(result[i].first[j].c_str())) // 是数字
                        res_vec.push_back(result[i].first[j]);
                }
            }    
            string res="";
            if(res_vec.size()==3){
                for(int j=0; j<3; j++)
                    res += res_vec[j];
                if(stoi(res.c_str())>= REC_RANGE_[0] && stoi(res.c_str())<= REC_RANGE_[1])
                    return res;
            }
        }
    }
    return "";
}

string TrtOcr::MultiFrameSmooth(string door_result, int step){
    count_img_ ++ ;
    results_[door_result]++;
    if(door_result == "")
        results_[""] = 1; //当有门牌时，会正常返回门牌号，一直没有门牌则会返回“”

    if(count_img_ == step){
        int max_value = 0;
        string res= "";
        for(auto it = results_.begin(); it!= results_.end(); it++){
            if(it->second > max_value){
                max_value = it->second;
                res = it-> first;
            }
        } 
        cout<< res << " : " << max_value << endl; 
        results_.clear();
        count_img_ = 0;
        return res;
    }
    return "";
}

TrtOcr::~TrtOcr(){
    // 先释放识别（可能占用较多显存与上下文），再释放检测
    try { if (tr) { delete tr; tr = nullptr; } } catch(...) {}
    try { if (td) { delete td; td = nullptr; } } catch(...) {}
}
