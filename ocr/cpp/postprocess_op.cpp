#include "postprocess_op.h"
#include "clipper.cpp"
namespace OCR {
std::vector<std::vector<int>> OrderPointsClockwise(std::vector<std::vector<int>> pts)
{
    std::vector<std::vector<int>> box = pts;
    std::sort(box.begin(), box.end(),
        [](std::vector<int> a, std::vector<int> b)
        {
            if (a[0] != b[0])
                return a[0] < b[0];
            return false;
        });

    std::vector<std::vector<int>> leftmost = {box[0], box[1]};
    std::vector<std::vector<int>> rightmost = {box[2], box[3]};

    if (leftmost[0][1] > leftmost[1][1])
        std::swap(leftmost[0], leftmost[1]);

    if (rightmost[0][1] > rightmost[1][1])
        std::swap(rightmost[0], rightmost[1]);

    std::vector<std::vector<int>> rect = {leftmost[0], rightmost[0], rightmost[1], leftmost[1]};
    return rect;
}

std::vector<std::vector<std::vector<int>>> box4rec(std::vector<std::vector<std::vector<int>>> boxes,cv::Mat &img) {
    int target_size = 960;
    int w = img.cols;
    int h = img.rows;
    float ratio = float(w) / float(h);
    int resize_w, resize_h;
    if (w > h) {  // 如果原始图像是宽屏
        resize_w = target_size;
        resize_h = static_cast<int>(target_size / ratio);
    } else {  // 如果原始图像是高屏或宽高相等
        resize_h = target_size;
        resize_w = static_cast<int>(target_size * ratio);
    }

    //除32，余下的超过16补全为32，不足16的长度舍弃
    resize_h = std::max(static_cast<int>(std::round(static_cast<float>(resize_h) / 32) * 32), 32);
    resize_w = std::max(static_cast<int>(std::round(static_cast<float>(resize_w) / 32) * 32), 32);

    // 将调整后的图像放置到背景图像的中心
    int x_offset = (target_size - resize_w) / 2;
    int y_offset = (target_size - resize_h) / 2;

    // 计算每个边界框的原始坐标
    std::vector<std::vector<std::vector<int>>> original_boxes;
    for (const auto& box : boxes) {
        std::vector<std::vector<int>> original_box;
        for (const auto& point : box) {
            int x = point[0] - x_offset;
            int y = point[1] - y_offset;
             // Scale the coordinates back to the original image size
            x = std::min(std::max(int(float(x) * float(w) / float(resize_w)), 0), w - 1);
            y = std::min(std::max(int(float(y) * float(h) / float(resize_h)), 0), h - 1);

            original_box.push_back({x, y});
        }
        original_boxes.push_back(original_box);
    }

    return original_boxes;
}



std::vector<std::vector<std::vector<int>>> box2src(std::vector<std::vector<std::vector<int>>>& boxes, cv::Mat& srcimg) {
    const int dst_width = 960;
    const int dst_height = 960;

    int w = srcimg.cols;
    int h = srcimg.rows;

    float sx = static_cast<float>(w) / dst_width;
    float sy = static_cast<float>(h) / dst_height;

    std::vector<std::vector<std::vector<int>>> original_boxes;

    for (auto& box : boxes) {
        box = OrderPointsClockwise(box);
        for (auto& point : box) {
            // 遍历每个定点
            // 将 resize_img 的坐标转换为原始图像的坐标
            point[0] = int(point[0]*sx);
            point[1] = int(point[1]*sy);
            point[0] = std::min(std::max(point[0], 0), w - 1);
            point[1] = std::min(std::max(point[1], 0), h - 1);
        }
        int rec_width = int(sqrt(pow(box[0][0] - box[1][0], 2) + pow(box[0][1] - box[1][1], 2)));
        int rec_height = int(sqrt(pow(box[0][0] - box[3][0], 2) + pow(box[0][1] - box[3][1], 2)));
        if (rec_width <= 3 || rec_height <= 3)
            continue;
        original_boxes.push_back(box);
    }

    return original_boxes;
}


using namespace cv;
using namespace std;
using namespace ClipperLib;

vector<vector<float>> mat_to_vector(const Mat& mat)
{
    vector<vector<float>> img_vec;
    img_vec.reserve(mat.rows);

    for (int i = 0; i < mat.rows; ++i)
    {
        vector<float> tmp;
        tmp.reserve(mat.cols);

        for (int j = 0; j < mat.cols; ++j)
        {
            tmp.push_back(mat.at<float>(i, j));
        }
        img_vec.push_back(std::move(tmp));
    }
    return img_vec;
}

tuple<vector<vector<float>>, float> get_mini_boxes(const cv::RotatedRect& box)
{
    float sside = std::min(box.size.width, box.size.height);

    cv::Mat points;
    cv::boxPoints(box, points);

    auto array = mat_to_vector(points);
    std::sort(array.begin(), array.end(),
        [](const vector<float>& a, const vector<float>& b)
        {
            if (a[0] != b[0])
                return a[0] < b[0];
            return false;
        });

    vector<float> idx1 = array[0], idx2 = array[1], idx3 = array[2], idx4 = array[3];
    if (array[3][1] <= array[2][1])
    {
        idx2 = array[3];
        idx3 = array[2];
    }
    else
    {
        idx2 = array[2];
        idx3 = array[3];
    }

    if (array[1][1] <= array[0][1])
    {
        idx1 = array[1];
        idx4 = array[0];
    }
    else
    {
        idx1 = array[0];
        idx4 = array[1];
    }

    array[0] = idx1;
    array[1] = idx2;
    array[2] = idx3;
    array[3] = idx4;

    return make_tuple(array, sside);
}

template <typename T>
T clamp(T val, T minVal, T maxVal)
{
    return (val < minVal) ? minVal : (val > maxVal) ? maxVal : val;
}

float box_score(const Mat& pred, const vector<vector<float>>& box_array)
{
    int width = pred.cols;
    int height = pred.rows;

    float box_x[4] = {box_array[0][0], box_array[1][0], box_array[2][0], box_array[3][0]};
    float box_y[4] = {box_array[0][1], box_array[1][1], box_array[2][1], box_array[3][1]};

    int xmin = clamp(int(std::floor(*(std::min_element(box_x, box_x + 4)))), 0, width - 1);
    int xmax = clamp(int(std::ceil(*(std::max_element(box_x, box_x + 4)))), 0, width - 1);
    int ymin = clamp(int(std::floor(*(std::min_element(box_y, box_y + 4)))), 0, height - 1);
    int ymax = clamp(int(std::ceil(*(std::max_element(box_y, box_y + 4)))), 0, height - 1);

    Mat mask = cv::Mat::zeros(ymax - ymin + 1, xmax - xmin + 1, CV_8UC1);
    cv::Point root_point[4];
    root_point[0] = cv::Point(int(box_x[0]) - xmin, int(box_y[0]) - ymin);
    root_point[1] = cv::Point(int(box_x[1]) - xmin, int(box_y[1]) - ymin);
    root_point[2] = cv::Point(int(box_x[2]) - xmin, int(box_y[2]) - ymin);
    root_point[3] = cv::Point(int(box_x[3]) - xmin, int(box_y[3]) - ymin);

    const cv::Point* ppt[1] = {root_point};
    int npt[] = {4};
    cv::fillPoly(mask, ppt, npt, 1, cv::Scalar(1));

    Mat croppedImg;
    pred(cv::Rect(xmin, ymin, xmax - xmin + 1, ymax - ymin + 1)).copyTo(croppedImg);

    return cv::mean(croppedImg, mask)[0];
}

float get_contour_area(const vector<vector<float>>& box, float unclip_ratio)
{
    float distance = 1.0;
    const int pts_num = 4;
    float area = 0.0f;
    float dist = 0.0f;
    for (int i = 0; i < pts_num; ++i)
    {
        int next = (i + 1) % pts_num;
        float dx = box[i][0] - box[next][0];
        float dy = box[i][1] - box[next][1];

        area += box[i][0] * box[next][1] - box[i][1] * box[next][0];
        dist += std::sqrt(dx * dx + dy * dy);
    }

    area = std::abs(area / 2.0f);
    if (dist != 0.0f)
    {
        distance = area * unclip_ratio / dist;
    }
    return distance;
}

cv::RotatedRect box_unclip(const vector<vector<float>>& box, float unclip_ratio)
{
    float distance = get_contour_area(box, unclip_ratio);
    ClipperOffset offset;
    Path p;
    p << IntPoint(int(box[0][0]), int(box[0][1])) << IntPoint(int(box[1][0]), int(box[1][1]))
      << IntPoint(int(box[2][0]), int(box[2][1])) << IntPoint(int(box[3][0]), int(box[3][1]));
    offset.AddPath(p, jtRound, etClosedPolygon);

    Paths soln;
    offset.Execute(soln, distance);
    vector<cv::Point2f> points;

    for (int i = 0; i < soln.size(); ++i)
    {
        for (int j = 0; j < soln[soln.size() - 1].size(); ++j)
        {
            points.emplace_back(soln[i][j].X, soln[i][j].Y);
        }
    }
    cv::RotatedRect res;
    if (points.size() <= 0)
    {
        res = cv::RotatedRect(cv::Point2f(0, 0), cv::Size2f(1, 1), 0);
    }
    else
    {
        res = cv::minAreaRect(points);
    }
    return res;
}

void boxes_from_bitmap(const Mat& pred, const Mat& bitmap, vector<vector<vector<int>>>& box_array,
    float box_thresh, float unclip_ratio, int min_size, int max_candidates)
{

    int width = bitmap.cols;
    int height = bitmap.rows;

    vector<vector<cv::Point>> contours;
    vector<cv::Vec4i> hierarchy;

    cv::findContours(bitmap, contours, hierarchy, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);
    int num_contours = contours.size() >= max_candidates ? max_candidates : contours.size();

    Mat contour_image;
    cv::cvtColor(bitmap, contour_image, cv::COLOR_GRAY2BGR);

    // for(auto& contour : contours){
    //     vector<vector<cv::Point>> single_contour = {contour};
    //     cv::drawContours(contour_image, single_contour, -1, cv::Scalar(0, 0, 255), 2);
    // }
    // cv::imwrite("contour_image.jpg", contour_image);

    vector<vector<vector<int>>> boxes;
    for (auto& contour : contours)
    {
        if (contour.size() <= min_size)
            continue;
        vector<vector<float>> array;
        float sside;
        auto box = cv::minAreaRect(contour);
        tie(array, sside) = get_mini_boxes(box);
        if (sside < min_size)
            continue;

        float score = box_score(pred, array);
        if (score < box_thresh)
            continue;
        auto points = box_unclip(array, unclip_ratio);
        // difference
        if (points.size.height < 1.001 & points.size.width < 1.001)
            continue;

        vector<vector<float>> cliparray;
        tie(cliparray, sside) = get_mini_boxes(points);
        if (sside < min_size + 2)
            continue;

        int dest_width = pred.cols;
        int dest_height = pred.rows;

        vector<vector<int>> intcliparray;
        intcliparray.reserve(4);
        float x_scale = float(dest_width) / float(width);
        float y_scale = float(dest_height) / float(height);

        for (int i = 0; i < 4; ++i)
        {
            int x = int(clamp(std::roundf(cliparray[i][0] * x_scale), 0.0f, float(dest_width)));
            int y = int(clamp(std::roundf(cliparray[i][1] * y_scale), 0.0f, float(dest_height)));
            intcliparray.push_back({x, y});
        }

        box_array.emplace_back(intcliparray);
    }
}

vector<vector<int>> order_points_clockwise(vector<vector<int>>& box)
{
    std::sort(box.begin(), box.end(),
        [](const vector<int>& a, const vector<int>& b)
        {
            if (a[0] != b[0])
                return a[0] < b[0];
            return false;
        });

    vector<vector<int>> leftmost = {box[0], box[1]};
    vector<vector<int>> rightmost = {box[2], box[3]};

    if (leftmost[0][1] > leftmost[1][1])
    {
        std::swap(leftmost[0], leftmost[1]);
    }

    if (rightmost[0][1] > rightmost[1][1])
    {
        std::swap(rightmost[0], rightmost[1]);
    }

    vector<vector<int>> rect = {leftmost[0], rightmost[0], rightmost[1], leftmost[1]};
    return rect;
}

vector<vector<vector<int>>> filter_boxes(
    vector<vector<vector<int>>>& boxes, int src_h, int src_w, float ratio_h, float ratio_w)
{

    vector<vector<vector<int>>> boxes_filter;

    for (auto& box : boxes)
    {
        box = order_points_clockwise(box);
        for (int i = 0; i < box.size(); ++i)
        {
            box[i][0] /= ratio_w;
            box[i][1] /= ratio_h;
            box[i][0] = int(std::min(std::max(box[i][0], 0), src_w - 1));
            box[i][1] = int(std::min(std::max(box[i][1], 0), src_h - 1));
        }
        int rec_width = int(sqrt(pow(box[0][0] - box[1][0], 2) + pow(box[0][1] - box[1][1], 2)));
        int rec_height = int(sqrt(pow(box[0][0] - box[3][0], 2) + pow(box[0][1] - box[3][1], 2)));
        if (rec_width <= 3 || rec_height <= 3)
            continue;
        boxes_filter.push_back(box);
    }

    return boxes_filter;
}

float calculate_iou(const vector<vector<int>>& box1, const vector<vector<int>>& box2) {
    // 计算两个四边形框的IoU
    // 简化处理：将四边形转换为轴对齐的边界框
    int x1_min = min({box1[0][0], box1[1][0], box1[2][0], box1[3][0]});
    int y1_min = min({box1[0][1], box1[1][1], box1[2][1], box1[3][1]});
    int x1_max = max({box1[0][0], box1[1][0], box1[2][0], box1[3][0]});
    int y1_max = max({box1[0][1], box1[1][1], box1[2][1], box1[3][1]});

    int x2_min = min({box2[0][0], box2[1][0], box2[2][0], box2[3][0]});
    int y2_min = min({box2[0][1], box2[1][1], box2[2][1], box2[3][1]});
    int x2_max = max({box2[0][0], box2[1][0], box2[2][0], box2[3][0]});
    int y2_max = max({box2[0][1], box2[1][1], box2[2][1], box2[3][1]});

    // 计算交集
    int inter_x_min = max(x1_min, x2_min);
    int inter_y_min = max(y1_min, y2_min);
    int inter_x_max = min(x1_max, x2_max);
    int inter_y_max = min(y1_max, y2_max);

    if (inter_x_max <= inter_x_min || inter_y_max <= inter_y_min) {
        return 0.0f;
    }

    float inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min);
    float area1 = (x1_max - x1_min) * (y1_max - y1_min);
    float area2 = (x2_max - x2_min) * (y2_max - y2_min);
    float union_area = area1 + area2 - inter_area;

    return union_area > 0 ? inter_area / union_area : 0.0f;
}

vector<vector<vector<int>>> apply_nms(vector<vector<vector<int>>>& boxes, float nms_threshold) {
    if (boxes.empty()) return boxes;

    // 计算每个框的面积，用于排序
    vector<pair<float, int>> area_indices;
    for (int i = 0; i < boxes.size(); i++) {
        const auto& box = boxes[i];
        int x_min = min({box[0][0], box[1][0], box[2][0], box[3][0]});
        int y_min = min({box[0][1], box[1][1], box[2][1], box[3][1]});
        int x_max = max({box[0][0], box[1][0], box[2][0], box[3][0]});
        int y_max = max({box[0][1], box[1][1], box[2][1], box[3][1]});
        float area = (x_max - x_min) * (y_max - y_min);
        area_indices.push_back({area, i});
    }

    // 按面积从大到小排序
    sort(area_indices.begin(), area_indices.end(), greater<pair<float, int>>());

    vector<bool> suppressed(boxes.size(), false);
    vector<vector<vector<int>>> result;

    for (const auto& item : area_indices) {
        int i = item.second;
        if (suppressed[i]) continue;

        result.push_back(boxes[i]);

        // 抑制与当前框重叠度高的其他框
        for (int j = 0; j < boxes.size(); j++) {
            if (i == j || suppressed[j]) continue;

            float iou = calculate_iou(boxes[i], boxes[j]);
            if (iou > nms_threshold) {
                suppressed[j] = true;
            }
        }
    }

    printf("[NMS] Input boxes: %zu, Output boxes: %zu (threshold: %.2f)\n",
           boxes.size(), result.size(), nms_threshold);

    return result;
}

void detector_postprocess(const Mat& pred_map, vector<vector<vector<int>>>& boxes, int src_h, int src_w, int dst_h,
    int dst_w, float mask_thresh, float box_thresh, float unclip_ratio, int min_size, int max_candidates)
{
    Mat cbuf_map;
    cv::convertScaleAbs(pred_map, cbuf_map, 255.0);
    Mat bit_map;
    const double threshold = mask_thresh * 255;
    cv::threshold(cbuf_map, bit_map, threshold, 255, cv::THRESH_BINARY);

    vector<vector<vector<int>>> box_array;
    boxes_from_bitmap(pred_map, bit_map, box_array, box_thresh, unclip_ratio, min_size, max_candidates);
    float ratio_h = dst_h / (float) src_h;
    float ratio_w = dst_w / (float) src_w;

    vector<vector<vector<int>>> filtered_boxes = filter_boxes(box_array, src_h, src_w, ratio_h, ratio_w);

    // 应用NMS去除重叠框
    boxes = apply_nms(filtered_boxes, 0.3f);  // NMS阈值设为0.3
}
} // namespace OCR
