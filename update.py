import os
import tarfile
import logging
from logging.handlers import RotatingFileHandler
import sys
import pkg_resources
import ast
import importlib
import shutil

import subprocess
def setup_logging(log_dir):
    """设置日志配置"""
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(log_dir, "update.log")

    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')

    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

def find_imports(directory):
    """分析目录中所有 .py 文件的导入语句
    返回使用的包名列表
    """
    imports = set()

    class ImportVisitor(ast.NodeVisitor):
        def visit_Import(self, node):
            for name in node.names:
                imports.add(name.name.split('.')[0])

        def visit_ImportFrom(self, node):
            if node.module:
                imports.add(node.module.split('.')[0])

    # 遍历所有 .py 文件
    for root, _, files in os.walk(directory):
        for file in files:
            if file.endswith('.py'):
                try:
                    with open(os.path.join(root, file), 'r', encoding='utf-8') as f:
                        tree = ast.parse(f.read())
                        ImportVisitor().visit(tree)
                except Exception as e:
                    logging.warning(f"解析文件 {file} 时出错: {e}")

    # 过滤掉标准库
    stdlib_modules = set(sys.stdlib_module_names)
    third_party_imports = {imp for imp in imports if imp not in stdlib_modules}

    return third_party_imports

def get_package_paths(package_names):
    """获取包的实际文件路径"""
    package_paths = set()

    for package_name in package_names:
        try:
            # 尝试导入包以获取其位置
            package = importlib.import_module(package_name)
            if hasattr(package, '__path__'):
                # 如果是包，添加整个包目录
                package_paths.add(os.path.dirname(package.__path__[0]))
            elif hasattr(package, '__file__'):
                # 如果是模块，添加模块文件
                package_paths.add(os.path.dirname(package.__file__))

            # 获取包的依赖
            try:
                dist = pkg_resources.get_distribution(package_name)
                for req in dist.requires():
                    req_name = req.project_name
                    try:
                        req_package = importlib.import_module(req_name)
                        if hasattr(req_package, '__path__'):
                            package_paths.add(os.path.dirname(req_package.__path__[0]))
                        elif hasattr(req_package, '__file__'):
                            package_paths.add(os.path.dirname(req_package.__file__))
                    except ImportError:
                        logging.warning(f"无法导入依赖包: {req_name}")
            except pkg_resources.DistributionNotFound:
                logging.warning(f"无法找到包的依赖信息: {package_name}")

        except ImportError:
            logging.warning(f"无法导入包: {package_name}")

    return package_paths

def create_update_package_v0(paths_to_pack):
    """创建更新包
    Args:
        paths_to_pack: 要打包的文件或目录列表
    """
    output_file = "update.tar.gz"
    missing_paths = []
    files_to_pack = set()

    # 检查是否在正确的目录
    if os.getcwd() != "/workspace/hngpt":
        logging.error("请在 /workspace/hngpt 目录下运行此脚本")
        return False

    # 检查是否有root权限（对于系统目录需要）
    if os.geteuid() != 0:
        logging.error("需要root权限来访问系统目录，请使用sudo运行")
        return False

    # 处理文件和目录
    for path in paths_to_pack:
        if not os.path.exists(path):
            missing_paths.append(path)
            continue

        if os.path.isdir(path):
            # 如果是系统路径（以/开头）
            if path.startswith('/'):
                for root, _, filenames in os.walk(path):
                    for filename in filenames:
                        full_path = os.path.join(root, filename)
                        # 去掉开头的/
                        rel_path = full_path[1:]
                        files_to_pack.add(rel_path)
            else:
                # 对于项目内的目录，只添加指定目录下的文件
                base_dir = path  # 例如 "models/hngpt-mini"
                if os.path.exists(base_dir):
                    for root, _, filenames in os.walk(base_dir):
                        for filename in filenames:
                            full_path = os.path.join(root, filename)
                            # 添加 workspace/hngpt 前缀
                            rel_path = os.path.join("workspace/hngpt", full_path)
                            files_to_pack.add(rel_path)
            logging.info(f"添加目录: {path}")
        else:
            if path.startswith('/'):
                files_to_pack.add(path[1:])  # 系统文件，去掉开头的/
            else:
                # 项目文件，添加 workspace/hngpt 前缀
                rel_path = os.path.join("workspace/hngpt", path)
                files_to_pack.add(rel_path)
            logging.info(f"添加文件: {path}")

    if missing_paths:
        logging.error(f"以下路径不存在: {', '.join(missing_paths)}")
        return False

    try:
        # 如果已存在则删除旧的更新包
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的 {output_file}")

        # 创建新的tar.gz文件
        with tarfile.open(output_file, "w:gz") as tar:
            for file_path in sorted(files_to_pack):  # 排序以保持稳定的打包顺序
                # 源文件路径需要加回/（如果是系统路径）
                src_path = f"/{file_path}" if file_path.startswith(('usr/', 'lib/')) else file_path.replace('workspace/hngpt/', '')
                logging.info(f"正在添加: {file_path}")
                tar.add(src_path, file_path)

        # 验证文件大小
        size = os.path.getsize(output_file)
        logging.info(f"更新包创建成功: {output_file} (大小: {size/1024/1024:.2f} MB)")

        return True

    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False


def create_update_package(paths_to_pack):
    """创建更新包"""
    output_file = "update.tar.gz"
    missing_paths = []
    files_to_pack = set()

    # 检查是否有root权限
    if os.geteuid() != 0:
        logging.error("需要root权限来访问系统目录，请使用sudo运行")
        return False

    # 处理文件和目录
    for path in paths_to_pack:
        if not os.path.exists(path):
            missing_paths.append(path)
            continue

        if os.path.isdir(path):
            # 如果是系统路径（以/开头）
            if path.startswith('/'):
                for root, _, filenames in os.walk(path):
                    for filename in filenames:
                        full_path = os.path.join(root, filename)
                        # 保持完整路径，但去掉开头的/
                        rel_path = full_path[1:]  # 去掉开头的/
                        files_to_pack.add((full_path, rel_path))
            else:
                # 对于项目内的目录，添加到workspace/hngpt/
                for root, _, filenames in os.walk(path):
                    for filename in filenames:
                        full_path = os.path.join(root, filename)
                        # 添加到workspace/hngpt/
                        rel_path = os.path.join("workspace/hngpt", full_path)
                        files_to_pack.add((full_path, rel_path))
                        # 如果是 app.conf，则同时安装到 /etc/supervisor/conf.d/
                        if os.path.basename(full_path) == "app.conf":
                            files_to_pack.add((full_path, "etc/supervisor/conf.d/app.conf"))
        else:
            if path.startswith('/'):
                # 系统文件保持原始路径，但去掉开头的/
                rel_path = path[1:]  # 去掉开头的/
                files_to_pack.add((path, rel_path))
            else:
                # 项目文件添加到workspace/hngpt/
                rel_path = os.path.join("workspace/hngpt", path)
                files_to_pack.add((path, rel_path))
                if os.path.basename(path) == "app.conf":
                    files_to_pack.add((path, "etc/supervisor/conf.d/app.conf"))

    if missing_paths:
        logging.error(f"以下路径不存在: {', '.join(missing_paths)}")
        return False

    try:
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的 {output_file}")

        with tarfile.open(output_file, "w:gz") as tar:
            for src_path, arc_path in sorted(files_to_pack):
                logging.info(f"正在添加: {arc_path}")
                tar.add(src_path, arc_path)

        size = os.path.getsize(output_file)
        logging.info(f"更新包创建成功: {output_file} (大小: {size/1024/1024:.2f} MB)")
        return True

    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False

def create_update_package_with_ollama(paths_to_pack, ollama_temp_dir=None):
    """创建包含Ollama的更新包"""
    output_file = "update.tar.gz"
    missing_paths = []
    files_to_pack = set()

    # 检查是否有root权限
    if os.geteuid() != 0:
        logging.error("需要root权限来访问系统目录，请使用sudo运行")
        return False

    # 处理文件和目录
    for path in paths_to_pack:
        # 特殊处理Ollama文件
        if ollama_temp_dir and path.startswith(ollama_temp_dir):
            if not os.path.exists(path):
                missing_paths.append(path)
                continue

            # 确定目标路径
            if path.endswith("/bin/ollama") or path.endswith("bin/ollama"):
                # Ollama可执行文件 -> /usr/bin/ollama
                target_path = "usr/bin/ollama"
                files_to_pack.add((path, target_path))
                logging.info(f"添加Ollama可执行文件: {path} -> {target_path}")
            elif "/lib/ollama" in path:
                # Ollama库目录 -> /usr/lib/ollama/
                if os.path.isdir(path):
                    for root, _, filenames in os.walk(path):
                        for filename in filenames:
                            full_path = os.path.join(root, filename)
                            # 计算相对于ollama库目录的路径
                            rel_to_lib = os.path.relpath(full_path, path)
                            target_path = os.path.join("usr/lib/ollama", rel_to_lib)
                            files_to_pack.add((full_path, target_path))
                            logging.info(f"添加Ollama库文件: {full_path} -> {target_path}")
                else:
                    # 单个文件
                    target_path = "usr/lib/ollama/" + os.path.basename(path)
                    files_to_pack.add((path, target_path))
                    logging.info(f"添加Ollama库文件: {path} -> {target_path}")
            continue

        # 处理普通项目文件
        if not os.path.exists(path):
            missing_paths.append(path)
            continue

        if os.path.isdir(path):
            # 项目目录
            for root, _, filenames in os.walk(path):
                for filename in filenames:
                    full_path = os.path.join(root, filename)
                    # 添加到workspace/hngpt/
                    rel_path = os.path.join("workspace/hngpt", full_path)
                    files_to_pack.add((full_path, rel_path))
        else:
            # 项目文件
            rel_path = os.path.join("workspace/hngpt", path)
            files_to_pack.add((path, rel_path))
            logging.info(f"添加项目文件: {path}")

    if missing_paths:
        logging.error(f"以下路径不存在: {', '.join(missing_paths)}")
        return False

    try:
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的 {output_file}")

        with tarfile.open(output_file, "w:gz") as tar:
            for src_path, arc_path in sorted(files_to_pack):
                logging.info(f"正在添加: {arc_path}")
                tar.add(src_path, arc_path)

        size = os.path.getsize(output_file)
        logging.info(f"更新包创建成功: {output_file} (大小: {size/1024/1024:.2f} MB)")
        return True

    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False



def create_update_package_with_installed_ollama(project_paths):
    """创建包含系统已安装Ollama的更新包，统一部署到 /usr
    - 可执行文件目标: /usr/bin/ollama
    - 库文件目标: /usr/lib/ollama/
    - 项目文件目标: workspace/hngpt/...
    """
    output_file = "update.tar.gz"
    files_to_pack = []
    missing_paths = []

    # 检查root权限（访问系统目录需要）
    if os.geteuid() != 0:
        logging.error("需要root权限来访问系统目录，请使用sudo运行")
        return False

    # 1) 添加项目文件
    for path in project_paths:
        if not os.path.exists(path):
            missing_paths.append(path)
            continue
        if os.path.isdir(path):
            for root, _, filenames in os.walk(path):
                for filename in filenames:
                    full_path = os.path.join(root, filename)
                    rel_path = os.path.join("workspace/hngpt", full_path)
                    files_to_pack.append((full_path, rel_path))
                    # 如果是 app.conf，则同时安装到 /etc/supervisor/conf.d/
                    if os.path.basename(full_path) == "app.conf":
                        files_to_pack.append((full_path, "etc/supervisor/conf.d/app.conf"))
        else:
            rel_path = os.path.join("workspace/hngpt", path)
            files_to_pack.append((path, rel_path))
            if os.path.basename(path) == "app.conf":
                files_to_pack.append((path, "etc/supervisor/conf.d/app.conf"))

    # 2) 采集系统已安装的Ollama（优先 /usr，其次 /usr/local）
    bin_src = "/usr/bin/ollama" if os.path.exists("/usr/bin/ollama") else "/usr/local/bin/ollama"
    lib_src = "/usr/lib/ollama" if os.path.exists("/usr/lib/ollama") else "/usr/local/lib/ollama"

    if not os.path.exists(bin_src):
        logging.error(f"未找到Ollama可执行文件: {bin_src}")
        return False

    # 可执行文件 -> /usr/bin/ollama
    files_to_pack.append((bin_src, "usr/bin/ollama"))
    logging.info(f"添加Ollama可执行文件: {bin_src} -> /usr/bin/ollama")

    # 库目录（如果存在） -> /usr/lib/ollama/
    if os.path.exists(lib_src):
        for root, _, filenames in os.walk(lib_src):
            for filename in filenames:
                src_path = os.path.join(root, filename)
                rel_to_lib = os.path.relpath(src_path, lib_src)
                dst_path = os.path.join("usr/lib/ollama", rel_to_lib)
                files_to_pack.append((src_path, dst_path))
        logging.info(f"添加Ollama库目录: {lib_src} -> /usr/lib/ollama/")
    else:
        logging.warning("未找到Ollama库目录，将不包含库文件")

    if missing_paths:
        logging.warning(f"以下项目路径不存在，将被跳过: {', '.join(missing_paths)}")

    # 3) 创建更新包
    try:
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的 {output_file}")

        with tarfile.open(output_file, "w:gz") as tar:
            for src_path, arc_path in sorted(files_to_pack):
                logging.info(f"正在添加: {arc_path}")
                tar.add(src_path, arc_path)

        size = os.path.getsize(output_file)
        logging.info(f"更新包创建成功: {output_file} (大小: {size/1024/1024:.2f} MB)")
        return True
    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False

def download_ollama():
    """下载最新版本的Ollama"""
    import urllib.request
    import shutil

    ollama_url = "https://ollama.com/download/ollama-linux-amd64.tgz"
    ollama_file = "ollama-linux-amd64.tgz"

    logging.info("正在下载最新版本的Ollama...")

    try:
        # 下载Ollama
        with urllib.request.urlopen(ollama_url) as response:
            with open(ollama_file, 'wb') as f:
                shutil.copyfileobj(response, f)

        logging.info(f"Ollama下载完成: {ollama_file}")

        # 解压到临时目录
        temp_dir = "temp_ollama"
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)
        os.makedirs(temp_dir)

        with tarfile.open(ollama_file, "r:gz") as tar:
            tar.extractall(temp_dir)

        logging.info(f"Ollama解压完成到: {temp_dir}")

        # 清理下载文件
        os.remove(ollama_file)

        return temp_dir

    except Exception as e:
        logging.error(f"下载Ollama失败: {str(e)}")
        return None



def print_usage():
    logging.info("用法: sudo python update.py [选项]")
    logging.info("选项:")
    logging.info("  --include-ollama          打包并包含系统中已安装的 Ollama (默认)")
    logging.info("  --no-ollama               只打包项目文件，不包含 Ollama")
    logging.info("  --from-installed          从系统安装采集 Ollama (默认)")
    logging.info("  --from-tgz <path|url>     从指定 tgz 文件或 URL 下载解压后打包 Ollama")
    logging.info("  --project <path>          追加要打包的项目路径，可重复指定多次")
    logging.info("示例:")
    logging.info("  sudo python update.py --no-ollama")
    logging.info("  sudo python update.py --from-tgz ./ollama-linux-amd64.tgz")


def main():
    setup_logging("log")
    logging.info("开始创建HNGPT-AI更新包...")

    # 解析命令行参数
    args = sys.argv[1:]
    include_ollama = False
    source = "installed"  # installed | tgz
    tgz_path_or_url = None
    extra_projects = []

    i = 0
    while i < len(args):
        arg = args[i]
        if arg in ("-h", "--help"):
            print_usage()
            return
        elif arg == "--no-ollama":
            include_ollama = False
            i += 1
            continue
        elif arg == "--include-ollama":
            include_ollama = True
            i += 1
            continue
        elif arg == "--from-installed":
            source = "installed"
            i += 1
            continue
        elif arg == "--from-tgz":
            source = "tgz"
            if i + 1 >= len(args):
                logging.error("--from-tgz 需要一个路径或URL参数")
                return
            tgz_path_or_url = args[i+1]
            i += 2
            continue
        elif arg == "--project":
            if i + 1 >= len(args):
                logging.error("--project 需要一个路径参数")
                return
            extra_projects.append(args[i+1])
            i += 2
            continue
        else:
            logging.warning(f"未知参数: {arg}")
            i += 1

    # 需要打包的项目路径列表（可根据需要调整）
    project_paths = [
        "layout_content_extractor.py",
    ] + extra_projects

    success = False

    if include_ollama:
        if source == "installed":
            success = create_update_package_with_installed_ollama(project_paths)
        else:
            # 使用 tgz 作为来源
            # 如果传入的是URL，下载到本地；如果是本地路径，直接使用
            temp_dir = None
            local_tgz = None
            try:
                if tgz_path_or_url is None:
                    # 如果未指定，默认使用官方URL
                    tgz_path_or_url = "https://ollama.com/download/ollama-linux-amd64.tgz"

                if tgz_path_or_url.startswith("http://") or tgz_path_or_url.startswith("https://"):
                    import urllib.request
                    local_tgz = "ollama-linux-amd64.tgz"
                    logging.info(f"下载Ollama: {tgz_path_or_url}")
                    with urllib.request.urlopen(tgz_path_or_url) as response, open(local_tgz, 'wb') as out:
                        shutil.copyfileobj(response, out)
                else:
                    local_tgz = tgz_path_or_url

                # 解压到临时目录
                temp_dir = "temp_ollama"
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                os.makedirs(temp_dir)
                with tarfile.open(local_tgz, "r:gz") as tar:
                    tar.extractall(temp_dir)

                # 将解压出的 Ollama 安装到系统目录（/usr/bin 和 /usr/lib），然后从系统目录打包
                if os.geteuid() != 0:
                    logging.error("需要root权限才能将 Ollama 安装到系统目录，请使用 sudo 运行")
                    return

                src_bin = os.path.join(temp_dir, "bin", "ollama")
                src_lib_dir = os.path.join(temp_dir, "lib", "ollama")

                if not os.path.exists(src_bin):
                    raise FileNotFoundError(f"未找到解压后的可执行文件: {src_bin}")

                # 预清理旧版本
                for p in ["/usr/bin/ollama", "/usr/local/bin/ollama"]:
                    try:
                        if os.path.exists(p):
                            os.remove(p)
                    except Exception as e:
                        logging.warning(f"删除旧二进制失败 {p}: {e}")
                for d in ["/usr/lib/ollama", "/usr/local/lib/ollama"]:
                    try:
                        if os.path.isdir(d):
                            shutil.rmtree(d)
                    except Exception as e:
                        logging.warning(f"删除旧库目录失败 {d}: {e}")

                # 安装新版本
                os.makedirs("/usr/bin", exist_ok=True)
                os.makedirs("/usr/lib", exist_ok=True)
                shutil.copy2(src_bin, "/usr/bin/ollama")
                os.chmod("/usr/bin/ollama", 0o755)
                if os.path.exists(src_lib_dir):
                    shutil.copytree(src_lib_dir, "/usr/lib/ollama", symlinks=True)
                logging.info("已将 Ollama 安装到系统目录: /usr/bin 和 /usr/lib")

                # 统一从系统目录采集并打包
                success = create_update_package_with_installed_ollama(project_paths)
            except Exception as e:
                logging.error(f"处理 tgz 来源时出错: {e}")
                success = False
            finally:
                if temp_dir and os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                if local_tgz and os.path.exists(local_tgz) and local_tgz != tgz_path_or_url:
                    try:
                        os.remove(local_tgz)
                    except Exception:
                        pass
    else:
        # 不包含Ollama，仅打包项目
        success = create_update_package(project_paths)

    if success:
        logging.info("更新包创建完成")
        logging.info("\n🚀 HNGPT-AI更新包使用说明:")
        logging.info("=" * 50)
        logging.info("1. 将 update.tar.gz 复制到目标机器")
        logging.info("2. 在目标机器上执行以下命令:")
        logging.info("")
        logging.info("   # 停止所有服务")
        logging.info("   sudo supervisorctl stop all")
        logging.info("   sudo pkill -f ollama")
        logging.info("")
        logging.info("   # 备份旧版本 (可选)")
        logging.info("   sudo cp -r /workspace/hngpt /workspace/hngpt.backup.$(date +%Y%m%d)")
        logging.info("   sudo cp -r /usr/lib/ollama /usr/lib/ollama.backup.$(date +%Y%m%d) 2>/dev/null || true")
        logging.info("   sudo cp -r /usr/local/lib/ollama /usr/local/lib/ollama.backup.$(date +%Y%m%d) 2>/dev/null || true")
        logging.info("")
        if include_ollama:
            logging.info("   # 预清理旧版本 (重要)")
            logging.info("   sudo rm -f /usr/bin/ollama /usr/local/bin/ollama")
            logging.info("   sudo rm -rf /usr/lib/ollama /usr/local/lib/ollama")
            logging.info("")
        logging.info("   # 解压更新文件")
        logging.info("   sudo tar -xzvf update.tar.gz -C /")
        logging.info("")
        if include_ollama:
            logging.info("   # 设置权限")
            logging.info("   sudo chmod +x /usr/bin/ollama")
            logging.info("")
        logging.info("   # 启动服务")
        logging.info("   sudo supervisorctl start all")
        logging.info("")
        logging.info("✅ 更新完成后，HNGPT-AI和Ollama都将是最新版本" if include_ollama else "✅ 更新完成（未包含 Ollama）")
    else:
        logging.error("更新包创建失败")
        sys.exit(1)

if __name__ == "__main__":
    main()