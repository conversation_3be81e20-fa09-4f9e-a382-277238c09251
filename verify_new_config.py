#!/usr/bin/env python3
"""
验证新配置的效果
测试调整后的并发参数是否改善了性能
"""

import asyncio
import aiohttp
import time
import json

async def test_optimized_config():
    """测试优化后的配置"""
    base_url = "http://localhost:8888"
    headers = {"Authorization": "Bearer startfrom2023", "Content-Type": "application/json"}

    print("🧪 验证优化后的并发配置")
    print("=" * 50)

    # 测试场景 - 只测试Chat和Embedding，因为OCR路由已修复但可能仍有问题
    test_scenarios = [
        {
            "name": "Chat - 推荐并发 (2个)",
            "type": "chat",
            "concurrent": 2,
            "payload": {
                "model": "hngpt-mini:latest",
                "messages": [{"role": "user", "content": "Test optimized config"}],
                "stream": False
            },
            "endpoint": "/v1/chat/completions"
        },
        {
            "name": "Chat - 超出推荐 (4个)",
            "type": "chat", 
            "concurrent": 4,
            "payload": {
                "model": "hngpt-mini:latest",
                "messages": [{"role": "user", "content": "Test over limit"}],
                "stream": False
            },
            "endpoint": "/v1/chat/completions"
        },
        {
            "name": "Embedding - 推荐并发 (6个)",
            "type": "embedding",
            "concurrent": 6,
            "payload": {
                "model": "hngpt-embedding",
                "input": "Test optimized embedding config"
            },
            "endpoint": "/v1/embeddings"
        },
        {
            "name": "Embedding - 超出推荐 (8个)",
            "type": "embedding",
            "concurrent": 8,
            "payload": {
                "model": "hngpt-embedding", 
                "input": "Test over embedding limit"
            },
            "endpoint": "/v1/embeddings"
        }
    ]
    
    results = []
    
    for scenario in test_scenarios:
        print(f"\n🔍 {scenario['name']}")
        print("-" * 40)
        
        async with aiohttp.ClientSession() as session:
            # 创建并发任务
            tasks = []
            for i in range(scenario['concurrent']):
                payload = scenario['payload'].copy()
                if scenario['type'] == 'chat':
                    payload['messages'][0]['content'] += f" #{i}"
                else:
                    payload['input'] += f" #{i}"
                
                task = session.post(
                    f"{base_url}{scenario['endpoint']}", 
                    json=payload, 
                    headers=headers, 
                    timeout=30
                )
                tasks.append(task)
            
            # 执行测试
            start_time = time.time()
            try:
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                total_time = time.time() - start_time
                
                # 统计结果
                success = 0
                errors = 0
                status_codes = {}
                response_times = []
                
                for i, response in enumerate(responses):
                    if isinstance(response, Exception):
                        errors += 1
                        print(f"  请求 {i+1}: ❌ 异常 - {str(response)[:50]}")
                    else:
                        if response.status == 200:
                            success += 1
                            print(f"  请求 {i+1}: ✅ 成功")
                        else:
                            errors += 1
                            status_codes[response.status] = status_codes.get(response.status, 0) + 1
                            print(f"  请求 {i+1}: ❌ HTTP {response.status}")
                        response.close()
                
                success_rate = success / scenario['concurrent'] * 100
                throughput = success / total_time if total_time > 0 else 0
                
                result = {
                    "scenario": scenario['name'],
                    "type": scenario['type'],
                    "concurrent": scenario['concurrent'],
                    "success": success,
                    "errors": errors,
                    "success_rate": success_rate,
                    "total_time": total_time,
                    "throughput": throughput,
                    "status_codes": status_codes
                }
                results.append(result)
                
                # 输出总结
                if success_rate >= 90:
                    status_icon = "✅"
                elif success_rate >= 70:
                    status_icon = "⚠️"
                else:
                    status_icon = "❌"
                
                print(f"\n{status_icon} 总结:")
                print(f"  成功率: {success_rate:.1f}% ({success}/{scenario['concurrent']})")
                print(f"  总耗时: {total_time:.2f}s")
                print(f"  吞吐量: {throughput:.2f} req/s")
                
                if status_codes:
                    print(f"  错误状态码: {status_codes}")
                
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                results.append({
                    "scenario": scenario['name'],
                    "error": str(e)
                })
        
        # 等待一下再进行下一个测试
        await asyncio.sleep(3)
    
    # 输出最终总结
    print(f"\n{'='*60}")
    print("📊 配置验证总结")
    print(f"{'='*60}")
    
    for result in results:
        if 'error' not in result:
            if result['success_rate'] >= 90:
                status = "✅ 优秀"
            elif result['success_rate'] >= 70:
                status = "⚠️ 可接受"
            else:
                status = "❌ 需要调整"
            
            print(f"{result['scenario']}: {status} ({result['success_rate']:.1f}%)")
        else:
            print(f"{result['scenario']}: ❌ 测试失败")
    
    print(f"\n💡 建议:")
    
    # 分析Chat结果
    chat_results = [r for r in results if r.get('type') == 'chat' and 'error' not in r]
    if chat_results:
        chat_2 = next((r for r in chat_results if r['concurrent'] == 2), None)
        chat_4 = next((r for r in chat_results if r['concurrent'] == 4), None)
        
        if chat_2 and chat_4:
            if chat_2['success_rate'] > chat_4['success_rate']:
                print("✅ Chat并发限制2是合理的")
            else:
                print("⚠️ Chat可能可以支持更高并发")
    
    # 分析Embedding结果
    embed_results = [r for r in results if r.get('type') == 'embedding' and 'error' not in r]
    if embed_results:
        embed_6 = next((r for r in embed_results if r['concurrent'] == 6), None)
        embed_8 = next((r for r in embed_results if r['concurrent'] == 8), None)
        
        if embed_6 and embed_8:
            if embed_6['success_rate'] >= 90 and embed_8['success_rate'] >= 90:
                print("💡 Embedding可能可以支持更高并发")
            elif embed_6['success_rate'] >= 90:
                print("✅ Embedding并发限制6是合理的")
            else:
                print("⚠️ Embedding并发可能需要降低")
    
    return results

async def main():
    """主函数"""
    try:
        # 检查服务器连接
        async with aiohttp.ClientSession() as session:
            async with session.get("http://localhost:8888/health") as response:
                if response.status != 200:
                    print("❌ 服务器连接失败，请确保服务器正在运行")
                    return
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return
    
    # 运行验证测试
    results = await test_optimized_config()
    
    # 保存结果
    with open("config_verification_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    print(f"\n✅ 验证完成，详细结果已保存到 config_verification_results.json")

if __name__ == "__main__":
    asyncio.run(main())
