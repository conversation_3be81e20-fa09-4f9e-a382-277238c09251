#!/usr/bin/env python3
"""
Base64图片编码工具
用于将图片文件转换为base64编码，方便HTTP请求使用
"""

import base64
import os
import sys
from pathlib import Path
import mimetypes


def image_to_base64(image_path, include_data_url=False):
    """
    将图片文件转换为base64编码
    
    Args:
        image_path (str): 图片文件路径
        include_data_url (bool): 是否包含data URL前缀
    
    Returns:
        str: base64编码字符串
    """
    try:
        # 检查文件是否存在
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        # 读取图片文件
        with open(image_path, 'rb') as image_file:
            image_data = image_file.read()
        
        # 转换为base64编码
        base64_encoded = base64.b64encode(image_data).decode('utf-8')
        
        if include_data_url:
            # 获取MIME类型
            mime_type, _ = mimetypes.guess_type(image_path)
            if mime_type is None:
                # 根据文件扩展名推断MIME类型
                ext = Path(image_path).suffix.lower()
                mime_map = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.bmp': 'image/bmp',
                    '.webp': 'image/webp',
                    '.tiff': 'image/tiff',
                    '.tif': 'image/tiff'
                }
                mime_type = mime_map.get(ext, 'image/jpeg')
            
            # 返回完整的data URL
            return f"data:{mime_type};base64,{base64_encoded}"
        
        return base64_encoded
        
    except Exception as e:
        raise Exception(f"转换图片为base64失败: {str(e)}")


def base64_to_image(base64_string, output_path):
    """
    将base64编码转换为图片文件
    
    Args:
        base64_string (str): base64编码字符串
        output_path (str): 输出图片文件路径
    """
    try:
        # 如果是data URL格式，提取base64部分
        if base64_string.startswith('data:'):
            base64_string = base64_string.split(',')[1]
        
        # 解码base64
        image_data = base64.b64decode(base64_string)
        
        # 写入文件
        with open(output_path, 'wb') as image_file:
            image_file.write(image_data)
        
        print(f"✅ 图片已保存到: {output_path}")
        
    except Exception as e:
        raise Exception(f"base64转换为图片失败: {str(e)}")


def get_image_info(image_path):
    """
    获取图片文件信息
    
    Args:
        image_path (str): 图片文件路径
    
    Returns:
        dict: 图片信息
    """
    try:
        file_stat = os.stat(image_path)
        mime_type, _ = mimetypes.guess_type(image_path)
        
        return {
            "path": image_path,
            "size_bytes": file_stat.st_size,
            "size_kb": round(file_stat.st_size / 1024, 2),
            "size_mb": round(file_stat.st_size / (1024 * 1024), 2),
            "mime_type": mime_type,
            "extension": Path(image_path).suffix.lower()
        }
    except Exception as e:
        raise Exception(f"获取图片信息失败: {str(e)}")


def create_http_request_example(image_path, api_url="http://localhost:8888/ocr"):
    """
    创建HTTP请求示例代码
    
    Args:
        image_path (str): 图片文件路径
        api_url (str): API接口URL
    
    Returns:
        str: HTTP请求示例代码
    """
    try:
        base64_data = image_to_base64(image_path)
        
        # Python requests示例
        python_example = f'''
# Python requests示例
import requests
import base64

# 图片base64编码
image_base64 = "{base64_data[:100]}..."  # 已截断显示

# 方法1: 直接发送base64编码的图片数据
response = requests.post(
    "{api_url}",
    data=base64.b64decode(image_base64),
    headers={{"Content-Type": "application/octet-stream"}},
    params={{"wait": "true"}}
)

# 方法2: 发送原始图片字节数据
with open("{image_path}", "rb") as f:
    image_data = f.read()

response = requests.post(
    "{api_url}",
    data=image_data,
    headers={{"Content-Type": "application/octet-stream"}},
    params={{"wait": "true"}}
)

print(response.json())
'''
        
        # curl示例
        curl_example = f'''
# curl示例
curl -X POST "{api_url}?wait=true" \\
  -H "Content-Type: application/octet-stream" \\
  --data-binary @"{image_path}"
'''
        
        return {
            "python": python_example,
            "curl": curl_example,
            "base64_length": len(base64_data)
        }
        
    except Exception as e:
        raise Exception(f"创建HTTP请求示例失败: {str(e)}")


def main():
    """命令行主函数"""
    if len(sys.argv) < 2:
        print("用法:")
        print("  python base64image.py <图片路径> [选项]")
        print("")
        print("选项:")
        print("  --data-url    包含data URL前缀")
        print("  --info        显示图片信息")
        print("  --example     生成HTTP请求示例")
        print("  --output <文件>  保存base64到文件")
        print("")
        print("示例:")
        print("  python base64image.py image.jpg")
        print("  python base64image.py image.jpg --data-url")
        print("  python base64image.py image.jpg --info")
        print("  python base64image.py image.jpg --example")
        print("  python base64image.py image.jpg --output base64.txt")
        return
    
    image_path = sys.argv[1]
    
    try:
        # 显示图片信息
        if "--info" in sys.argv:
            info = get_image_info(image_path)
            print("📷 图片信息:")
            print(f"  路径: {info['path']}")
            print(f"  大小: {info['size_bytes']} bytes ({info['size_kb']} KB, {info['size_mb']} MB)")
            print(f"  类型: {info['mime_type']}")
            print(f"  扩展名: {info['extension']}")
            print()
        
        # 生成HTTP请求示例
        if "--example" in sys.argv:
            examples = create_http_request_example(image_path)
            print("🌐 HTTP请求示例:")
            print(f"Base64编码长度: {examples['base64_length']} 字符")
            print("\n" + "="*50)
            print(examples['python'])
            print("="*50)
            print(examples['curl'])
            print("="*50)
            return
        
        # 转换为base64
        include_data_url = "--data-url" in sys.argv
        base64_result = image_to_base64(image_path, include_data_url)
        
        # 保存到文件
        if "--output" in sys.argv:
            output_index = sys.argv.index("--output") + 1
            if output_index < len(sys.argv):
                output_file = sys.argv[output_index]
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(base64_result)
                print(f"✅ Base64编码已保存到: {output_file}")
            else:
                print("❌ --output 选项需要指定输出文件名")
        else:
            # 输出到控制台
            print("📝 Base64编码:")
            if len(base64_result) > 200:
                print(f"{base64_result[:100]}...{base64_result[-100:]}")
                print(f"(完整长度: {len(base64_result)} 字符)")
            else:
                print(base64_result)
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
