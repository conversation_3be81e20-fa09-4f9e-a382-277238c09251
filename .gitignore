# Python
__pycache__/
*.py[cod]
*$py.class
*.so
*.egg
*.egg-info/
dist/
build/
.pytest_cache/
.coverage
.tox/
.venv/
venv/
env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Logs
*.log
*.log.*
log/
logs/
supervisord.log*
supervisord.pid

# Large files
*.tgz
*.tar.gz
*.zip
*.rar
ollama-linux-amd64.tgz

# Model files
models/
*.bin
*.safetensors
*.gguf

# Temporary files
tmp/
temp/
.tmp/
ComboOCR
tests
PP-DocLayout-L
# Cache
.cache/
.hypothesis/

# OCR keys (sensitive)
ocr_keys.txt

# Backup files
*.bk
*-bk
app.py-bk

# Compiled extensions
*.so

# Local configuration
.env
.env.local
config.local.*

# Test outputs
test_output/
test_results/

# OCR test results and performance images
ocr/*.png
ocr/result*.png
ocr/performance_result*.png

# Large model directories
PP-DocLayout-L/
tests/

# Large static files
static/swagger-ui/swagger-ui-bundle.js
