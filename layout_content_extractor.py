"""
版面内容提取模块
提供基于版面分析的页面内容提取功能
"""

import cv2
import numpy as np
import timefinder


def calculate_iou(box1, box2):
    """计算两个边界框的IoU"""
    x1_1, y1_1, x2_1, y2_1 = box1
    x1_2, y1_2, x2_2, y2_2 = box2
    
    # 计算交集区域
    x1_inter = max(x1_1, x1_2)
    y1_inter = max(y1_1, y1_2)
    x2_inter = min(x2_1, x2_2)
    y2_inter = min(y2_1, y2_2)
    
    if x2_inter <= x1_inter or y2_inter <= y1_inter:
        return 0.0
    
    inter_area = (x2_inter - x1_inter) * (y2_inter - y1_inter)
    
    # 计算并集区域
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union_area = area1 + area2 - inter_area
    
    if union_area <= 0:
        return 0.0
    
    return inter_area / union_area


def extract_page_content_by_layout(layout_results, ocr_results, original_image_shape):
    """
    按照版面布局的阅读顺序提取页面内容
    
    Args:
        layout_results: 版面分析结果
        ocr_results: OCR识别结果
        original_image_shape: 原图尺寸
    
    Returns:
        dict: 包含pageContent, sealContent, handWriteContent, dateContent的结果
    """
    original_height, original_width = original_image_shape[:2]
    
    # 初始化时间查找器
    time_finder = timefinder.TimeFinder()
    
    # 预处理OCR结果，转换为统一格式
    processed_ocr_results = []
    if ocr_results and len(ocr_results) > 0:
        for idx, ocr_result in enumerate(ocr_results):
            if len(ocr_result) >= 3:
                ocr_coordinate = ocr_result[0]
                text = ocr_result[1]
                confidence = ocr_result[2]

                if confidence > 0.3 and text.strip():
                    # OCR坐标转换为[x1, y1, x2, y2]格式
                    if len(ocr_coordinate) == 4 and len(ocr_coordinate[0]) == 2:
                        ocr_orig_x1 = min(point[0] for point in ocr_coordinate)
                        ocr_orig_y1 = min(point[1] for point in ocr_coordinate)
                        ocr_orig_x2 = max(point[0] for point in ocr_coordinate)
                        ocr_orig_y2 = max(point[1] for point in ocr_coordinate)

                        processed_ocr_results.append({
                            "idx": idx,
                            "text": text,
                            "confidence": confidence,
                            "coordinate": [ocr_orig_x1, ocr_orig_y1, ocr_orig_x2, ocr_orig_y2],
                            "assigned_layout": -1
                        })

    # 为每个OCR文本框找到IoU最大的版面区域
    for ocr_idx, ocr_data in enumerate(processed_ocr_results):
        ocr_box = ocr_data["coordinate"]
        max_iou = 0
        best_layout_idx = -1

        for layout_idx, layout_result in enumerate(layout_results):
            layout_box = layout_result['coordinate']
            iou = calculate_iou(ocr_box, layout_box)
            
            if iou > max_iou:
                max_iou = iou
                best_layout_idx = layout_idx

        # 每个OCR文本框都必须分配给IoU最大的版面区域，无论IoU值多小
        if best_layout_idx >= 0:
            processed_ocr_results[ocr_idx]["assigned_layout"] = best_layout_idx

    # 按版面区域组织OCR文本
    layout_ocr_map = {}
    for ocr_data in processed_ocr_results:
        layout_idx = ocr_data["assigned_layout"]
        if layout_idx >= 0:
            if layout_idx not in layout_ocr_map:
                layout_ocr_map[layout_idx] = []
            layout_ocr_map[layout_idx].append({
                "text": ocr_data["text"],
                "coordinate": ocr_data["coordinate"]
            })

    # 按版面区域的位置排序（从上到下，从左到右）
    layout_with_positions = []
    for i, layout_result in enumerate(layout_results):
        x1, y1, x2, y2 = layout_result['coordinate']
        center_y = (y1 + y2) / 2
        center_x = (x1 + x2) / 2
        
        layout_with_positions.append({
            "index": i,
            "label": layout_result['label'],
            "coordinate": [x1, y1, x2, y2],
            "center_x": center_x,
            "center_y": center_y,
            "ocr_texts": layout_ocr_map.get(i, [])
        })

    # 按阅读顺序排序：先按Y坐标（从上到下），再按X坐标（从左到右）
    layout_with_positions.sort(key=lambda x: (x["center_y"], x["center_x"]))

    # 提取页面内容
    page_content_lines = []
    
    for layout_info in layout_with_positions:
        if not layout_info["ocr_texts"]:
            continue
            
        # 在当前版面区域内，按OCR文本框的位置排序
        ocr_texts = layout_info["ocr_texts"]
        
        # 按行组织OCR文本
        lines = []
        current_line = []
        current_y = None
        line_height_threshold = 20  # 行高阈值
        
        # 先按Y坐标排序
        ocr_texts.sort(key=lambda x: x["coordinate"][1])
        
        for ocr_text in ocr_texts:
            x1, y1, x2, y2 = ocr_text["coordinate"]
            center_y = (y1 + y2) / 2
            
            if current_y is None or abs(center_y - current_y) <= line_height_threshold:
                # 同一行
                current_line.append({
                    "text": ocr_text["text"],
                    "x": x1
                })
                if current_y is None:
                    current_y = center_y
            else:
                # 新的一行
                if current_line:
                    # 按X坐标排序当前行的文本
                    current_line.sort(key=lambda x: x["x"])
                    line_text = " ".join([item["text"] for item in current_line])
                    lines.append(line_text)
                
                current_line = [{
                    "text": ocr_text["text"],
                    "x": x1
                }]
                current_y = center_y
        
        # 处理最后一行
        if current_line:
            current_line.sort(key=lambda x: x["x"])
            line_text = " ".join([item["text"] for item in current_line])
            lines.append(line_text)
        
        # 将当前版面区域的所有行添加到页面内容中
        if lines:
            page_content_lines.extend(lines)

    # 组合最终的页面内容
    page_content = "\n".join(page_content_lines)

    # 检测印章和手写内容
    seal_content = []
    handwrite_content = []
    
    for layout_result in layout_results:
        if layout_result['label'] == 'seal':
            seal_content = ["has_seal"]
        elif layout_result['label'] == 'handwriting':
            handwrite_content = ["has_handwritten"]

    # 提取日期内容
    date_content = time_finder.find_time(page_content)

    # 构建结果
    result = {
        "pageContent": page_content,
        "sealContent": seal_content,
        "handWriteContent": handwrite_content,
        "dateContent": date_content
    }

    return result
