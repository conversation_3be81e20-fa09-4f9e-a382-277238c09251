# HNGPT-AI

一个基于Ollama的AI聊天和嵌入服务平台，支持多GPU负载均衡和多种embedding模型。

## 🚀 功能特性

### 核心功能
- 🤖 **AI聊天服务**: 支持流式和非流式对话
- 🔤 **文本嵌入**: 支持多种embedding模型 (hngpt-embedding, hngpt-embedding)
- ⚖️ **负载均衡**: 多GPU服务器智能分配
- 🔄 **队列管理**: 请求队列和处理状态监控
- 🔐 **认证系统**: Token认证和用户管理

### API兼容性
- ✅ **OpenAI兼容**: 支持OpenAI格式的API调用
- ✅ **标准格式**: 支持自定义API格式
- ✅ **流式响应**: 实时流式对话体验

## 📊 支持的模型

### Chat模型
- `hngpt`: 主要对话模型

### Embedding模型
- `hngpt-embedding`: 指令感知embedding模型 (1024维，支持归一化)
- `hngpt-embedding`: 兼容性embedding模型

## 🛠️ 安装和使用

### 环境要求
- Python 3.8+
- Ollama服务
- GPU支持 (推荐)

### 快速开始

1. **启动服务**
   ```bash
   python app.py
   ```

2. **Chat API调用**
   ```bash
   curl -X POST http://localhost:8888/v1/chat/completions \
     -H "Authorization: Bearer your_token" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "hngpt",
       "messages": [{"role": "user", "content": "你好"}]
     }'
   ```

3. **Embedding API调用**
   ```bash
   curl -X POST http://localhost:8888/v1/embeddings \
     -H "Authorization: Bearer your_token" \
     -H "Content-Type: application/json" \
     -d '{
       "model": "hngpt-embedding",
       "input": "你的文本"
     }'
   ```

## 🧪 测试工具

### 并发测试
```bash
python concurrent_api_test.py      # 完整并发测试
python simple_blocking_test.py     # 简化阻塞测试
```

### 相似度测试
```bash
python simple_similarity_test.py   # 相似度计算测试
python find_most_similar.py        # 最相似句子查找
```

### Ollama测试
```bash
python tests/test_ollama_concurrent.py  # Ollama并发能力测试
```

## 📈 性能特点

### Embedding API
- ✅ **真正并发**: 支持多请求并行处理
- ✅ **快速响应**: 平均响应时间 < 1秒
- ✅ **自动归一化**: hngpt-embedding自动L2归一化

### Chat API
- ⚠️ **串行处理**: Ollama限制，Chat请求排队处理
- 🔧 **优化建议**: 使用应用层限流和队列管理

## 🔧 配置说明

### 多GPU配置
系统自动检测GPU数量并配置相应的Ollama服务器：
- GPU 0: 端口 11434
- GPU 1: 端口 11433
- GPU 2: 端口 11432
- ...

### 认证配置
在 `authorized_users.txt` 中配置授权用户token。

## 📝 开发说明

### 项目结构
```
hngpt/
├── app.py                 # 主服务文件
├── static/               # 静态文件 (前端界面)
├── tests/                # 测试脚本
├── concurrent_api_test.py # 并发测试
├── simple_similarity_test.py # 相似度测试
└── README.md             # 项目说明
```

### 关键特性
- **指令感知**: hngpt-embedding支持任务特定指令优化
- **负载均衡**: 智能分配请求到不同GPU服务器
- **错误恢复**: 自动重试和故障转移机制

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

本项目遵循相应的开源许可证。

---

**快速开始**: `python app.py`

**问题反馈**: 请在GitHub/Gitee上提交Issue
