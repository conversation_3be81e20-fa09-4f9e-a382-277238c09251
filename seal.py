import os
import cv2
import numpy as np
import json
import statistics
from scipy.special import softmax
import random
import logging
from pathlib import Path
import sys
import threading
from datetime import datetime, timedelta
import time
import rec

# 延迟导入CUDA相关模块，避免启动时立即初始化
_cuda_initialized = False
_cuda_available = False

def init_cuda():
    """安全地初始化CUDA"""
    global _cuda_initialized, _cuda_available

    if _cuda_initialized:
        return _cuda_available

    try:
        import pycuda.driver as cuda
        import tensorrt as trt

        # 尝试初始化CUDA
        cuda.init()
        _cuda_available = True
        logging.info("✓ CUDA 初始化成功")

        # 将模块添加到全局命名空间以便后续使用
        globals()['cuda'] = cuda
        globals()['trt'] = trt

    except Exception as e:
        logging.error(f"❌ CUDA 初始化失败: {str(e)}")
        logging.error("  系统将在CPU模式下运行相关功能")
        _cuda_available = False

        # 提供fallback模块
        globals()['cuda'] = None
        globals()['trt'] = None

    _cuda_initialized = True
    return _cuda_available

# 尝试导入其他可能需要的模块
try:
    import onnxruntime
except ImportError:
    logging.warning("⚠️ ONNX Runtime 不可用")
    onnxruntime = None

# 在文件开头添加日志配置
def setup_logger():
    """设置日志格式"""
    formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    
    # 获取根日志记录器
    logger = logging.getLogger()
    
    # 清除现有的处理器
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # 添加控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 设置日志级别
    logger.setLevel(logging.INFO)

# 调用日志设置
setup_logger()

class YoLov5TRT(object):
    def __init__(self, engine_file_path, device_id):
        """初始化 YoLov5TRT"""
        self.device_id = device_id
        self.device = cuda.Device(device_id)
        self.ctx = self.device.make_context()
        
        try:
            self.stream = cuda.Stream()
            TRT_LOGGER = trt.Logger(trt.Logger.INFO)
            runtime = trt.Runtime(TRT_LOGGER)

            with open(engine_file_path, "rb") as f:
                self.engine = runtime.deserialize_cuda_engine(f.read())
            self.context = self.engine.create_execution_context()

            # 初始化内存
            self._init_buffers()
            
        except Exception as e:
            logging.error(f"Error initializing YoLov5TRT: {str(e)}")
            self.cleanup()
            raise
        finally:
            self.ctx.pop()

    def _init_buffers(self):
        """初始化缓冲区"""
        self.host_inputs = []
        self.cuda_inputs = []
        self.host_outputs = []
        self.cuda_outputs = []
        self.bindings = []
        
        for binding in self.engine:
            size = trt.volume(self.engine.get_binding_shape(binding)) * self.engine.max_batch_size
            dtype = trt.nptype(self.engine.get_binding_dtype(binding))
            host_mem = cuda.pagelocked_empty(size, dtype)
            cuda_mem = cuda.mem_alloc(host_mem.nbytes)
            self.bindings.append(int(cuda_mem))
            if self.engine.binding_is_input(binding):
                self.input_w = self.engine.get_binding_shape(binding)[-1]
                self.input_h = self.engine.get_binding_shape(binding)[-2]
                self.host_inputs.append(host_mem)
                self.cuda_inputs.append(cuda_mem)
            else:
                self.host_outputs.append(host_mem)
                self.cuda_outputs.append(cuda_mem)

    def infer(self, image_raw):
        """推理方法"""
        self.ctx.push()
        try:
            input_image, image_raw, origin_h, origin_w = self.preprocess_image(image_raw)
            np.copyto(self.host_inputs[0], input_image.ravel())
            cuda.memcpy_htod_async(self.cuda_inputs[0], self.host_inputs[0], self.stream)
            self.context.execute_async_v2(bindings=self.bindings, stream_handle=self.stream.handle)
            cuda.memcpy_dtoh_async(self.host_outputs[0], self.cuda_outputs[0], self.stream)
            self.stream.synchronize()
            output = self.host_outputs[0].reshape((-1,7))
            bbox = output[:,:5]
            cls = np.argmax(output[:,5:],axis = 1)
            res = np.c_[bbox, cls].ravel()
            res = np.insert(res, 0, output.shape[0])
            clses = ["handwritten", "seal"]
            contains = []
            result_boxes, result_scores, result_classid = self.post_process(res, origin_h, origin_w)
            for j in range(len(result_boxes)):
                box = result_boxes[j]
                cls = clses[int(result_classid[j])]
                score = result_scores[j]
                if score > 0.7:
                    item = {'cls': cls, 'box': box}
                    contains.append(item)
            return contains
        except Exception as e:
            logging.error(f"Error in YoLov5TRT.infer: {str(e)}")
            return []
        finally:
            self.ctx.pop()

    def cleanup(self):
        """清理资源"""
        try:
            # 释放CUDA内存
            if hasattr(self, 'cuda_inputs'):
                for cuda_inp in self.cuda_inputs:
                    if cuda_inp:
                        try:
                            cuda_inp.free()
                        except Exception as e:
                            logging.error(f"Error freeing cuda input: {str(e)}")
                            
            if hasattr(self, 'cuda_outputs'):
                for cuda_out in self.cuda_outputs:
                    if cuda_out:
                        try:
                            cuda_out.free()
                        except Exception as e:
                            logging.error(f"Error freeing cuda output: {str(e)}")
            
            # 删除TensorRT资源
            if hasattr(self, 'context'):
                del self.context
            if hasattr(self, 'engine'):
                del self.engine
                
            # 清理CUDA上下文
            if hasattr(self, 'ctx'):
                try:
                    self.ctx.pop()
                except Exception:
                    pass
                try:
                    self.ctx.detach()
                except Exception as e:
                    logging.error(f"Error detaching context: {str(e)}")
                
        except Exception as e:
            logging.error(f"Error in cleanup: {str(e)}")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception as e:
            logging.error(f"Error in YoLov5TRT.__del__: {str(e)}")

    def preprocess_image(self, raw_bgr_image):
        """预处理图像"""
        image_raw = raw_bgr_image
        h, w, c = image_raw.shape
        image = cv2.cvtColor(image_raw, cv2.COLOR_BGR2RGB)
        
        # 计算缩放比例
        r_w = self.input_w / w
        r_h = self.input_h / h
        if r_h > r_w:
            tw = self.input_w
            th = int(r_w * h)
            tx1 = tx2 = 0
            ty1 = int((self.input_h - th) / 2)
            ty2 = self.input_h - th - ty1
        else:
            tw = int(r_h * w)
            th = self.input_h
            tx1 = int((self.input_w - tw) / 2)
            tx2 = self.input_w - tw - tx1
            ty1 = ty2 = 0

        # 调整大小并添加填充
        image = cv2.resize(image, (tw, th))
        image = cv2.copyMakeBorder(
            image, ty1, ty2, tx1, tx2, cv2.BORDER_CONSTANT, None, (128, 128, 128)
        )
        
        # 归一化和转换格式
        image = image.astype(np.float32)
        image /= 255.0
        image = np.transpose(image, [2, 0, 1])
        image = np.expand_dims(image, axis=0)
        image = np.ascontiguousarray(image)
        
        return image, image_raw, h, w

    def post_process(self, output, origin_h, origin_w):
        """后处理输出结果"""
        num = int(output[0])
        pred = np.reshape(output[1:], (-1, 6))[:num, :]
        boxes = self.non_max_suppression(pred, origin_h, origin_w, conf_thres=0.25, nms_thres=0.45)
        result_boxes = boxes[:, :4] if len(boxes) else np.array([])
        result_scores = boxes[:, 4] if len(boxes) else np.array([])
        result_classid = boxes[:, 5] if len(boxes) else np.array([])
        return result_boxes, result_scores, result_classid

    def bbox_iou(self, box1, box2, x1y1x2y2=True):
        """计算边界框IoU"""
        if not x1y1x2y2:
            b1_x1, b1_x2 = box1[:, 0] - box1[:, 2] / 2, box1[:, 0] + box1[:, 2] / 2
            b1_y1, b1_y2 = box1[:, 1] - box1[:, 3] / 2, box1[:, 1] + box1[:, 3] / 2
            b2_x1, b2_x2 = box2[:, 0] - box2[:, 2] / 2, box2[:, 0] + box2[:, 2] / 2
            b2_y1, b2_y2 = box2[:, 1] - box2[:, 3] / 2, box2[:, 1] + box2[:, 3] / 2
        else:
            b1_x1, b1_y1, b1_x2, b1_y2 = box1[:, 0], box1[:, 1], box1[:, 2], box1[:, 3]
            b2_x1, b2_y1, b2_x2, b2_y2 = box2[:, 0], box2[:, 1], box2[:, 2], box2[:, 3]

        inter_rect_x1 = np.maximum(b1_x1, b2_x1)
        inter_rect_y1 = np.maximum(b1_y1, b2_y1)
        inter_rect_x2 = np.minimum(b1_x2, b2_x2)
        inter_rect_y2 = np.minimum(b1_y2, b2_y2)
        
        inter_area = np.clip(inter_rect_x2 - inter_rect_x1 + 1, 0, None) * \
                     np.clip(inter_rect_y2 - inter_rect_y1 + 1, 0, None)
        b1_area = (b1_x2 - b1_x1 + 1) * (b1_y2 - b1_y1 + 1)
        b2_area = (b2_x2 - b2_x1 + 1) * (b2_y2 - b2_y1 + 1)

        iou = inter_area / (b1_area + b2_area - inter_area + 1e-16)
        return iou

    def non_max_suppression(self, prediction, origin_h, origin_w, conf_thres=0.5, nms_thres=0.4):
        """非极大值抑制"""
        boxes = prediction[prediction[:, 4] >= conf_thres]
        boxes[:, :4] = self.xywh2xyxy(origin_h, origin_w, boxes[:, :4])
        boxes[:, 0] = np.clip(boxes[:, 0], 0, origin_w - 1)
        boxes[:, 2] = np.clip(boxes[:, 2], 0, origin_w - 1)
        boxes[:, 1] = np.clip(boxes[:, 1], 0, origin_h - 1)
        boxes[:, 3] = np.clip(boxes[:, 3], 0, origin_h - 1)
        confs = boxes[:, 4]
        boxes = boxes[np.argsort(-confs)]
        keep_boxes = []
        while boxes.shape[0]:
            large_overlap = self.bbox_iou(np.expand_dims(boxes[0, :4], 0), boxes[:, :4]) > nms_thres
            label_match = boxes[0, -1] == boxes[:, -1]
            invalid = large_overlap & label_match
            keep_boxes += [boxes[0]]
            boxes = boxes[~invalid]
        boxes = np.stack(keep_boxes, 0) if len(keep_boxes) else np.array([])
        return boxes

    def xywh2xyxy(self, origin_h, origin_w, x):
        """转换边界框格式从xywh到xyxy"""
        y = np.zeros_like(x)
        r_w = self.input_w / origin_w
        r_h = self.input_h / origin_h
        if r_h > r_w:
            y[:, 0] = x[:, 0] - x[:, 2] / 2
            y[:, 2] = x[:, 0] + x[:, 2] / 2
            y[:, 1] = x[:, 1] - x[:, 3] / 2 - (self.input_h - r_w * origin_h) / 2
            y[:, 3] = x[:, 1] + x[:, 3] / 2 - (self.input_h - r_w * origin_h) / 2
            y /= r_w
        else:
            y[:, 0] = x[:, 0] - x[:, 2] / 2 - (self.input_w - r_h * origin_w) / 2
            y[:, 2] = x[:, 0] + x[:, 2] / 2 - (self.input_w - r_h * origin_w) / 2
            y[:, 1] = x[:, 1] - x[:, 3] / 2
            y[:, 3] = x[:, 1] + x[:, 3] / 2
            y /= r_h
        return y

class OnnxEncoderDecoder(object):
    def __init__(self, model_path, gpu_id=None):
        """
        初始化编码器解码器
        model_path: 模型路径
        gpu_id: GPU ID，如果为None则使用CPU
        """
        self.gpu_id = gpu_id
        self.device = None
        self.ctx = None
        
        try:
            # 如果指定了GPU，创建CUDA上下文
            if gpu_id is not None:
                self.device = cuda.Device(gpu_id)
                self.ctx = self.device.make_context()
                logging.info(f"[OnnxEncoderDecoder] Created CUDA context for GPU {gpu_id}")
            
            # 根据是否指定GPU选择provider
            if gpu_id is not None:
                # 简化 CUDA provider 配置
                cuda_provider = {
                    'device_id': int(gpu_id)  # 确保是整数类型
                }
                providers = [
                    ('CUDAExecutionProvider', cuda_provider)
                ]
                logging.info(f"[OnnxEncoderDecoder] Using CUDA provider for GPU {gpu_id}")
            else:
                providers = ['CPUExecutionProvider']
                logging.info("[OnnxEncoderDecoder] Using CPU provider")
            
            # 加载词汇表
            vocab_path = os.path.join(model_path, "vocab.json")
            if not os.path.exists(vocab_path):
                raise FileNotFoundError(f"Vocabulary file not found: {vocab_path}")
                
            self.vocab = self.read_vocab(vocab_path)
            self.vocab_inp = {self.vocab[key]: key for key in self.vocab}
            self.threshold = 0.88
            self.max_len = 50
            
            # 创建会话选项
            session_options = onnxruntime.SessionOptions()
            session_options.graph_optimization_level = onnxruntime.GraphOptimizationLevel.ORT_ENABLE_ALL
            session_options.log_severity_level = 3  # WARNING
            session_options.enable_cpu_mem_arena = False
            
            # 初始化编码器和解码器
            encoder_path = os.path.join(model_path, "encoder_model.onnx")
            decoder_path = os.path.join(model_path, "decoder_model.onnx")
            
            try:
                # 加载编码器模型
                self.encoder = onnxruntime.InferenceSession(
                    encoder_path,
                    sess_options=session_options,
                    providers=providers
                )
                
                # 加载解码器模型
                self.decoder = onnxruntime.InferenceSession(
                    decoder_path,
                    sess_options=session_options,
                    providers=providers
                )
                
                # 获取输入名称
                self.encoder_input_name = self.encoder.get_inputs()[0].name
                self.decoder_input_names = {input_key.name: idx for idx, input_key in enumerate(self.decoder.get_inputs())}
                
                # 验证是否成功使用 CUDA
                if gpu_id is not None:
                    ep = self.encoder.get_providers()[0]
                    if ep != 'CUDAExecutionProvider':
                        logging.warning(f"[OnnxEncoderDecoder] Failed to use CUDA, falling back to {ep}")
                    else:
                        logging.info(f"[OnnxEncoderDecoder] Successfully using CUDA on GPU {gpu_id}")
                
            except Exception as e:
                logging.error(f"[OnnxEncoderDecoder] Error loading models: {str(e)}")
                raise
            
            logging.info(f"[OnnxEncoderDecoder] Successfully initialized on {'GPU '+str(gpu_id) if gpu_id is not None else 'CPU'}")
            
        except Exception as e:
            logging.error(f"[OnnxEncoderDecoder] Initialization error: {str(e)}")
            if self.ctx:
                try:
                    self.ctx.pop()
                except:
                    pass
            raise
        finally:
            if self.ctx:
                try:
                    self.ctx.pop()
                except:
                    pass

    def read_vocab(self, path):
        with open(path, encoding="utf-8") as f:
            vocab = json.load(f)
        return vocab

    def do_norm(self, x):
        mean = [0.5, 0.5, 0.5]
        std = [0.5, 0.5, 0.5]
        x = x / 255.0
        x[0, :, :] -= mean[0]
        x[1, :, :] -= mean[1]
        x[2, :, :] -= mean[2]
        x[0, :, :] /= std[0]
        x[1, :, :] /= std[1]
        x[2, :, :] /= std[2]
        return x

    def run(self, image):
        """处理输入图像"""
        if self.ctx:
            self.ctx.push()
            
        try:
            # 1. 输入验证
            if image is None or image.size == 0:
                logging.error("[OnnxEncoderDecoder] Empty input image")
                return "", 0.0
                
            if len(image.shape) != 3:
                logging.error(f"[OnnxEncoderDecoder] Invalid image shape: {image.shape}")
                return "", 0.0
            
            # 2. 图像预处理
            try:
                image = cv2.resize(image, (384, 384))
                pixel_values = cv2.split(np.array(image))
                pixel_values = self.do_norm(np.array(pixel_values))
                pixel_values = np.array([pixel_values])
                
                logging.info(f"[OnnxEncoderDecoder] Preprocessed image shape: {pixel_values.shape}")
            except Exception as e:
                logging.error(f"[OnnxEncoderDecoder] Preprocessing error: {str(e)}")
                return "", 0.0
            
            # 3. 编码器推理
            try:
                encoder_inputs = {self.encoder_input_name: pixel_values.astype(np.float32)}
                encoder_output = self.encoder.run(None, encoder_inputs)[0]
                
                if encoder_output is None:
                    logging.error("[OnnxEncoderDecoder] Encoder returned None")
                    return "", 0.0
                    
                logging.info(f"[OnnxEncoderDecoder] Encoder output shape: {encoder_output.shape}")
            except Exception as e:
                logging.error(f"[OnnxEncoderDecoder] Encoder error: {str(e)}")
                return "", 0.0
            
            # 4. 解码器推理
            try:
                ids = [self.vocab["<s>"]]
                mask = [1]
                scores = []
                
                # 获取结束标记ID
                eos_token = self.vocab.get("", -1)  # 使用get方法，提供默认值
                if eos_token == -1:
                    logging.warning("[OnnxEncoderDecoder] End of sequence token not found in vocabulary")
                
                for i in range(self.max_len):
                    try:
                        input_ids = np.array([ids], dtype=np.int64)
                        attention_mask = np.array([mask], dtype=np.int64)
                        
                        # 准备解码器输入
                        decoder_inputs = {
                            "input_ids": input_ids,
                            "attention_mask": attention_mask,
                            "encoder_hidden_states": encoder_output.astype(np.float32)
                        }
                        
                        # 运行解码器
                        outputs = self.decoder.run(['logits'], decoder_inputs)
                        if not outputs or len(outputs) == 0:
                            logging.error("[OnnxEncoderDecoder] Empty decoder outputs")
                            break
                            
                        logits = outputs[0]
                        if logits is None or not isinstance(logits, np.ndarray):
                            logging.error(f"[OnnxEncoderDecoder] Invalid logits type: {type(logits)}")
                            break
                            
                        # 获取最后一个时间步的预测
                        last_logits = logits[0, -1, :]
                        probs = softmax(last_logits)
                        pred_token = int(np.argmax(probs))
                        pred_score = float(probs[pred_token])
                        
                        logging.debug(f"[OnnxEncoderDecoder] Step {i}: token={pred_token}, score={pred_score:.4f}")
                        
                        # 检查是否到达结束标记
                        if eos_token != -1 and pred_token == eos_token:
                            logging.debug("[OnnxEncoderDecoder] Reached end of sequence token")
                            break
                            
                        # 更新序列
                        ids.append(pred_token)
                        mask.append(1)
                        scores.append(pred_score)
                        
                        # 如果序列太长，提前停止
                        if len(ids) >= self.max_len:
                            logging.warning("[OnnxEncoderDecoder] Reached maximum sequence length")
                            break
                            
                    except Exception as e:
                        logging.error(f"[OnnxEncoderDecoder] Error in decoding step {i}: {str(e)}")
                        if hasattr(e, '__traceback__'):
                            import traceback
                            logging.error(traceback.format_tb(e.__traceback__))
                        break
                
                # 检查是否有有效的得分
                if not scores:
                    logging.warning("[OnnxEncoderDecoder] No valid scores generated")
                    return "", 0.0
                
                # 计算平均得分
                mean_score = float(np.mean(scores))
                
                # 解码文本
                text = self.decode_text(ids)
                if not text:
                    logging.warning("[OnnxEncoderDecoder] Empty decoded text")
                    return "", 0.0
                
                logging.info(f"[OnnxEncoderDecoder] Successfully decoded text: {text} with score {mean_score:.4f}")
                return text, mean_score
                
            except Exception as e:
                logging.error(f"[OnnxEncoderDecoder] Decoder error: {str(e)}")
                return "", 0.0
                
        except Exception as e:
            logging.error(f"[OnnxEncoderDecoder] Error processing image: {str(e)}")
            return "", 0.0
            
        finally:
            if self.ctx:
                try:
                    self.ctx.pop()
                except:
                    pass

    def decode_text(self, tokens):
        """解码文本"""
        s_start = self.vocab.get('<s>')
        s_end = self.vocab.get('')
        unk = self.vocab.get('<unk>')
        pad = self.vocab.get('<pad>')
        text = ''
        for tk in tokens:
            if tk == s_end:
                break
            if tk not in [s_end, s_start, pad, unk]:
                text += self.vocab_inp[tk]
        return text

    def __del__(self):
        """释放资源"""
        try:
            self.encoder = None
            self.decoder = None
            
            # 清理CUDA上下文
            if self.ctx:
                try:
                    self.ctx.pop()
                except:
                    pass
                try:
                    self.ctx.detach()
                except:
                    pass
            
            import gc
            gc.collect()
            
        except Exception as e:
            logging.error(f"[OnnxEncoderDecoder] Error in __del__: {str(e)}")

# 添加新的ModelInstance类来管理单个GPU上的模型实例
class ModelInstance:
    def __init__(self, models_dir, device_id):
        self.device_id = device_id
        self.device = cuda.Device(device_id)
        self.ctx = self.device.make_context()
        
        try:
            logging.info(f"Initializing models on GPU {device_id}")
            
            # 检查模型文件
            model_paths = {
                "seal_model": os.path.join(models_dir, "seal.trt"),
                "seal_onnx": os.path.join(models_dir, "seal-onnx"),
                "hw_onnx": os.path.join(models_dir, "hw-onnx")
            }
            
            for key, path in model_paths.items():
                if not os.path.exists(path):
                    raise FileNotFoundError(f"Model not found: {path}")
            
            # 为当前GPU加载模型
            self.detector = YoLov5TRT(model_paths["seal_model"], device_id)
            logging.info(f"Loaded YoLov5TRT model on GPU {device_id}")
            
            # 加载印章和手写识别模型，指定GPU ID
            self.seal_recognizer = OnnxEncoderDecoder(model_paths["seal_onnx"], gpu_id=device_id)
            logging.info(f"Loaded seal recognition model on GPU {device_id}")
            
            self.hw_recognizer = OnnxEncoderDecoder(model_paths["hw_onnx"], gpu_id=device_id)
            logging.info(f"Loaded handwriting recognition model on GPU {device_id}")
            
        except Exception as e:
            logging.error(f"Failed to initialize models on GPU {device_id}: {str(e)}")
            self.cleanup()
            raise
        finally:
            if hasattr(self, 'ctx'):
                try:
                    self.ctx.pop()
                except Exception:
                    pass

    def cleanup(self):
        """清理所有资源"""
        try:
            # 清理检测器
            if hasattr(self, 'detector'):
                try:
                    self.detector.cleanup()
                except Exception as e:
                    logging.error(f"Error cleaning up detector: {str(e)}")
                del self.detector
            
            # 清理识别器
            if hasattr(self, 'seal_recognizer'):
                try:
                    self.seal_recognizer.__del__()
                except Exception as e:
                    logging.error(f"Error cleaning up seal recognizer: {str(e)}")
                del self.seal_recognizer
                
            if hasattr(self, 'hw_recognizer'):
                try:
                    self.hw_recognizer.__del__()
                except Exception as e:
                    logging.error(f"Error cleaning up hw recognizer: {str(e)}")
                del self.hw_recognizer
            
            # 清理CUDA上下文
            if hasattr(self, 'ctx'):
                try:
                    self.ctx.pop()
                except Exception:
                    pass
                try:
                    self.ctx.detach()
                except Exception as e:
                    logging.error(f"Error detaching context: {str(e)}")
            
        except Exception as e:
            logging.error(f"Error in ModelInstance.cleanup for GPU {self.device_id}: {str(e)}")

    def __del__(self):
        """析构函数"""
        try:
            self.cleanup()
        except Exception as e:
            logging.error(f"Error in ModelInstance.__del__ for GPU {self.device_id}: {str(e)}")

class HSRecognizer:
    def __init__(self, models_dir="models", use_onnx=False):
        """
        初始化识别器
        models_dir: 模型目录
        use_onnx: 是否使用ONNX模型进行识别，默认False使用rapid_ocr
        """
        self.models_dir = os.path.abspath(models_dir)
        self.gpu_count = cuda.Device.count()
        self.current_gpu = 0
        self.lock = threading.Lock()
        self.use_onnx = use_onnx
        
        logging.info(f"Initializing HSRecognizer with {self.gpu_count} GPUs (use_onnx={use_onnx})")
        
        # 检查模型文件
        model_paths = {
            "seal_model": os.path.join(models_dir, "seal.trt"),
        }
        
        # 如果使用ONNX模型，添加额外的模型路径
        if use_onnx:
            model_paths.update({
                "seal_onnx": os.path.join(models_dir, "seal-onnx"),
                "hw_onnx": os.path.join(models_dir, "hw-onnx")
            })
        
        # 验证模型文件
        for key, path in model_paths.items():
            if not os.path.exists(path):
                raise FileNotFoundError(f"Model not found: {path}")
        
        # 为每个GPU创建实例
        self.gpu_instances = []
        for gpu_id in range(self.gpu_count):
            try:
                instance = self._create_gpu_instance(gpu_id, model_paths)
                if instance:
                    self.gpu_instances.append(instance)
                    logging.info(f"Successfully initialized model set on GPU {gpu_id}")
            except Exception as e:
                logging.error(f"Failed to initialize GPU {gpu_id}: {str(e)}")
                continue
        
        if not self.gpu_instances:
            raise RuntimeError("No GPU instances were successfully initialized")
        
        logging.info(f"Successfully initialized {len(self.gpu_instances)} GPU instances")

    def _create_gpu_instance(self, gpu_id, model_paths):
        """创建单个GPU实例"""
        device = cuda.Device(gpu_id)
        ctx = device.make_context()
        
        try:
            # 基础检测器始终需要
            detector = YoLov5TRT(model_paths["seal_model"], gpu_id)
            logging.info(f"Loaded YoLov5TRT model on GPU {gpu_id}")
            
            instance = {
                'gpu_id': gpu_id,
                'context': ctx,
                'models': {
                    'detector': detector
                }
            }
            
            # 如果使用ONNX模型，加载额外的识别器
            if self.use_onnx:
                try:
                    seal_recognizer = OnnxEncoderDecoder(model_paths["seal_onnx"], gpu_id=gpu_id)
                    hw_recognizer = OnnxEncoderDecoder(model_paths["hw_onnx"], gpu_id=gpu_id)
                    
                    instance['models'].update({
                        'seal_recognizer': seal_recognizer,
                        'hw_recognizer': hw_recognizer
                    })
                    
                    logging.info(f"Loaded ONNX recognizers on GPU {gpu_id}")
                except Exception as e:
                    logging.error(f"Failed to load ONNX models on GPU {gpu_id}: {str(e)}")
                    # 即使ONNX加载失败，仍然返��带有检测器的实例
            
            return instance
            
        except Exception as e:
            logging.error(f"Failed to create model instance on GPU {gpu_id}: {str(e)}")
            if ctx:
                try:
                    ctx.pop()
                except:
                    pass
            return None
        finally:
            if ctx:
                try:
                    ctx.pop()
                except:
                    pass

    def _get_next_gpu_id(self):
        """简单轮询获取下一个GPU ID"""
        with self.lock:
            current = self.current_gpu
            self.current_gpu = (self.current_gpu + 1) % len(self.gpu_instances)
            logging.info(f"[HSRecognizer] Selected GPU {current} -> Next GPU will be {self.current_gpu}")
            return current

    def recognize(self, img, gpu_id=None):
        """执行识别"""
        if gpu_id is None:
            gpu_id = self._get_next_gpu_id()
            
        if not 0 <= gpu_id < len(self.gpu_instances):
            logging.error(f"[HSRecognizer] Invalid GPU ID: {gpu_id}")
            return [], []
            
        instance = self.gpu_instances[gpu_id]
        logging.info(f"[HSRecognizer] Using GPU {gpu_id} for recognition")
        
        try:
            instance['context'].push()
            try:
                # 1. 目标检测
                detections = instance['models']['detector'].infer(img)
                logging.info(f"[HSRecognizer] Detected {len(detections)} objects on GPU {gpu_id}")
                
                seals = []
                handwritings = []
                
                # 2. 对每个检测结果进行识别
                for item in detections:
                    if not isinstance(item, dict) or 'cls' not in item or 'box' not in item:
                        continue
                        
                    cls = item['cls']
                    box = item['box']
                    x1, y1, x2, y2 = map(int, box)
                    
                    # 验证坐标
                    if not (0 <= x1 < x2 <= img.shape[1] and 0 <= y1 < y2 <= img.shape[0]):
                        continue
                        
                    cropped = img[y1:y2, x1:x2]
                    
                    # 根据类别识别
                    if cls == "seal":
                        if self.use_onnx and 'seal_recognizer' in instance['models']:
                            # 使用 ONNX 模型进行识别
                            text, score = instance['models']['seal_recognizer'].run(cropped)
                            if text:
                                seals.append({
                                    'text': text,
                                    'score': float(score),
                                    'box': [x1, y1, x2, y2]
                                })
                        else:
                            # 不使用 ONNX 时，直接返回 "has_seal"
                            seals.append({
                                'text': "has_seal",
                                'score': 1.0,
                                'box': [x1, y1, x2, y2]
                            })
                            
                    elif cls == "handwritten":
                        if self.use_onnx and 'hw_recognizer' in instance['models']:
                            # 使用 ONNX 模型进行识别
                            text, score = instance['models']['hw_recognizer'].run(cropped)
                            if text:
                                handwritings.append({
                                    'text': text,
                                    'score': float(score),
                                    'box': [x1, y1, x2, y2]
                                })
                        else:
                            # 不使用 ONNX 时，直接返回 "has_handwritten"
                            handwritings.append({
                                'text': "has_handwritten",
                                'score': 1.0,
                                'box': [x1, y1, x2, y2]
                            })
                
                logging.info(f"[HSRecognizer] Found {len(seals)} seals and {len(handwritings)} handwritten texts on GPU {gpu_id}")
                return seals, handwritings
                
            finally:
                instance['context'].pop()
                
        except Exception as e:
            logging.error(f"[HSRecognizer] Recognition error on GPU {gpu_id}: {str(e)}")
            return [], []
        

def resize2pad(img, target_size = 960):
    h, w = img.shape[:2]
    ratio = float(w) / float(h)
    
    if w > h:  # If the original image is wide
        resize_w = target_size
        resize_h = int(target_size / ratio)
    else:  # If the original image is tall or square
        resize_h = target_size
        resize_w = int(target_size * ratio)
    
    # Adjust dimensions to be multiples of 32
    resize_h = max(int(round(float(resize_h) / 32) * 32), 32)
    resize_w = max(int(round(float(resize_w) / 32) * 32), 32)
    
    # Resize the image
    temp_img = cv2.resize(img, (resize_w, resize_h))
    
    # Create a black background image of size target_size x target_size
    resize_img = np.zeros((target_size, target_size, img.shape[2]), dtype=img.dtype)
    
    # Place the resized image in the center of the black background
    x_offset = (target_size - resize_w) // 2
    y_offset = (target_size - resize_h) // 2
    resize_img[y_offset:y_offset+resize_h, x_offset:x_offset+resize_w] = temp_img
    
    return resize_img