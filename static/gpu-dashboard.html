<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPU轻量级仪表板 - HNGPT AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .header {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 1.8rem;
            font-weight: 300;
        }

        .header .subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.85rem;
            margin-top: 0.5rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1.5rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .gpu-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.2rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.2s ease;
        }

        .gpu-card:hover {
            transform: translateY(-2px);
        }

        .gpu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }

        .gpu-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #2d3748;
        }

        .gpu-temp {
            font-size: 0.9rem;
            color: #4a5568;
            background: #f7fafc;
            padding: 0.2rem 0.6rem;
            border-radius: 12px;
        }

        .metrics {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.8rem;
            margin-bottom: 1rem;
        }

        .metric {
            text-align: center;
        }

        .metric-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2d3748;
        }

        .metric-label {
            font-size: 0.75rem;
            color: #718096;
            margin-top: 0.2rem;
        }

        .tasks {
            display: flex;
            gap: 0.4rem;
            flex-wrap: wrap;
        }

        .task-badge {
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .badge-chat { background: #bee3f8; color: #2a69ac; }
        .badge-embed { background: #c6f6d5; color: #22543d; }
        .badge-ocr { background: #fbb6ce; color: #97266d; }
        .badge-idle { background: #e2e8f0; color: #4a5568; }

        .summary {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 1.2rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            text-align: center;
        }

        .summary-item .number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #2d3748;
        }

        .summary-item .label {
            font-size: 0.75rem;
            color: #718096;
            margin-top: 0.3rem;
        }

        .status-indicator {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: #48bb78;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
            z-index: 1000;
        }

        .last-update {
            text-align: center;
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.8rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }
            
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ GPU轻量级仪表板</h1>
        <div class="subtitle">高性能监控 - 专为高频刷新优化，数据传输量减少70%</div>
    </div>

    <div class="status-indicator" id="statusIndicator">
        🚀 轻量级API
    </div>

    <div class="container">
        <!-- 任务总览 -->
        <div class="summary">
            <h3 style="margin-bottom: 1rem; color: #4a5568;">📊 任务总览</h3>
            <div class="summary-grid" id="taskSummary">
                <div class="summary-item">
                    <div class="number" id="totalTasks">-</div>
                    <div class="label">总任务</div>
                </div>
                <div class="summary-item">
                    <div class="number" id="activeTasks">-</div>
                    <div class="label">处理中</div>
                </div>
                <div class="summary-item">
                    <div class="number" id="doneTasks">-</div>
                    <div class="label">已完成</div>
                </div>
                <div class="summary-item">
                    <div class="number" id="failedTasks">-</div>
                    <div class="label">失败</div>
                </div>
            </div>
        </div>

        <!-- GPU卡片 -->
        <div class="dashboard-grid" id="gpuGrid">
            <!-- GPU卡片将通过JavaScript动态生成 -->
        </div>

        <div class="last-update">
            最后更新: <span id="lastUpdate">-</span> | 刷新间隔: 2秒
        </div>
    </div>

    <script>
        let refreshInterval;
        let isRefreshing = false;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            startAutoRefresh();
        });

        // 自动刷新 - 更高频率
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 2000); // 每2秒刷新
        }

        // 停止自动刷新
        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // 刷新数据 - 使用轻量级API
        async function refreshData() {
            if (isRefreshing) return;
            
            isRefreshing = true;
            const statusIndicator = document.getElementById('statusIndicator');
            statusIndicator.style.background = '#ed8936'; // 橙色表示加载中

            try {
                // 使用轻量级dashboard API
                const response = await fetch('/monitor/api/dashboard');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const data = await response.json();
                updateDashboard(data);
                
                statusIndicator.style.background = '#48bb78'; // 绿色表示成功
                statusIndicator.textContent = '🚀 轻量级API';
                
            } catch (error) {
                console.error('数据获取失败:', error);
                statusIndicator.style.background = '#f56565'; // 红色表示错误
                statusIndicator.textContent = '❌ 连接失败';
            } finally {
                isRefreshing = false;
                document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
            }
        }

        // 更新仪表板
        function updateDashboard(data) {
            updateTaskSummary(data.tasks || {});
            updateGPUCards(data.gpus || []);
        }

        // 更新任务总览
        function updateTaskSummary(tasks) {
            document.getElementById('totalTasks').textContent = tasks.total || 0;
            document.getElementById('activeTasks').textContent = tasks.active || 0;
            document.getElementById('doneTasks').textContent = tasks.done || 0;
            document.getElementById('failedTasks').textContent = tasks.failed || 0;
        }

        // 更新GPU卡片
        function updateGPUCards(gpus) {
            const gpuGrid = document.getElementById('gpuGrid');
            gpuGrid.innerHTML = '';

            gpus.forEach((gpu) => {
                const card = createGPUCard(gpu);
                gpuGrid.appendChild(card);
            });
        }

        // 创建GPU卡片
        function createGPUCard(gpu) {
            const card = document.createElement('div');
            card.className = 'gpu-card';
            
            const tasks = gpu.tasks || {};
            const totalTasks = (tasks.chat || 0) + (tasks.embed || 0) + (tasks.ocr || 0);

            card.innerHTML = `
                <div class="gpu-header">
                    <div class="gpu-title">🎮 GPU ${gpu.id}</div>
                    <div class="gpu-temp">${gpu.temp || 0}°C</div>
                </div>

                <div class="metrics">
                    <div class="metric">
                        <div class="metric-value">${(gpu.util || 0).toFixed(0)}%</div>
                        <div class="metric-label">GPU使用率</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${(gpu.mem || 0).toFixed(0)}%</div>
                        <div class="metric-label">显存使用率</div>
                    </div>
                </div>

                <div class="tasks">
                    ${tasks.chat ? `<span class="task-badge badge-chat">Chat: ${tasks.chat}</span>` : ''}
                    ${tasks.embed ? `<span class="task-badge badge-embed">Embed: ${tasks.embed}</span>` : ''}
                    ${tasks.ocr ? `<span class="task-badge badge-ocr">OCR: ${tasks.ocr}</span>` : ''}
                    ${totalTasks === 0 ? '<span class="task-badge badge-idle">空闲</span>' : ''}
                </div>
            `;
            
            return card;
        }

        // 页面可见性变化时控制刷新
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
                refreshData();
            }
        });
    </script>
</body>
</html>
