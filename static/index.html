<!DOCTYPE html>
<html>

<head>
  <title>AI助手</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="智能对话，高效协作">
  <meta name="theme-color" content="#60a5fa">
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    connect-src 'self' http://localhost:8888 ws://localhost:8888;
    script-src 'self' 'unsafe-inline' 'unsafe-eval';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob:;
    font-src 'self' data:;
  ">
  <link rel="shortcut icon" type="image/x-icon" href="/static/favicon.ico">
  <link rel="stylesheet" href="/static/prism.min.css" />
  <link href="/static/bootstrap.min.css" rel="stylesheet" crossorigin="anonymous">
  <script src="/static/bootstrap.bundle.min.js" crossorigin="anonymous"></script>
  <script src="/static/marked.min.js" crossorigin="anonymous"></script>
  <script src="/static/purify.min.js" crossorigin="anonymous"></script>
  <script src="/static/jquery-3.7.1.min.js" crossorigin="anonymous"></script>
  <script src="/static/prism.min.js"></script>
  <link rel="stylesheet" href="/static/chat.css">
</head>

<body>
  <!-- 加载指示器 -->
  <div id="loading-overlay" class="loading-overlay" style="display: none;">
    <div class="loading-spinner">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">加载中...</span>
      </div>
      <p class="mt-2">正在初始化...</p>
    </div>
  </div>

  <div class="container">
    <div id="scroll-wrapper">
      <div id="chat-container" class="card">
        <div class="card-header">
          <h1 class="chat-title">
            <span class="title-icon">🤖</span>
            AI助手
            <span class="title-subtitle">智能对话，高效协作</span>
          </h1>
        </div>
        <div class="card-body">
          <div id="chat-history" role="log" aria-live="polite" aria-label="聊天历史"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- 隐藏的配置输入 -->
  <input id="system-prompt" type="hidden" value="你是一个AI助手,Your name is hngpt，一个由hngpt科技开发和维护的AI助手">
  <input id="host-address" type="hidden" value="">

  <!-- 输入区域 -->
  <div class="container p-2 card" id="input-area">
    <div class="input-group">
      <textarea
        class="form-control"
        id="user-input"
        placeholder="请输入您的问题..."
        aria-label="用户输入"
        rows="1"
        maxlength="4000"
        oninput="if(typeof autoGrow === 'function') autoGrow(this)">
      </textarea>
      <button id="send-button" class="btn btn-primary" aria-label="发送消息">
        <span class="send-text">发送</span>
        <span class="send-icon d-none">
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">发送中...</span>
          </div>
        </span>
      </button>
    </div>
    <div class="input-footer">
      <small class="text-muted">按 Enter 发送，Shift+Enter 换行</small>
    </div>
  </div>

  <div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
      <div class="modal-content">
        <div class="modal-header">
          <h5 class="modal-title">Unable to access hngpt server</h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>
        <div class="modal-body">
          <p id="errorText"></p>
        </div>
      </div>
    </div>
  </div>

  <script src="/static/chat.js"></script>
  <script src="/static/cool-effects.js"></script>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      document.getElementById('host-address').value = window.location.origin;
    });
  </script>

</body>

</html> 