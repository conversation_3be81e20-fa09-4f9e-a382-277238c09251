<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码块抖动修复测试</title>
    <link href="/static/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="/static/chat.css">
    <link rel="stylesheet" href="/static/prism.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-output {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            min-height: 200px;
            background: #f9f9f9;
        }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        .controls button {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center">🔧 代码块抖动修复测试</h1>
        
        <div class="controls">
            <button class="btn btn-primary" onclick="testStreamingCode()">测试流式代码渲染</button>
            <button class="btn btn-secondary" onclick="testMixedContent()">测试混合内容</button>
            <button class="btn btn-success" onclick="testLongCode()">测试长代码</button>
            <button class="btn btn-danger" onclick="clearTest()">清空</button>
        </div>
        
        <div class="test-output message-response" id="output">
            <p>点击上方按钮开始测试...</p>
        </div>
        
        <div class="alert alert-info">
            <h5>测试说明：</h5>
            <ul>
                <li><strong>流式代码渲染</strong>：模拟AI逐步生成代码的过程</li>
                <li><strong>混合内容</strong>：测试文本和代码混合的渲染</li>
                <li><strong>长代码</strong>：测试大型代码块的渲染性能</li>
            </ul>
            <p><strong>观察要点</strong>：页面是否有明显的抖动、代码块是否正确展开、高亮是否正常工作</p>
        </div>
    </div>

    <script src="/static/bootstrap.bundle.min.js"></script>
    <script src="/static/marked.min.js"></script>
    <script src="/static/prism.js"></script>
    <script src="/static/chat.js"></script>
    <script>
        const output = document.getElementById('output');
        
        function testStreamingCode() {
            const codeLines = [
                '```javascript',
                '// 流式渲染测试',
                'function streamTest() {',
                '    console.log("开始测试...");',
                '    ',
                '    const data = {',
                '        name: "测试数据",',
                '        value: 123,',
                '        items: [',
                '            "item1",',
                '            "item2",',
                '            "item3"',
                '        ]',
                '    };',
                '    ',
                '    // 处理数据',
                '    data.items.forEach(item => {',
                '        console.log("处理:", item);',
                '    });',
                '    ',
                '    return data;',
                '}',
                '```'
            ];
            
            let currentText = '';
            let index = 0;
            
            function addNextLine() {
                if (index < codeLines.length) {
                    currentText += codeLines[index] + '\n';
                    
                    // 使用优化后的updateResponse函数
                    updateResponse(output, currentText, 'test-message');
                    
                    index++;
                    setTimeout(addNextLine, 150); // 模拟网络延迟
                }
            }
            
            output.innerHTML = '';
            addNextLine();
        }
        
        function testMixedContent() {
            const content = `# 混合内容测试

这是一段普通文本，后面会有代码块。

\`\`\`python
def hello_world():
    print("Hello, World!")
    return "success"
\`\`\`

代码块后面的文本内容。

- 列表项 1
- 列表项 2 包含 \`内联代码\`
- 列表项 3

另一个代码块：

\`\`\`css
.test-class {
    color: #333;
    font-size: 16px;
    margin: 10px 0;
}
\`\`\`

结束文本。`;
            
            updateResponse(output, content, 'test-message-2');
        }
        
        function testLongCode() {
            let longCode = '# 长代码测试\n\n```javascript\n';
            
            // 生成长代码
            for (let i = 0; i < 50; i++) {
                longCode += `// 这是第 ${i + 1} 行注释\n`;
                longCode += `function func${i}() {\n`;
                longCode += `    const data${i} = {\n`;
                longCode += `        id: ${i},\n`;
                longCode += `        name: "item${i}",\n`;
                longCode += `        value: ${i * 10}\n`;
                longCode += `    };\n`;
                longCode += `    \n`;
                longCode += `    console.log("处理数据:", data${i});\n`;
                longCode += `    return data${i};\n`;
                longCode += `}\n\n`;
            }
            
            longCode += '```\n\n这是长代码后的文本。';
            
            updateResponse(output, longCode, 'test-message-3');
        }
        
        function clearTest() {
            output.innerHTML = '<p>测试已清空，点击上方按钮开始新的测试...</p>';
        }
        
        // 初始化marked和其他必要的设置
        if (typeof setupMarked === 'function') {
            setupMarked();
        }
    </script>
</body>
</html>
