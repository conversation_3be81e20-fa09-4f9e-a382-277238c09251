from fastapi import FastAPI, Request, HTTPException, Depends, BackgroundTasks, Query, UploadFile, Body, Form, WebSocket, WebSocketDisconnect  
from fastapi.responses import JSONResponse, StreamingResponse, HTMLResponse, FileResponse
from fastapi.staticfiles import StaticFiles
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field, validator
from typing import Optional, Union, Dict, List, Tuple
from urllib.parse import urlparse, parse_qs
import httpx
from queue import Queue
import configparser
import logging
from logging.handlers import RotatingFileHandler
from starlette.middleware.base import BaseHTTPMiddleware
import time
import asyncio
import sys
from itertools import cycle
from threading import Lock
import json
from contextlib import asynccontextmanager
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import Response
import numpy as np
import base64
import cv2
import uuid
from fastapi import UploadFile, Body
from fastapi.background import BackgroundTasks
from collections import defaultdict
import re
import rec
from asyncio import Event
from fastapi.openapi.docs import get_swagger_ui_html
from dataclasses import dataclass, field, asdict, field
import os
from pydantic import validator
from fastapi.openapi.docs import get_swagger_ui_html, get_swagger_ui_oauth2_redirect_html  
from fastapi.responses import StreamingResponse
from fastapi.responses import RedirectResponse
import signal
from urllib.parse import quote
#from seal import HSRecognizer
from timefinder import TimeFinder
from multiprocessing import Queue, Process, Manager
import torch
import warnings
import onnxruntime
from layout_content_extractor import extract_page_content_by_layout

# 添加更详细的警告过滤
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=UserWarning, module="numpy.core.getlimits")
warnings.filterwarnings("ignore", message="The value of the smallest subnormal.*")

# 设置 numpy 错误处理模式
np.seterr(all="ignore")

# 如果需要，也可以设置特定的 numpy 错误处理
np.seterr(divide='ignore', invalid='ignore', over='ignore', under='ignore')

# GPU资源分配策略：为不同服务预留专用GPU资源
# GPU 0,1: 主要用于LLM (Ollama)
# GPU 2,3: 主要用于OCR/Layout，但可以动态共享
# 不设置全局CUDA_VISIBLE_DEVICES，让每个服务动态管理
# os.environ['CUDA_VISIBLE_DEVICES'] = '0,1,2,3'  # 注释掉全局设置
os.environ['ONNXRUNTIME_CUDA_PROVIDER_OPTIONS'] = '{"cudnn_conv_algo_search": "DEFAULT"}'
# 添加 numpy 配置
os.environ['NUMPY_WARNINGS'] = 'ignore'

# 添加警告过滤
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)

# 添加 ONNX Runtime 警告过滤
warnings.filterwarnings("ignore", category=UserWarning, module="onnxruntime")
warnings.filterwarnings("ignore", message=".*VerifyEachNodeIsAssignedToAnEp.*")
warnings.filterwarnings("ignore", message=".*Some nodes were not assigned.*")

# ONNX Runtime 环境配置
onnxruntime.set_default_logger_severity(3)  # 设置日志级别为 WARNING
os.environ['ORT_LOGGING_LEVEL'] = '2'  # 设置为 WARNING
os.environ['ORT_DISABLE_SHAPE_OPS_ON_CPU'] = '0'  # 允许在 CPU 上执行形状操作

# 定义 bearer
bearer = HTTPBearer()

# 定义应用
app = FastAPI(docs_url=None, redoc_url=None)

def setup_logging(log_dir):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, "app.log")
    
    # 创建一个格式化器
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    
    # 获取根日志记录器
    rlog = logging.getLogger()
    
    # 清除所有现有的处理器
    for handler in rlog.handlers[:]:
        rlog.removeHandler(handler)
    
    # 设置日志
    rlog.setLevel(logging.INFO)
    
    # 创建文件处理器
    file_handler = RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,
        backupCount=1,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    rlog.addHandler(file_handler)
    
    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    rlog.addHandler(console_handler)
    
    # 输出一条试日志
    logging.info("Logging system initialized")



# 修改 ServerStatus 类
@dataclass
class ServerStatus:
    url: str
    # 分别为 chat 和 embeddings 创建专用队列
    chat_queue: Queue = None
    embeddings_queue: Queue = None
    # 优化：大幅增加队列大小和并发限制以支持高负载测试
    max_queue_size: int = 50  # 大幅增加队列大小到50
    max_concurrent_chat: int = 8  # 允许8个并发聊天请求
    max_concurrent_embeddings: int = 12  # 允许12个并发嵌入请求
    # 当前正在处理的请求数量
    chat_processing: int = 0
    embeddings_processing: int = 0
    # 错误计数
    chat_errors: int = 0
    embeddings_errors: int = 0
    # 健康状态相关属性已删除 - 不再进行健康检查
    # 性能指标
    avg_response_time: float = 0.0
    total_requests: int = 0
    
    def __post_init__(self):
        """初始化后创建队列"""
        self.chat_queue = Queue()
        self.embeddings_queue = Queue()
    
    def record_success(self, endpoint: str, response_time: float = 0.0):
        """记录成功请求"""
        # 重置错误计数
        if endpoint == "chat":
            self.chat_errors = 0
        else:
            self.embeddings_errors = 0

        # 更新性能指标
        self.total_requests += 1
        if response_time > 0:
            # 使用指数移动平均计算响应时间
            alpha = 0.1  # 平滑因子
            if self.avg_response_time == 0:
                self.avg_response_time = response_time
            else:
                self.avg_response_time = alpha * response_time + (1 - alpha) * self.avg_response_time

    def record_error(self, endpoint: str):
        """记录错误请求"""
        if endpoint == "chat":
            self.chat_errors += 1
            # 记录错误但不进行健康状态判断
            if self.chat_errors >= 3:
                logging.warning(f"服务器 {self.url} 的 chat 端点连续{self.chat_errors}次错误")
        else:
            self.embeddings_errors += 1
            if self.embeddings_errors >= 3:
                logging.warning(f"服务器 {self.url} 的 embeddings 端点连续{self.embeddings_errors}次错误")

    def get_max_concurrent(self, endpoint: str) -> int:
        """动态获取最大并发数"""
        if endpoint == "chat":
            base_concurrent = self.max_concurrent_chat
        else:
            base_concurrent = self.max_concurrent_embeddings

        # 健康检查已删除，直接返回基础并发数
        return base_concurrent


    
    # check_health方法已删除 - 不再进行健康检查
    
    # reset_processing_counters方法已删除 - 不再需要重置处理计数器

# 在文件顶部定义全局变量
SERVERS_CONFIG: Dict[str, ServerStatus] = {}
authorized_users: Dict[str, str] = {}
server_cycle = None
server_lock = Lock()
recognizer = None
timefinder = TimeFinder()




# 统一任务结果类
@dataclass
class TaskResult:
    """统一任务结果"""
    task_id: str
    task_type: str  # 'llm', 'embedding', 'ocr'
    status: str = "processing"  # 'processing', 'completed', 'failed', 'timeout'
    result: Optional[dict] = None
    error: Optional[str] = None
    progress: int = 0
    created_at: float = field(default_factory=time.time)
    completed_at: Optional[float] = None
    gpu_id: Optional[int] = None

# 统一任务管理器 - 整合GPU资源管理和性能监控
class UnifiedTaskManager:
    """统一任务管理器，管理所有类型的任务、GPU资源和性能监控"""

    def __init__(self):
        # 任务管理
        self.tasks: Dict[str, asyncio.Task] = {}  # 运行中的任务
        self.task_results: Dict[str, TaskResult] = {}  # 任务结果
        self.task_events: Dict[str, asyncio.Event] = {}  # 任务完成事件
        self.task_counter = 0
        # 优化：只在真正需要原子操作的地方使用锁
        self.lock = asyncio.Lock()  # 主锁，用于任务创建和管理
        self._result_lock = asyncio.Lock()  # 只保护结果写入

        # 累积性段错误防护
        self.layout_call_count = 0  # 版面分析调用计数
        self.layout_cleanup_threshold = 3  # 每3次调用强制清理

        # GPU资源管理
        self.gpu_count = torch.cuda.device_count() if torch.cuda.is_available() else 0
        self.gpu_status = {}
        for i in range(self.gpu_count):
            self.gpu_status[i] = {
                'llm_chat_tasks': 0,     # LLM Chat任务数
                'llm_embedding_tasks': 0, # LLM Embedding任务数
                'ocr_tasks': 0,          # OCR任务数
                'memory_used': 0,        # 内存使用量
                'last_cleanup': time.time(),
                'lock': Lock(),
            }

        # 性能监控（仅保留前端需要的指标）
        self.performance_metrics = {
            "task_throughput": {},  # 任务吞吐量
            "response_times": {},   # 响应时间
            "error_rates": {}       # 错误率
        }
        self.start_time = time.time()

        logging.info(f"🔧 统一任务管理器初始化:")
        logging.info(f"  总GPU数量: {self.gpu_count}")
        logging.info(f"  集成功能: 任务管理 + GPU资源管理 + 性能监控")

    def generate_task_id(self, task_type: str) -> str:
        """生成唯一的任务ID"""
        return f"{task_type}_{uuid.uuid4().hex[:8]}"

    def acquire_gpu(self, gpu_id: int, task_type: str = "ocr") -> bool:
        """获取GPU资源 - 支持chat、embedding、ocr三类任务"""
        if gpu_id >= self.gpu_count:
            return False

        with self.gpu_status[gpu_id]['lock']:
            status = self.gpu_status[gpu_id]

            # 检查GPU内存是否足够
            try:
                gpu_info = get_gpu_info(gpu_id)
                if gpu_info:  # 只有在成功获取GPU信息时才检查内存
                    memory_usage = gpu_info.get('memory_utilization_percent', 0) / 100.0

                    # 动态内存阈值：LLM任务要求更高
                    if task_type in ["chat", "embedding"]:
                        memory_threshold = 0.75  # LLM任务75%阈值
                    elif task_type == "ocr":
                        memory_threshold = 0.90  # OCR任务90%阈值（显存占用低）
                    else:
                        logging.warning(f"不支持的任务类型: {task_type}")
                        return False

                    if memory_usage > memory_threshold:
                        logging.warning(f"GPU {gpu_id} 内存使用过高 ({memory_usage:.1%})，拒绝{task_type}任务")
                        return False
                # 如果无法获取GPU信息，继续执行（假设内存足够）
            except Exception as e:
                logging.debug(f"检查GPU {gpu_id} 内存时出错: {e}")
                pass

            # 更新任务计数
            if task_type == "chat":
                status['llm_chat_tasks'] += 1
            elif task_type == "embedding":
                status['llm_embedding_tasks'] += 1
            elif task_type == "ocr":
                status['ocr_tasks'] += 1

            logging.info(f"🔒 GPU {gpu_id} 资源获取成功 [{task_type}]: "
                        f"LLM={status['llm_chat_tasks']},EMBEDDING={status['llm_embedding_tasks']}, OCR={status['ocr_tasks']}")
            return True

    def release_gpu(self, gpu_id: int, task_type: str = "ocr"):
        """释放GPU资源 - 支持chat、embedding、ocr三类任务"""
        if gpu_id >= self.gpu_count:
            return

        with self.gpu_status[gpu_id]['lock']:
            status = self.gpu_status[gpu_id]

            # 更新任务计数
            if task_type == "chat":
                status['llm_chat_tasks'] = max(0, status['llm_chat_tasks'] - 1)
            elif task_type == "embedding":
                status['llm_embedding_tasks'] = max(0, status['llm_embedding_tasks'] - 1)
            elif task_type == "ocr":
                status['ocr_tasks'] = max(0, status['ocr_tasks'] - 1)
            else:
                logging.warning(f"不支持的任务类型: {task_type}")
                return

            logging.info(f"🔓 GPU {gpu_id} 资源释放 [{task_type}]: "
                        f"LLM={status['llm_chat_tasks']},EMBEDDING={status['llm_embedding_tasks']}, OCR={status['ocr_tasks']}")

    def record_task_completion(self, task_type: str, response_time: float, success: bool):
        """记录任务完成指标 - 仅保留前端需要的指标"""
        if task_type not in self.performance_metrics["task_throughput"]:
            self.performance_metrics["task_throughput"][task_type] = {"count": 0, "total_time": 0}
            self.performance_metrics["response_times"][task_type] = []
            self.performance_metrics["error_rates"][task_type] = {"success": 0, "error": 0}

        self.performance_metrics["task_throughput"][task_type]["count"] += 1
        self.performance_metrics["task_throughput"][task_type]["total_time"] += response_time
        self.performance_metrics["response_times"][task_type].append(response_time)

        if success:
            self.performance_metrics["error_rates"][task_type]["success"] += 1
        else:
            self.performance_metrics["error_rates"][task_type]["error"] += 1

        # 保持响应时间历史记录在合理范围内
        if len(self.performance_metrics["response_times"][task_type]) > 100:
            self.performance_metrics["response_times"][task_type] = self.performance_metrics["response_times"][task_type][-50:]

    async def create_task(self, task_type: str, task_data: dict, gpu_id: Optional[int] = None) -> str:
        """创建新任务并返回task_id"""
        async with self.lock:
            task_id = self.generate_task_id(task_type)

            # 创建任务结果对象和完成事件
            task_result = TaskResult(
                task_id=task_id,
                task_type=task_type,
                status="processing",
                progress=0,
                gpu_id=gpu_id
            )
            self.task_results[task_id] = task_result
            self.task_events[task_id] = asyncio.Event()  # 创建任务完成事件

            # 根据任务类型创建对应的处理任务
            if task_type == "ocr":
                task = asyncio.create_task(self._process_ocr_task(task_id, task_data))
            elif task_type == "llm":
                task = asyncio.create_task(self._process_llm_task(task_id, task_data))
            elif task_type == "embedding":
                task = asyncio.create_task(self._process_embedding_task(task_id, task_data))
            else:
                raise ValueError(f"不支持的任务类型: {task_type}")

            self.tasks[task_id] = task
            logging.info(f"📝 创建任务 {task_id} [{task_type}]")
            return task_id

    async def get_task_result(self, task_id: str, timeout: float = 30.0) -> Optional[TaskResult]:
        """获取任务结果，使用事件驱动方式，避免轮询"""
        # 检查任务是否存在
        if task_id not in self.task_results:
            return None

        # 检查任务是否已经完成
        result = self.task_results[task_id]
        if result.status in ["completed", "failed", "timeout"]:
            return result

        # 等待任务完成事件
        if task_id in self.task_events:
            try:
                # 使用 asyncio.wait_for 实现超时等待
                await asyncio.wait_for(self.task_events[task_id].wait(), timeout=timeout)

                # 事件触发后返回结果
                if task_id in self.task_results:
                    return self.task_results[task_id]

            except asyncio.TimeoutError:
                # 超时处理
                if task_id in self.task_results:
                    self.task_results[task_id].status = "timeout"
                    self.task_results[task_id].error = f"任务执行超时 ({timeout}s)"
                    self.task_results[task_id].progress = 100
                    # 触发事件，通知其他等待者
                    if task_id in self.task_events:
                        self.task_events[task_id].set()
                    return self.task_results[task_id]

        return None

    async def _cleanup_task_event(self, task_id: str):
        """延迟清理任务事件，避免内存泄漏"""
        # 等待一小段时间，确保所有等待者都能收到事件
        await asyncio.sleep(1.0)
        if task_id in self.task_events:
            self.task_events.pop(task_id, None)

    async def update_task_status(self, task_id: str, status: str, result: dict = None, error: str = None, progress: int = None):
        """更新任务状态 - 优化版本，减少锁使用"""
        if task_id in self.task_results:
            # 大部分操作不需要锁，只有在完成状态时才需要原子操作
            task_result = self.task_results[task_id]
            task_result.status = status
            if result is not None:
                task_result.result = result
            if error is not None:
                task_result.error = error
            if progress is not None:
                task_result.progress = progress

            # 只在任务完成时使用锁进行清理
            if status in ["completed", "failed", "timeout"]:
                async with self._result_lock:
                    task_result.completed_at = time.time()
                    # 清理运行中的任务
                    if task_id in self.tasks:
                        self.tasks.pop(task_id, None)
                    # 触发任务完成事件
                    if task_id in self.task_events:
                        self.task_events[task_id].set()  # 通知等待的协程
                        # 延迟清理事件，给其他等待者一些时间
                        asyncio.create_task(self._cleanup_task_event(task_id))

    def get_task_stats(self) -> dict:
        """获取任务统计信息"""
        stats = {
            "total_tasks": len(self.task_results),
            "processing_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "task_types": {"llm": 0, "embedding": 0, "ocr": 0}
        }

        for task_result in self.task_results.values():
            if task_result.status == "processing":
                stats["processing_tasks"] += 1
                stats["task_types"][task_result.task_type] += 1
            elif task_result.status == "completed":
                stats["completed_tasks"] += 1
            elif task_result.status == "failed":
                stats["failed_tasks"] += 1

        return stats

    def get_task_status(self, task_id: str) -> Optional[dict]:
        """获取单个任务的状态信息"""
        if task_id not in self.task_results:
            return None

        task_result = self.task_results[task_id]
        return {
            "task_id": task_result.task_id,
            "task_type": task_result.task_type,
            "status": task_result.status,
            "progress": task_result.progress,
            "created_at": task_result.created_at,
            "completed_at": task_result.completed_at,
            "gpu_id": task_result.gpu_id,
            "result": task_result.result,
            "error": task_result.error
        }

    def get_performance_summary(self) -> dict:
        """获取性能摘要 - 仅保留前端需要的指标"""
        uptime = time.time() - self.start_time

        summary = {
            "uptime_seconds": uptime,
            "task_metrics": {}
        }

        # 任务指标
        for task_type in self.performance_metrics["task_throughput"]:
            throughput_data = self.performance_metrics["task_throughput"][task_type]
            response_times = self.performance_metrics["response_times"][task_type]
            error_data = self.performance_metrics["error_rates"][task_type]

            avg_response_time = (throughput_data["total_time"] / throughput_data["count"]
                               if throughput_data["count"] > 0 else 0)

            total_requests = error_data["success"] + error_data["error"]
            error_rate = (error_data["error"] / total_requests * 100
                         if total_requests > 0 else 0)

            summary["task_metrics"][task_type] = {
                "total_tasks": throughput_data["count"],
                "avg_response_time": avg_response_time,
                "error_rate_percent": error_rate,
                "throughput_per_minute": throughput_data["count"] / (uptime / 60) if uptime > 0 else 0
            }

        return summary

    async def _process_ocr_task(self, task_id: str, task_data: dict):
        """处理OCR任务 - 优化版本，集成性能监控，支持base64接口的额外参数"""
        start_time = time.time()
        success = False

        try:
            image = task_data['image']
            timeout = task_data.get('timeout', 30.0)
            enable_seal_hw = task_data.get('enable_seal_hw', False)

            # 支持base64接口的额外参数
            file_name = task_data.get('file_name', '')
            page_no = task_data.get('page_no', 1)

            await self.update_task_status(task_id, "processing", progress=10)

            # 记录任务详细信息
            logging.info(f"[OCR] 任务详情 - 文件名: {file_name}, 页码: {page_no}, 印章手写: {enable_seal_hw}")

            # 使用GPU资源管理器选择最佳GPU
            best_gpu_id = select_best_gpu_for_task('ocr')
            if best_gpu_id is None:
                raise Exception("没有可用的GPU用于OCR任务")

            # 显示简化的GPU分配日志
            logging.info(f"🎯 [OCR] 任务分配 → GPU{best_gpu_id}")

            # 尝试获取GPU资源
            if not self.acquire_gpu(best_gpu_id, "ocr"):
                # 如果获取失败，尝试其他GPU
                for fallback_gpu in range(self.gpu_count):
                    if self.acquire_gpu(fallback_gpu, "ocr"):
                        best_gpu_id = fallback_gpu
                        break
                else:
                    # 所有GPU都不可用
                    await self.update_task_status(task_id, "failed", error="所有GPU资源都不可用", progress=100)
                    return

            gpu_id = best_gpu_id

            # GPU资源管理已由统一任务管理器处理
            try:
                gpu_info = get_gpu_info(gpu_id)
                if gpu_info:
                    allocated = gpu_info.get('memory_used_mb', 0)
                    reserved = gpu_info.get('memory_total_mb', 11264)
                    logging.info(f"GPU {gpu_id} initial memory - Used: {allocated:.2f}MB, Total: {reserved:.2f}MB")
                else:
                    logging.info(f"GPU {gpu_id} 内存信息获取失败，继续处理")

                # 使用版面分析和OCR进行综合文档处理
                # 转换为RGB格式用于模型推理
                img_array = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

                # 验证图像尺寸和内存安全
                height, width = img_array.shape[:2]
                image_size_mb = (height * width * 3) / (1024 * 1024)  # 估算内存占用
                logging.info(f"图像尺寸: {width}x{height}, 估算内存: {image_size_mb:.1f}MB")

                # 如果图像过大，可能导致内存问题
                if image_size_mb > 50:  # 50MB阈值
                    logging.warning(f"图像过大 ({image_size_mb:.1f}MB)，可能影响处理稳定性")

                await self.update_task_status(task_id, "processing", progress=20)

                # 步骤1: 版面分析 (使用OCR资源)
                try:
                    logging.info("执行版面分析...")

                    # 检查GPU内存状态
                    gpu_info = get_gpu_info(gpu_id)
                    if gpu_info:
                        memory_util = gpu_info.get('memory_utilization_percent', 0)
                        logging.info(f"GPU {gpu_id} 内存使用率: {memory_util:.1f}%")

                        # 如果内存使用率过高，跳过版面分析
                        if memory_util > 85:
                            logging.warning(f"GPU {gpu_id} 内存使用率过高 ({memory_util:.1f}%)，跳过版面分析")
                            layout_results = []
                        else:
                            # 使用安全的版面分析包装函数
                            layout_results = self._safe_layout_analysis(img_array, gpu_id)
                    else:
                        # 无法获取GPU信息，使用安全的版面分析
                        logging.warning("无法获取GPU信息，使用安全的版面分析")
                        layout_results = self._safe_layout_analysis(img_array, gpu_id)

                    # 详细记录版面分析结果
                    if layout_results:
                        logging.info(f"✅ 版面分析成功 - 检测到 {len(layout_results)} 个区域")
                        # 记录前几个区域的详细信息
                        for i, region in enumerate(layout_results[:3]):
                            if hasattr(region, 'get'):
                                label = region.get('label', 'unknown')
                                score = region.get('score', 0)
                                logging.info(f"  区域{i+1}: {label} (置信度: {score:.2f})")
                    else:
                        logging.warning("⚠️ 版面分析结果为空，将跳过版面分析步骤")

                except Exception as e:
                    logging.error(f"版面分析失败: {str(e)}，跳过版面分析")
                    layout_results = []

                await self.update_task_status(task_id, "processing", progress=40)

                # 步骤2: 整页OCR识别 (使用已获取的OCR资源)
                logging.info("执行整页OCR识别...")
                ocr_results = rec.rapid_ocr_with_params(img_array, gpu_id=gpu_id, version='v5')
                logging.info(f"识别到 {len(ocr_results) if ocr_results else 0} 个文本区域")
                await self.update_task_status(task_id, "processing", progress=60)

                # 步骤3: 提取页面内容
                logging.info("提取页面内容...")
                result = extract_page_content_by_layout(layout_results, ocr_results, image.shape)
                await self.update_task_status(task_id, "processing", progress=80)

                # 添加额外的元数据到结果中
                if file_name or page_no != 1:
                    result['metadata'] = {
                        'file_name': file_name,
                        'page_no': page_no,
                        'enable_seal_hw': enable_seal_hw,
                        'task_id': task_id
                    }

                logging.info(f"页面内容长度: {len(result['pageContent'])} 字符")

                await self.update_task_status(task_id, "completed", result=result, progress=100)
                success = True
                logging.info(f"OCR completed for task {task_id}")

            finally:
                # 释放GPU资源
                self.release_gpu(gpu_id, "ocr")

                # GPU队列已移除 - 资源管理由GPUResourceManager统一处理
                logging.info(f"✅ 任务 {task_id} GPU {gpu_id} 资源释放完成")

                # 记录最终内存状态
                try:
                    gpu_info = get_gpu_info(gpu_id)
                    if gpu_info:
                        allocated = gpu_info.get('memory_used_mb', 0)
                        reserved = gpu_info.get('memory_total_mb', 11264)
                        logging.info(f"📊 GPU {gpu_id} 最终内存状态 - 已使用: {allocated:.1f}MB, 总计: {reserved:.1f}MB")
                    else:
                        logging.debug(f"无法获取GPU {gpu_id} 内存信息")
                except Exception as e:
                    logging.debug(f"获取GPU {gpu_id} 内存信息失败: {str(e)}")

        except Exception as e:
            error_msg = str(e)
            logging.error(f"❌ OCR任务 {task_id} 失败: {error_msg}")

            # 根据错误类型提供更详细的错误信息
            if "CUDA" in error_msg or "GPU" in error_msg:
                error_msg = f"GPU资源错误: {error_msg}"
            elif "memory" in error_msg.lower() or "out of memory" in error_msg.lower():
                error_msg = f"内存不足: {error_msg}"
            elif "timeout" in error_msg.lower():
                error_msg = f"处理超时: {error_msg}"

            await self.update_task_status(task_id, "failed", error=error_msg, progress=100)

        finally:
            # 记录性能指标
            response_time = time.time() - start_time
            self.record_task_completion("ocr", response_time, success)

    def _safe_layout_analysis(self, img_array, gpu_id: int):
        """安全的版面分析，带有累积性段错误防护"""
        try:
            import gc
            import psutil
            import os
            import numpy as np

            # 累积性段错误防护 - 调用计数
            self.layout_call_count += 1
            logging.info(f"版面分析调用计数: {self.layout_call_count}")

            # 每3次调用进行轻量级资源清理（保护预初始化实例）
            if self.layout_call_count >= self.layout_cleanup_threshold:
                logging.info(f"达到清理阈值({self.layout_cleanup_threshold})，执行轻量级资源清理")
                try:
                    # 轻量级GPU内存清理（不影响预初始化的Layout实例）
                    if torch.cuda.is_available():
                        # 只清理GPU缓存，不清理实例
                        torch.cuda.empty_cache()

                    # 轻量级垃圾回收
                    import gc
                    gc.collect()

                    # 重置计数器
                    self.layout_call_count = 0
                    logging.info("✅ 轻量级资源清理完成（保护预初始化实例）")

                except Exception as cleanup_error:
                    logging.error(f"轻量级清理失败: {cleanup_error}")
                    # 清理失败时重置计数器，避免无限重试
                    self.layout_call_count = 0

            # 检查系统内存
            memory = psutil.virtual_memory()
            if memory.percent > 90:
                logging.warning(f"系统内存使用率过高 ({memory.percent:.1f}%)，跳过版面分析")
                return []

            # 检查进程内存
            process = psutil.Process(os.getpid())
            process_memory_mb = process.memory_info().rss / 1024 / 1024
            if process_memory_mb > 8192:  # 8GB阈值
                logging.warning(f"进程内存使用过高 ({process_memory_mb:.1f}MB)，跳过版面分析")
                return []

            # 强制垃圾回收
            gc.collect()

            # 严格的输入验证
            import numpy as np

            if img_array is None:
                logging.error("输入图像数组为None")
                return []

            if not isinstance(img_array, np.ndarray):
                logging.error(f"输入不是numpy数组，类型: {type(img_array)}")
                return []

            # 检查数组形状
            if len(img_array.shape) != 3:
                logging.error(f"图像数组形状错误: {img_array.shape}，期望3维")
                return []

            height, width, channels = img_array.shape

            # 检查图像尺寸合理性
            if height <= 0 or width <= 0 or channels != 3:
                logging.error(f"图像尺寸无效: {height}x{width}x{channels}")
                return []

            # 检查图像尺寸是否过大（防止内存问题）
            max_pixels = 4096 * 4096  # 16M像素限制
            if height * width > max_pixels:
                logging.error(f"图像过大: {height}x{width} > {max_pixels}像素")
                return []

            # 检查数据类型
            if img_array.dtype != np.uint8:
                logging.warning(f"图像数据类型: {img_array.dtype}，期望uint8")
                try:
                    img_array = img_array.astype(np.uint8)
                except Exception as e:
                    logging.error(f"数据类型转换失败: {e}")
                    return []

            # GPU资源检查
            gpu_info = get_gpu_info(gpu_id)
            if gpu_info:
                gpu_memory_percent = gpu_info.get('memory_utilization_percent', 0)
                if gpu_memory_percent > 85:
                    logging.warning(f"GPU {gpu_id} 内存使用率过高 ({gpu_memory_percent:.1f}%)，跳过版面分析")
                    return []

            # 强制内存清理
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize(gpu_id)

            # 执行版面分析（使用预初始化的实例）
            max_retries = 2
            for attempt in range(max_retries + 1):
                try:
                    logging.info(f"开始版面分析 - GPU {gpu_id}, 图像: {height}x{width}, 尝试: {attempt + 1}")

                    # 确保数组是连续的
                    if not img_array.flags['C_CONTIGUOUS']:
                        img_array = np.ascontiguousarray(img_array)

                    # 使用预初始化的Layout实例（不会创建新实例）
                    layout_results = rec.rapid_layout_with_params(img_array, gpu_id=gpu_id)

                    # 验证结果
                    if layout_results is None:
                        logging.warning(f"版面分析返回None - 尝试 {attempt + 1}")
                        if attempt < max_retries:
                            # 短暂等待后重试
                            time.sleep(0.5)
                            continue
                        else:
                            logging.error("版面分析多次重试后仍返回None")
                            return []

                    # 检查结果是否为空列表
                    if isinstance(layout_results, list) and len(layout_results) == 0:
                        logging.warning(f"版面分析返回空列表 - 尝试 {attempt + 1}")
                        if attempt < max_retries:
                            time.sleep(0.5)
                            continue

                    # 成功获得结果
                    logging.info(f"✅ 版面分析成功 - 检测到 {len(layout_results)} 个区域 (使用预初始化实例)")

                    # 轻量级GPU缓存清理（不影响实例）
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()

                    return layout_results

                except Exception as e:
                    logging.error(f"版面分析尝试 {attempt + 1} 失败: {str(e)}")
                    if attempt < max_retries:
                        logging.info(f"等待后重试...")
                        time.sleep(1.0)
                        continue
                    else:
                        logging.error("版面分析所有重试都失败")
                        return []

            return []

        except Exception as e:
            logging.error(f"版面分析异常: {str(e)}")
            logging.error(f"异常类型: {type(e).__name__}")
            return []

    async def _process_llm_task(self, task_id: str, task_data: dict):
        """处理LLM Chat任务"""
        start_time = time.time()
        success = False

        try:
            request_data = task_data['request']
            server_name = task_data['server_name']
            server_status = task_data['server_status']

            # 提取GPU ID并显示清晰的分配日志
            gpu_id = int(server_name.replace('server', ''))
            await self.update_task_status(task_id, "processing", progress=20)

            # 获取服务器索引
            try:
                server_index = int(server_name.replace('server', ''))
            except ValueError:
                # 如果server_name不是标准格式，从URL中解析
                server_index = int(server_status.url.split(':')[-1]) - 11434

            # 更新GPU任务计数
            self.gpu_status[server_index]['llm_chat_tasks'] += 1

            # 更新任务的GPU ID
            self.task_results[task_id].gpu_id = server_index

            chat_tasks = self.gpu_status[server_index]['llm_chat_tasks']
            embed_tasks = self.gpu_status[server_index]['llm_embedding_tasks']
            logging.info(f"💬 Started chat task on GPU {server_index}, "
                       f"chat tasks: {chat_tasks}, "
                       f"embedding tasks: {embed_tasks}")

            await self.update_task_status(task_id, "processing", progress=40)

            try:
                # 使用标准OpenAI格式的消息
                messages = [msg.model_dump() for msg in request_data.messages]

                # 确保至少有一条消息
                if not messages:
                    raise ValueError("No valid messages provided")

                # 构造统一的请求格式
                ollama_request = {
                    "model": request_data.model,
                    "messages": messages,
                    "stream": False,  # 统一任务管理器中使用非流式
                    "temperature": request_data.temperature,
                    "max_tokens": request_data.max_tokens
                }

                # 发送请求到Ollama服务器，设置合理的超时时间
                timeout_config = httpx.Timeout(
                    connect=10.0,  # 连接超时
                    read=300.0,    # 读取超时（5分钟）
                    write=30.0,    # 写入超时
                    pool=30.0      # 连接池超时
                )
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    response = await client.post(
                        f"{server_status.url}/v1/chat/completions",
                        json=ollama_request,
                        headers={"Content-Type": "application/json"}
                    )
                    response.raise_for_status()
                    result = response.json()

                await self.update_task_status(task_id, "processing", progress=80)

                # 任务完成，更新计数
                if self.gpu_status[server_index]['llm_chat_tasks'] > 0:
                    self.gpu_status[server_index]['llm_chat_tasks'] -= 1
                    remaining_tasks = self.gpu_status[server_index]['llm_chat_tasks']
                    logging.info(f"Completed chat task on GPU {server_index}, "
                               f"remaining chat tasks: {remaining_tasks}")

                await self.update_task_status(task_id, "completed", result=result, progress=100)

                # 显示任务完成的清晰日志
                gpu_id = int(server_name.replace('server', ''))
                logging.info(f"✅✅✅ [LLM-CHAT] 任务 {task_id} 完成 → GPU{gpu_id} ✅✅✅")
                logging.info(f"    📊 GPU{gpu_id} 剩余Chat任务: {remaining_tasks}")
                logging.info(f"    ⏱️ 处理耗时: {time.time() - start_time:.2f}秒")
                success = True

            finally:
                # 确保任务计数被正确更新
                server_status.chat_processing = max(0, server_status.chat_processing - 1)
                try:
                    server_status.chat_queue.get_nowait()
                except:
                    pass

        except Exception as e:
            error_msg = str(e)
            logging.error(f"❌ LLM任务 {task_id} 失败: {error_msg}")

            # 清理任务计数
            try:
                server_name = task_data.get('server_name', '')
                if server_name:
                    server_index = int(server_name.replace('server', ''))
                    if self.gpu_status[server_index]['llm_chat_tasks'] > 0:
                        self.gpu_status[server_index]['llm_chat_tasks'] -= 1
            except Exception as cleanup_error:
                logging.debug(f"Error during cleanup: {cleanup_error}")

            await self.update_task_status(task_id, "failed", error=error_msg, progress=100)

        finally:
            # 记录性能指标
            response_time = time.time() - start_time
            self.record_task_completion("llm", response_time, success)

    async def _process_embedding_task(self, task_id: str, task_data: dict):
        """处理Embedding任务"""
        start_time = time.time()
        success = False

        try:
            request_data = task_data['request']
            server_name = task_data['server_name']
            server_status = task_data['server_status']
            input_text = task_data['input_text']

            # 提取GPU ID并显示清晰的分配日志
            gpu_id = int(server_name.replace('server', ''))
            logging.info(f"🔮🔮🔮 [EMBEDDING] 开始处理任务 {task_id} → GPU{gpu_id} 🔮🔮🔮")
            logging.info(f"    📍 服务器: {server_name} ({server_status.url})")
            logging.info(f"    🎯 模型: {request_data.get('model', 'N/A')}")
            logging.info(f"    📝 文本长度: {len(input_text) if input_text else 0} 字符")

            await self.update_task_status(task_id, "processing", progress=20)

            # 获取服务器索引
            try:
                server_index = int(server_name.replace('server', ''))
            except ValueError:
                # 如果server_name不是标准格式，从URL中解析
                server_index = int(server_status.url.split(':')[-1]) - 11434

            # 更新GPU任务计数
            self.gpu_status[server_index]['llm_embedding_tasks'] += 1

            # 更新任务的GPU ID
            self.task_results[task_id].gpu_id = server_index

            embed_tasks = self.gpu_status[server_index]['llm_embedding_tasks']
            chat_tasks = self.gpu_status[server_index]['llm_chat_tasks']
            logging.info(f"🔤 Started embedding task on GPU {server_index}, "
                       f"embedding tasks: {embed_tasks}, "
                       f"chat tasks: {chat_tasks}")

            await self.update_task_status(task_id, "processing", progress=40)

            try:
                # 发送请求到Ollama服务器，设置合理的超时时间
                timeout_config = httpx.Timeout(
                    connect=10.0,  # 连接超时
                    read=60.0,     # 读取超时（1分钟）
                    write=30.0,    # 写入超时
                    pool=30.0      # 连接池超时
                )
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    response = await client.post(
                        f"{server_status.url}/api/embeddings",
                        json=request_data,
                        headers={"Content-Type": "application/json"}
                    )
                    response.raise_for_status()
                    ollama_response = response.json()

                await self.update_task_status(task_id, "processing", progress=60)

                # 获取原始embedding
                raw_embedding = ollama_response.get("embedding", [])

                # hngpt-embedding模型需要归一化
                if raw_embedding:
                    processed_embedding = normalize_embedding(raw_embedding)
                else:
                    processed_embedding = raw_embedding

                # 构造返回结果，使用原始请求的模型名称
                original_model = task_data.get('original_model', 'hngpt-embedding')
                result = {
                    "object": "list",
                    "data": [
                        {
                            "object": "embedding",
                            "embedding": processed_embedding,
                            "index": 0
                        }
                    ],
                    "model": original_model,  # 返回用户请求的模型名称
                    "usage": {
                        "prompt_tokens": len(input_text.split()),
                        "total_tokens": len(input_text.split())
                    }
                }

                await self.update_task_status(task_id, "processing", progress=80)

                # 任务完成，更新计数
                if self.gpu_status[server_index]['llm_embedding_tasks'] > 0:
                    self.gpu_status[server_index]['llm_embedding_tasks'] -= 1
                    remaining_tasks = self.gpu_status[server_index]['llm_embedding_tasks']
                    logging.info(f"Completed embedding task on GPU {server_index}, "
                               f"remaining embedding tasks: {remaining_tasks}")

                await self.update_task_status(task_id, "completed", result=result, progress=100)

                # 显示任务完成的清晰日志
                gpu_id = int(server_name.replace('server', ''))
                logging.info(f"✅✅✅ [EMBEDDING] 任务 {task_id} 完成 → GPU{gpu_id} ✅✅✅")
                logging.info(f"    📊 GPU{gpu_id} 剩余Embedding任务: {remaining_tasks}")
                logging.info(f"    ⏱️ 处理耗时: {time.time() - start_time:.2f}秒")
                success = True

            finally:
                # 确保任务计数被正确更新
                server_status.embeddings_processing = max(0, server_status.embeddings_processing - 1)
                try:
                    server_status.embeddings_queue.get_nowait()
                except:
                    pass

        except Exception as e:
            error_msg = str(e)
            logging.error(f"❌ Embedding任务 {task_id} 失败: {error_msg}")

            # 清理任务计数
            try:
                server_name = task_data.get('server_name', '')
                if server_name:
                    server_index = int(server_name.replace('server', ''))
                    if self.gpu_status[server_index]['llm_embedding_tasks'] > 0:
                        self.gpu_status[server_index]['llm_embedding_tasks'] -= 1
            except Exception as cleanup_error:
                logging.debug(f"Error during cleanup: {cleanup_error}")

            await self.update_task_status(task_id, "failed", error=error_msg, progress=100)

        finally:
            # 记录性能指标
            response_time = time.time() - start_time
            self.record_task_completion("embedding", response_time, success)

# 创建全局统一任务管理器实例（包含GPU资源管理和性能监控）
unified_task_manager = UnifiedTaskManager()

# 注意：gpu_manager和performance_monitor已整合到unified_task_manager中

# 替代TaskRouter.get_route_stats()的函数
def get_route_stats() -> dict:
    """获取路由统计信息 - 替代TaskRouter.get_route_stats()"""
    # 获取统一任务管理器的统计
    unified_stats = unified_task_manager.get_task_stats()

    # 计算处理中的任务数（从GPU状态统计）
    processing_tasks = sum(
        unified_task_manager.gpu_status[i].get('llm_chat_tasks', 0) +
        unified_task_manager.gpu_status[i].get('llm_embedding_tasks', 0) +
        unified_task_manager.gpu_status[i].get('ocr_tasks', 0)
        for i in range(unified_task_manager.gpu_count)
    )

    return {
        'total_tasks': unified_stats["total_tasks"],
        'queue_size': 0,  # TaskRouter的队列已移除，始终为0
        'processing': processing_tasks,
        'completed': unified_stats["completed_tasks"],
        'gpu_status': {
            str(i): {
                'llm_tasks': unified_task_manager.gpu_status[i].get('llm_chat_tasks', 0),
                'embedding_tasks': unified_task_manager.gpu_status[i].get('llm_embedding_tasks', 0),
                'ocr_tasks': unified_task_manager.gpu_status[i].get('ocr_tasks', 0)
            } for i in range(unified_task_manager.gpu_count)
        }
    }

# GPU队列已移除 - 使用UnifiedTaskManager和GPUResourceManager统一管理

# Embedding模型配置 - 简化版本
EMBEDDING_MODEL_CONFIG = {
    "hngpt-embedding": {
        "supports_instructions": True,
        "default_instruction": "Represent this document for retrieval and similarity search:",
        "requires_normalization": True,
        "add_endoftext": True,
        "dimensions": 1024
    },
    "hngpt-embedding": {
        "supports_instructions": False,
        "default_instruction": "",
        "requires_normalization": False,
        "add_endoftext": False,
        "dimensions": None
    }
}

def prepare_embedding_text(text: str, model: str) -> str:
    """
    根据模型类型准备embedding文本 - 简化版本
    """
    model_config = EMBEDDING_MODEL_CONFIG.get(model, EMBEDDING_MODEL_CONFIG["hngpt-embedding"])

    # 如果模型支持指令感知，添加默认指令
    if model_config["supports_instructions"] and model_config["default_instruction"]:
        formatted_text = f"{model_config['default_instruction']}\n{text}"
    else:
        formatted_text = text

    # 添加结束标记（如果需要）
    if model_config["add_endoftext"]:
        formatted_text = f"{formatted_text}<|endoftext|>"

    return formatted_text

def normalize_embedding(embedding: List[float]) -> List[float]:
    """
    L2归一化embedding向量
    """
    if not embedding:
        return embedding

    np_embedding = np.array(embedding)
    norm = np.linalg.norm(np_embedding)
    if norm > 0:
        return (np_embedding / norm).tolist()
    return embedding


# 修改初始化函数
async def initialize():
    global SERVERS_CONFIG, authorized_users, recognizer
    
    # 动态根据GPU数量配置服务器
    gpu_available = torch.cuda.is_available()
    gpu_count = torch.cuda.device_count() if gpu_available else 1

    logging.info(f"🔍 GPU检测结果:")
    logging.info(f"  CUDA可用: {gpu_available}")
    logging.info(f"  检测到GPU数量: {gpu_count}")

    if gpu_available:
        for i in range(gpu_count):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            logging.info(f"  GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")

    logging.info(f"配置 {gpu_count} 个Ollama服务器")

    # 根据GPU数量动态生成服务器配置
    SERVERS_CONFIG = {}
    base_port = 11434  # Ollama默认端口

    logging.info(f"🔧 开始配置服务器:")
    for i in range(gpu_count):
        server_name = f'server{i}'
        server_port = base_port - i  # 端口递减：11434, 11433, 11432, ...
        server_url = f'http://localhost:{server_port}'
        SERVERS_CONFIG[server_name] = ServerStatus(url=server_url)
        logging.info(f"  ✓ {server_name} -> {server_url} (GPU {i})")

    logging.info(f"📊 服务器配置完成:")
    logging.info(f"  总服务器数: {len(SERVERS_CONFIG)}")
    logging.info(f"  服务器列表: {list(SERVERS_CONFIG.keys())}")
    logging.info(f"  端口范围: {base_port - gpu_count + 1} - {base_port}")

    # 如果没有GPU，至少配置一个服务器
    if not SERVERS_CONFIG:
        SERVERS_CONFIG['server0'] = ServerStatus(url='http://localhost:11434')
        logging.info("No GPU detected, configured single server on CPU")
    
    # 初始化用户列表
    authorized_users = await get_authorized_users("authorized_users.txt")

    # 预初始化OCR和Layout实例
    await initialize_ocr_and_layout_instances()

    # # 初始化识别器，直接使用 HSRecognizer
    # recognizer = HSRecognizer(models_dir="models")

    logging.info(f"Initialized {len(SERVERS_CONFIG)} servers for load balancing")
    logging.info(f"Loaded {len(authorized_users)} authorized users")
    # logging.info("Initialized HSRecognizer")

    # 检查所有服务器的初始状态
    await check_all_servers_status()

async def initialize_ocr_and_layout_instances():
    """预初始化所有GPU的OCR和Layout实例"""
    import rec

    gpu_count = get_gpu_count()
    logging.info(f"🚀 开始预初始化OCR和Layout实例，GPU数量: {gpu_count}")

    for gpu_id in range(gpu_count):
        try:
            logging.info(f"📦 初始化GPU {gpu_id} 的实例...")

            # 预初始化OCR实例（v5版本）
            ocr_success = rec.MultiGPUOCR.initInstance(gpu_id, "v5")
            if ocr_success:
                logging.info(f"  ✅ GPU {gpu_id} OCR实例初始化成功")
            else:
                logging.warning(f"  ⚠️ GPU {gpu_id} OCR实例初始化失败")

            # 预初始化Layout实例
            layout_success = rec.MultiGPULayout.initInstance(gpu_id, 0.3)
            if layout_success:
                logging.info(f"  ✅ GPU {gpu_id} Layout实例初始化成功")
            else:
                logging.warning(f"  ⚠️ GPU {gpu_id} Layout实例初始化失败")

        except Exception as e:
            logging.error(f"  ❌ GPU {gpu_id} 实例初始化失败: {str(e)}")

    logging.info("🎉 OCR和Layout实例预初始化完成")

async def check_all_servers_status():
    """检查所有配置的服务器状态"""
    logging.info(f"🔍 检查所有服务器状态:")

    for server_name, server_status in SERVERS_CONFIG.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{server_status.url}/api/tags")
                if response.status_code == 200:
                    models = response.json().get("models", [])
                    logging.info(f"  ✅ {server_name} ({server_status.url}): 在线, {len(models)}个模型")
                    # 健康状态属性已删除，不再设置
                else:
                    logging.warning(f"  ❌ {server_name} ({server_status.url}): HTTP {response.status_code}")
                    # 健康状态属性已删除，不再设置
        except Exception as e:
            logging.error(f"  ❌ {server_name} ({server_status.url}): 连接失败 - {str(e)}")
            # 健康状态属性已删除，不再设置

    # 健康检查已删除，所有服务器默认为健康
    healthy_servers = list(SERVERS_CONFIG.keys())

    logging.info(f"📊 服务器状态总结:")
    logging.info(f"  配置服务器: {len(healthy_servers)}/{len(SERVERS_CONFIG)}")
    logging.info(f"  服务器列表: {healthy_servers}")

    if len(healthy_servers) == 0:
        logging.error("⚠️ 没有健康的服务器！请检查Ollama服务是否正常运行")
    elif len(healthy_servers) < len(SERVERS_CONFIG):
        unhealthy = [name for name in SERVERS_CONFIG.keys() if name not in healthy_servers]
        logging.warning(f"⚠️ 部分服务器不健康: {unhealthy}")

# 健康检查已删除 - 不再需要定期检查服务器健康状态

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logging.info("Starting application...")
    
    # 检查CUDA环境
    check_cuda_environment()
    
    # 初始化应用
    await initialize()
    yield
    # Shutdown
    logging.info("🔄 正在关闭应用...")

    # 清理资源
    try:
        try:
            logging.info("清理OCR和Layout实例...")
            rec.MultiGPUOCR.finalizeAll()
            rec.MultiGPULayout.finalizeAll()
        except Exception as e:
            logging.warning(f"清理实例时出错: {e}")

        logging.info("✅ 应用关闭清理完成")
    except Exception as e:
        logging.error(f"应用关闭时出错: {e}")
    
    # 清理GPU资源
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

# 生成队列状态可视化的函数
def get_queue_status_bar(server_name, queue_size, processing, max_queue_size, endpoint_type="chat"):
    """生成ASCII进度条显示队列状态"""
    # 进度条宽度等于max_queue_size
    bar_width = max_queue_size
    
    # 根据状态决定显示
    if queue_size > 0 or processing > 0:
        # 有请求在处理中或队列中
        bar = '█' * bar_width
    else:
        # 空闲状态
        bar = '░' * bar_width
    
    # 计算利用率百分比
    utilization = 100 * (queue_size + processing) / max(max_queue_size, 1)
    
    return f"[{server_name}:{endpoint_type}] [{bar}] {queue_size}队列/{processing}处理中/最大{max_queue_size} ({utilization:.0f}%)"

def select_best_gpu_for_task(task_type: str) -> Optional[int]:
    """
    清晰简单的GPU选择算法

    Args:
        task_type: 'chat', 'embeddings', 'ocr'

    Returns:
        最佳GPU的ID，如果没有可用GPU则返回None
    """
    if not torch.cuda.is_available():
        return 0  # 如果没有CUDA，使用CPU模式

    gpu_count = torch.cuda.device_count()
    if gpu_count == 0:
        return 0

    # 并发限制定义
    CONCURRENT_LIMITS = {
        'chat': 1,      # Chat任务每GPU最多1个
        'embeddings': 3, # Embedding任务每GPU最多3个
        'ocr': 1        # OCR任务每GPU最多1个
    }

    max_concurrent = CONCURRENT_LIMITS.get(task_type, 1)

    # 收集所有GPU的负载信息
    gpu_candidates = []

    for gpu_id in range(gpu_count):
        try:
            # 获取GPU硬件指标
            gpu_info = get_gpu_info(gpu_id)
            gpu_util = gpu_info.get('gpu_utilization_percent', 0)
            memory_util = gpu_info.get('memory_utilization_percent', 0)

            # 获取当前任务数量
            gpu_status = unified_task_manager.gpu_status.get(gpu_id, {})
            current_tasks = 0

            if task_type == 'chat':
                current_tasks = gpu_status.get('llm_chat_tasks', 0)
            elif task_type == 'embeddings':
                current_tasks = gpu_status.get('llm_embedding_tasks', 0)
            elif task_type == 'ocr':
                current_tasks = gpu_status.get('ocr_tasks', 0)

            # 检查并发限制
            if current_tasks >= max_concurrent:
                logging.debug(f"GPU{gpu_id} 已达到{task_type}任务并发限制: {current_tasks}/{max_concurrent}")
                continue  # 跳过已满的GPU

            # 计算优先级得分（越高越好）
            # 优先级1: GPU使用率低的得分高 (权重40%)
            gpu_score = (100 - gpu_util) * 0.4

            # 优先级2: 显存使用率低的得分高 (权重35%)
            memory_score = (100 - memory_util) * 0.35

            # 优先级3: 任务数量少的得分高 (权重25%)
            task_score = (max_concurrent - current_tasks) * 25.0 / max_concurrent * 0.25

            # 总得分 = GPU得分 + 显存得分 + 任务得分
            total_score = gpu_score + memory_score + task_score

            gpu_candidates.append({
                'gpu_id': gpu_id,
                'total_score': total_score,
                'gpu_util': gpu_util,
                'memory_util': memory_util,
                'current_tasks': current_tasks,
                'gpu_score': gpu_score,
                'memory_score': memory_score,
                'task_score': task_score
            })

            logging.info(f"🔍 GPU{gpu_id}: 得分{total_score:.1f} (GPU:{gpu_util:.1f}% 显存:{memory_util:.1f}% 任务:{current_tasks}/{max_concurrent})")

        except Exception as e:
            logging.warning(f"GPU{gpu_id} 状态检查失败: {e}")
            continue

    # 如果没有可用GPU
    if not gpu_candidates:
        logging.warning(f"没有可用的GPU用于{task_type}任务")
        return None

    # 选择总得分最高的GPU
    best_gpu = max(gpu_candidates, key=lambda x: x['total_score'])

    logging.info(f"🎯 选择GPU{best_gpu['gpu_id']}用于{task_type}任务: 得分{best_gpu['total_score']:.1f}")

    return best_gpu['gpu_id']


# 优化：简化的服务器选择函数
async def get_best_server(endpoint: str = "chat") -> Tuple[str, ServerStatus]:
    """使用清晰GPU选择算法的服务器选择函数"""
    if not SERVERS_CONFIG:
        raise HTTPException(
            status_code=503,
            detail="没有可用的服务器"
        )

    # 步骤1: 映射endpoint类型到task_type
    if endpoint == 'chat':
        task_type = 'chat'
    elif endpoint == 'embeddings':
        task_type = 'embedding'  # 注意：统一任务管理器中使用'embedding'
    elif endpoint == 'ocr':
        task_type = 'ocr'
    else:
        task_type = 'chat'  # 默认使用chat

    # 步骤2: 使用GPU选择算法
    best_gpu_id = select_best_gpu_for_task(task_type)

    if best_gpu_id is None:
        # 如果没有可用的GPU，尝试轮询所有服务器
        logging.warning(f"没有可用的GPU用于{task_type}任务，尝试轮询服务器")
        return await _select_server_by_load(endpoint)

    # 步骤3: 根据GPU ID选择对应的服务器
    server_name = f"server{best_gpu_id}"

    if server_name not in SERVERS_CONFIG:
        # 如果指定的服务器不存在，尝试轮询
        logging.warning(f"服务器{server_name}不存在，尝试轮询服务器")
        return await _select_server_by_load(endpoint)

    selected_server = SERVERS_CONFIG[server_name]

    # 步骤4: 验证服务器可用性
    if not _is_server_available(selected_server, endpoint):
        logging.warning(f"服务器{server_name}队列已满，尝试轮询服务器")
        return await _select_server_by_load(endpoint)

    # 步骤5: 记录分配信息
    try:
        gpu_info = get_gpu_info(best_gpu_id)
        if gpu_info:
            memory_util = gpu_info.get('memory_utilization_percent', 0)
            logging.info(f"🎯 [{endpoint.upper()}] 任务分配 → GPU{best_gpu_id} (内存: {memory_util:.1f}%)")
        else:
            logging.info(f"🎯 [{endpoint.upper()}] 任务分配 → GPU{best_gpu_id}")
    except Exception as e:
        logging.info(f"🎯 [{endpoint.upper()}] 任务分配 → GPU{best_gpu_id}")
        logging.debug(f"GPU信息获取失败: {e}")

    return (server_name, selected_server)


def _is_server_available(server: ServerStatus, endpoint: str) -> bool:
    """检查服务器是否可用"""
    max_concurrent = server.get_max_concurrent(endpoint)

    if endpoint == "chat":
        return (server.chat_processing < max_concurrent and
                server.chat_queue.qsize() < server.max_queue_size)
    elif endpoint == "embeddings":
        return (server.embeddings_processing < max_concurrent and
                server.embeddings_queue.qsize() < server.max_queue_size)
    else:
        # 对于其他类型（如OCR），默认可用
        return True


async def _select_server_by_load(endpoint: str) -> Tuple[str, ServerStatus]:
    """基于负载选择服务器（回退方案）"""
    available_servers = []

    # 检查所有服务器的可用性
    for name, server in SERVERS_CONFIG.items():
        if _is_server_available(server, endpoint):
            # 计算负载分数（越低越好）
            if endpoint == "chat":
                load_score = server.chat_processing + server.chat_queue.qsize()
            elif endpoint == "embeddings":
                load_score = server.embeddings_processing + server.embeddings_queue.qsize()
            else:
                load_score = 0

            available_servers.append((name, server, load_score))

    if not available_servers:
        raise HTTPException(
            status_code=503,
            detail=f"所有服务器都不可用于{endpoint}请求"
        )

    # 选择负载最低的服务器
    available_servers.sort(key=lambda x: x[2])
    best_server_name, best_server, load_score = available_servers[0]

    logging.info(f"🔄 [{endpoint.upper()}] 负载均衡选择 → {best_server_name} (负载: {load_score})")

    return (best_server_name, best_server)

class RequestBody(BaseModel):
    # Add fields according to your API requirements
    pass

# 添加 ShutdownRequest 模型定义
class ShutdownRequest(BaseModel):
    confirm: bool = Field(..., description="Set to true to confirm shutdown")

class LoggerMiddleware(BaseHTTPMiddleware):
    # 需要过滤的路由（不记录日志）
    FILTERED_PATHS = {
        "/monitor/api/status",
        "/health",
        "/ping",
        "/favicon.ico"
    }

    async def dispatch(self, request: Request, call_next):
        start_time = time.time()
        response = await call_next(request)
        end_time = time.time()

        # 过滤掉频繁的监控路由
        if request.url.path not in self.FILTERED_PATHS:
            processing_time = end_time - start_time

            # 根据处理时间选择日志级别和表情
            if processing_time > 5.0:
                emoji = "🐌"
                logging.error(
                    f"{emoji} Path: {request.url.path} "
                    f"Method: {request.method} "
                    f"Client: {request.client.host} "
                    f"Time: {processing_time:.3f}s"
                )
            elif processing_time > 2.0:
                emoji = "⚠️"
                logging.warning(
                    f"{emoji} Path: {request.url.path} "
                    f"Method: {request.method} "
                    f"Client: {request.client.host} "
                    f"Time: {processing_time:.3f}s"
                )
            elif processing_time > 1.0:
                emoji = "🔶"
                logging.info(
                    f"{emoji} Path: {request.url.path} "
                    f"Method: {request.method} "
                    f"Client: {request.client.host} "
                    f"Time: {processing_time:.3f}s"
                )
            else:
                emoji = "✅"
                logging.info(
                    f"{emoji} Path: {request.url.path} "
                    f"Method: {request.method} "
                    f"Client: {request.client.host} "
                    f"Time: {processing_time:.3f}s"
                )

        return response

app = FastAPI(
    title="hngpt AI Model Server",
    version="0.1.0",
    lifespan=lifespan,
    docs_url=None,  # 禁用默认的 /docs 端点
    redoc_url=None  # 禁用默认的 /redoc 端点
)

# 添加 CORS 中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加安全中间件
app.add_middleware(TrustedHostMiddleware, allowed_hosts=["*"])

@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    
    csp_directives = [  
        "default-src 'self' 'unsafe-inline' 'unsafe-eval' *",  
        "img-src 'self' data: blob: * http://localhost:8889",  
        "style-src 'self' 'unsafe-inline' * http://localhost:8889",  
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' * http://localhost:8889",  
        "connect-src 'self' * http://localhost:8889",  
        "font-src 'self' data: * http://localhost:8889",  
        "frame-src 'self' * http://localhost:8889",  
        "object-src 'self' * http://localhost:8889"  
    ]
    # 将指令列表合并为单个字符串，确保使用分号和空格分隔
    csp_value = "; ".join(csp_directives)
    
    # 添加其他全头
    response.headers["Content-Security-Policy"] = csp_value
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
    
    return response

app.mount("/static", StaticFiles(directory="static"), name="static")

@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义 Swagger UI 页面"""
    html = get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",
        swagger_css_url="/static/swagger-ui/swagger-ui.css",
        swagger_favicon_url="/static/favicon.ico",
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,
            "deepLinking": True,
            "displayRequestDuration": True,
            "filter": True,
            "tryItOutEnabled": True,
            "persistAuthorization": True
        }
    )
    
    content = html.body.decode()
    
    # 添加自定义脚本
    custom_script = """
    <script>
        // 等待 Swagger UI 加载完成
        const interval = setInterval(() => {
            const authorizeBtn = document.querySelector('.swagger-ui .auth-wrapper button.authorize');
            if (authorizeBtn) {
                clearInterval(interval);
                
                // 创建 Supervisor 按钮
                const supervisorBtn = document.createElement('button');
                supervisorBtn.className = 'btn authorize';
                supervisorBtn.style.marginRight = '10px';
                supervisorBtn.onclick = () => {
                    const auth = localStorage.getItem('authorized');
                    if (auth) {
                        try {
                            const authData = JSON.parse(auth);
                            if (authData.HTTPBearer && authData.HTTPBearer.value === 'hngpt_admin@8888') {
                                // 直接在新窗口中打开，URL 中包含认证信息
                                window.open('/supervisor/?auth=' + encodeURIComponent('Bearer ' + authData.HTTPBearer.value), '_blank');
                            } else {
                                alert('Admin access required');
                            }
                        } catch (e) {
                            alert('Please authenticate first');
                        }
                    } else {
                        alert('Please authenticate first');
                    }
                };
                supervisorBtn.textContent = 'Open Supervisor';
                
                // 插入到 Authorize 按钮前面
                authorizeBtn.parentNode.insertBefore(supervisorBtn, authorizeBtn);
            }
        }, 100);
    </script>
    """
    
    content = content.replace('</body>', custom_script + '</body>')
    return HTMLResponse(content)

# 添加认证中转路由
@app.post("/supervisor/auth")
async def supervisor_auth(
    request: Request,
    response: Response
):
    form = await request.form()
    token = form.get('token')
    
    if token != 'hngpt_admin@8888':
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # 置认证 cookie
    response = RedirectResponse(url="/supervisor/")
    response.set_cookie(
        key="supervisor_auth",
        value=token,
        httponly=True,
        secure=False,  # 本地开为 False
        samesite='lax'
    )
    return response

# 修改 supervisor 代理由
@app.api_route("/supervisor/{path:path}", methods=["GET", "POST", "PUT", "DELETE"])
async def supervisor_proxy(
    request: Request, 
    path: str = "",
    auth: Optional[str] = Query(None)
):
    # 查认证
    token = None
    if auth:
        if auth.startswith('Bearer '):
            token = auth.split(' ')[1]
        else:
            token = auth
    
    if not token or token != "hngpt_admin@8888":
        raise HTTPException(status_code=403, detail="Not authorized")
    
    # 构建 supervisor URL，移除查询参数中的 auth
    query_params = dict(request.query_params)
    query_params.pop('auth', None)
    
    # 处理文件参数
    file_name = query_params.pop('file', None)
    
    # 构建完整的 supervisor URL
    clean_path = path.strip('/')
    if file_name:
        supervisor_url = f"http://localhost:8889/{clean_path}/{file_name}"
    else:
        supervisor_url = f"http://localhost:8889/{clean_path}"
        if not supervisor_url.endswith('/'):
            supervisor_url += '/'
    
    logging.info(f"Proxying request to: {supervisor_url}")
    
    try:
        async with httpx.AsyncClient() as client:
            # 直接转发请求到 supervisor
            response = await client.request(
                method=request.method,
                url=supervisor_url,
                params=query_params,  # 使用剩余的查询参数
                headers={
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                },
                content=await request.body() if request.method != "GET" else None,
                follow_redirects=True
            )
            
            # 获取响应内容
            content = await response.aread()
            
            # 如果是 HTML 响应，修改资源路径和操作链接
            if response.headers.get('content-type', '').startswith('text/html'):
                content = content.decode('utf-8')
                
                # 修改资源路径，使用完的 supervisor URL
                content = content.replace('href="http://localhost:8889/', f'href="/supervisor/?auth={token}&path=')
                content = content.replace('src="http://localhost:8889/', f'src="/supervisor/?auth={token}&path=')
                
                # 修改相对路径
                content = content.replace('href="stylesheets/', f'href="/supervisor/stylesheets?auth={token}&file=')
                content = content.replace('src="images/', f'src="/supervisor/images?auth={token}&file=')
                
                # 修改操作链接
                content = content.replace('href="index.html?', f'href="/supervisor/?auth={token}&')
                content = content.replace('href="?', f'href="/supervisor/?auth={token}&')
                
                content = content.encode('utf-8')
            
            # 设置应头，不包含 content-length
            headers = {
                'Content-Type': response.headers.get('content-type', 'text/html'),
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
            
            # 根据文件类型设置确 content-type
            if supervisor_url.endswith('.css'):
                headers['Content-Type'] = 'text/css'
            elif supervisor_url.endswith('.js'):
                headers['Content-Type'] = 'application/javascript'
            elif supervisor_url.endswith('.png'):
                headers['Content-Type'] = 'image/png'
            elif supervisor_url.endswith('.gif'):
                headers['Content-Type'] = 'image/gif'
            
            # 使用 StreamingResponse 而不是 Response
            return StreamingResponse(
                content=iter([content]),
                status_code=response.status_code,
                headers=headers
            )
            
    except Exception as e:
        logging.error(f"Error proxying to supervisor: {str(e)}")
        raise HTTPException(status_code=502, detail=f"Failed to connect to supervisor: {str(e)}")

# 添加一个专门处理 /images 路径的路由
@app.get("/images/{file_path:path}")
async def proxy_supervisor_images(
    request: Request,
    file_path: str
):
    # 直接代 supervisor 的 images 径
    supervisor_url = f"http://localhost:8889/images/{file_path}"
    
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(
                supervisor_url,
                headers={
                    'Accept': '*/*',
                    'Accept-Encoding': 'gzip, deflate',
                    'Connection': 'keep-alive'
                },
                follow_redirects=True
            )
            
            # 设置正确的 content-type
            headers = {
                'Content-Type': 'image/gif' if file_path.endswith('.gif') else 'image/png',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
            
            return StreamingResponse(
                content=iter([await response.aread()]),
                status_code=response.status_code,
                headers=headers
            )
            
    except Exception as e:
        logging.error(f"Error proxying to supervisor images: {str(e)}")
        raise HTTPException(status_code=502, detail=f"Failed to connect to supervisor: {str(e)}")

@app.get("/docs/oauth2-redirect")
async def swagger_ui_redirect():
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>OAuth2 Redirect</title>
    </head>
    <body>
        <script>
            window.onload = function() {
                window.opener.swaggerUIRedirectOauth2(window.location.href);
                window.close();
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(html_content)


# 2. 添加首页路由
@app.get("/")
async def read_root():
    # 修改 index.html 中的资源路径
    with open("static/index.html", "r", encoding="utf-8") as f:
        content = f.read()
        # 替换所有资源引用路径，但要避免重复替换
        content = content.replace('href="', 'href="/static/')
        content = content.replace('src="', 'src="/static/')
        # 修复可能的双重 /static/ 问题
        content = content.replace('/static//static/', '/static/')
        # 不替换特定的 href
        content = content.replace('href="/static/http', 'href="http')
        return HTMLResponse(content)

# 3. 添加 favicon.ico 路由
@app.get("/favicon.ico")
async def get_favicon():
    return FileResponse("static/favicon.ico")

# 添加日志中间件
app.add_middleware(LoggerMiddleware)

async def get_authorized_users(filename):
    try:
        with open(filename, 'r') as f:
            lines = f.readlines()
        authorized_users = {}
        for line in lines:
            line = line.strip()
            if line:  # 跳过空行
                try:
                    user, key = line.split(':')
                    authorized_users[key.strip()] = user.strip()
                except ValueError:
                    logging.warning(f"Skipping invalid line in users file: {line}")
        return authorized_users
    except Exception as e:
        logging.error(f"Error reading authorized users: {e}")
        return {}



# 添 OpenAI 兼容的模型
class OpenAIEmbeddingRequest(BaseModel):
    """OpenAI 格式的 Embedding 请求"""
    model: str
    input: Union[str, List[str]]  # 可以是单个字符串字符串列表
    encoding_format: Optional[str] = "float"
    user: Optional[str] = None

# 标准格式的 Embedding 请求
class EmbeddingRequest(BaseModel):
    """标准格式的 Embedding 请求"""
    model: str
    prompt: str
    encoding_format: Optional[str] = "float"
    user: Optional[str] = None




# OpenAI 格式的请求模型
class ChatMessage(BaseModel):
    role: str
    content: str
    name: Optional[str] = None

class ChatCompletionRequest(BaseModel):
    model: str
    messages: List[ChatMessage]
    temperature: Optional[float] = 0.7
    stream: Optional[bool] = False
    max_tokens: Optional[int] = None

# OpenAI 格式的响应模
class ChatCompletionChoice(BaseModel):
    index: int
    message: ChatMessage
    finish_reason: Optional[str] = None

class ChatCompletionResponse(BaseModel):
    id: str = Field(default_factory=lambda: f"chatcmpl-{int(time.time()*1000)}")
    object: str = "chat.completion"
    created: int = Field(default_factory=lambda: int(time.time()))
    model: str
    choices: List[ChatCompletionChoice]
    usage: Dict[str, int]

# Add this new retry function for non-streaming requests
async def retry_with_backoff(func, max_retries=3, initial_delay=1):
    """
    Regular request retry decorator
    """
    last_error = None
    for attempt in range(max_retries):
        try:
            return await func()
        except httpx.ConnectError as e:
            last_error = e
            if attempt == max_retries - 1:
                break
            delay = initial_delay * (2 ** attempt)
            logging.warning(f"Connection failed (attempt {attempt + 1}/{max_retries}), retrying in {delay}s...")
            await asyncio.sleep(delay)
            continue
        except Exception as e:
            logging.error(f"Unexpected error in retry_with_backoff: {str(e)}")
            raise

    if last_error:
        raise last_error

# Update the embeddings endpoint to use the new retry function
@app.post("/v1/embeddings")
@app.post("/api/embeddings")
async def create_embeddings(
    request: Union[EmbeddingRequest, OpenAIEmbeddingRequest],
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials

    # 显示接收到的Embedding请求日志
    input_text = getattr(request, 'input', '') or getattr(request, 'text', '')
    logging.info(f"📥📥📥 [API] 接收到Embedding请求 📥📥📥")
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    server_name = None
    server_status = None
    
    try:
        # 获取处理 embeddings 的最佳服务器
        server_name, server_status = await get_best_server("embeddings")

        # 准备任务数据
        if isinstance(request, OpenAIEmbeddingRequest):
            input_text = request.input[0] if isinstance(request.input, list) else request.input
        else:
            input_text = request.prompt

        # 根据模型类型准备文本
        prepared_text = prepare_embedding_text(input_text, request.model)

        embedding_request = {
            "model": "hngpt-embedding",  # 统一使用hngpt-embedding模型
            "prompt": prepared_text
        }

        task_data = {
            'request': embedding_request,
            'server_name': server_name,
            'server_status': server_status,
            'input_text': input_text,
            'original_model': request.model  # 保存用户请求的原始模型名称
        }

        # 使用统一任务管理器创建任务
        task_id = await unified_task_manager.create_task('embedding', task_data)
        logging.info(f"Created embedding task ID: {task_id}")

        # 等待任务完成（同步处理）
        result = await unified_task_manager.get_task_result(task_id, 30.0)

        if result and result.status == "completed":
            return result.result
        else:
            error_msg = result.error if result else "任务未找到"
            raise HTTPException(status_code=500, detail=error_msg)


            
    except Exception as e:
        logging.error(f"Embeddings 处理错误: {str(e)}")
        # 记录错误
        if server_status:
            server_status.record_error("embeddings")
            
        # 确保在错误情况下也清理队列和更新计数
        try:
            if server_status:
                server_status.embeddings_queue.get_nowait()
                server_status.embeddings_processing -= 1
                queue_status = get_queue_status_bar(
                    server_name, 
                    server_status.embeddings_queue.qsize(), 
                    server_status.embeddings_processing, 
                    server_status.max_queue_size, 
                    "embeddings"
                )
                # 简化队列状态日志 - 只在调试模式下显示
                if logging.getLogger().level <= logging.DEBUG:
                    logging.debug(f"服务器队列状态更新: {queue_status}")
        except Exception as cleanup_error:
            logging.error(f"清理嵌入队列时出错: {str(cleanup_error)}")
        raise HTTPException(status_code=500, detail=str(e))
    finally:
        # 完成后更新计数
        try:
            if server_status:
                server_status.embeddings_queue.get_nowait()
                server_status.embeddings_processing -= 1

                # GPU任务计数由统一任务管理器处理
                queue_status = get_queue_status_bar(
                    server_name, 
                    server_status.embeddings_queue.qsize(), 
                    server_status.embeddings_processing, 
                    server_status.max_queue_size, 
                    "embeddings"
                )
                # 简化队列状态日志 - 只在调试模式下显示
                if logging.getLogger().level <= logging.DEBUG:
                    logging.debug(f"服务器队列状态更新: {queue_status}")
        except Exception as e:
            # 如果队列已经在try块或错误处理中被清理，这里会抛出异常，可以忽略
            pass


# 统一的聊天接口 - 使用标准OpenAI格式
@app.post("/v1/chat/completions")
async def unified_chat_completion(
    request: ChatCompletionRequest,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials

    # 显示接收到的请求日志
    logging.info(f"📥📥📥 [API] 接收到Chat请求 📥📥📥")
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 获取处理聊天的最佳服务器
    server_name, server_status = await get_best_server("chat")
    server_index = int(server_status.url.split(':')[-1]) - 11434

    # 创建统一任务
    task_data = {
        'request': request,
        'server_name': server_name,
        'server_status': server_status,
        'start_time': time.time()
    }

    # 通过统一任务管理器创建任务
    task_id = await unified_task_manager.create_task("llm", task_data, gpu_id=server_index)

    queue_item_added = False  # 跟踪队列项是否已添加
    start_time = task_data['start_time']  # 从task_data获取开始时间

    try:
        # 更新队列和处理计数
        server_status.chat_queue.put_nowait(1)
        server_status.chat_processing += 1
        queue_item_added = True
   
        queue_status = get_queue_status_bar(
            server_name, 
            server_status.chat_queue.qsize(), 
            server_status.chat_processing, 
            server_status.max_queue_size, 
            "chat"
        )
        # 简化队列状态日志 - 只在调试模式下显示
        if logging.getLogger().level <= logging.DEBUG:
            logging.debug(queue_status)
        
        # 使用标准OpenAI格式的消息
        messages = [msg.model_dump() for msg in request.messages]

        # 确保至少有一条消息
        if not messages:
            raise HTTPException(status_code=400, detail="No valid messages provided")

        # 构造统一的请求格式
        ollama_request = {
            "model": request.model,
            "messages": messages,
            "stream": request.stream,
            "temperature": request.temperature,
            "max_tokens": request.max_tokens
        }
        
        if ollama_request["stream"]:
            # 对于流式响应，在生成器中处理队列的清理
            async def stream_with_cleanup():
                cleanup_done = False
                try:
                    async for chunk in stream_chat_completion(ollama_request, {"url": server_status.url}):
                        yield chunk
                except Exception as e:
                    # 记录流式处理错误
                    error_msg = f"流式聊天处理错误: {str(e)}"
                    logging.error(error_msg)
                    # 记录错误
                    if server_status and not cleanup_done:
                        server_status.record_error("chat")
                    yield f"data: {{\"error\": \"{error_msg}\"}}\n\n"
                finally:
                    # 在流式响应完成后清理队列
                    try:
                        if queue_item_added and not cleanup_done:
                            server_status.chat_queue.get_nowait()
                            server_status.chat_processing -= 1
                            cleanup_done = True
                            queue_status = get_queue_status_bar(
                                server_name, 
                                server_status.chat_queue.qsize(), 
                                server_status.chat_processing, 
                                server_status.max_queue_size, 
                                "chat"
                            )
                            # 简化队列状态日志 - 只在调试模式下显示
                            if logging.getLogger().level <= logging.DEBUG:
                                logging.debug(f"服务器队列状态更新: {queue_status}")
                    except Exception as e:
                        logging.error(f"清理聊天队列时出错: {str(e)}")
            
            return StreamingResponse(
                stream_with_cleanup(),
                media_type="text/event-stream"
            )
        else:
            cleanup_done = False
            try:
                # 设置合理的超时时间
                timeout_config = httpx.Timeout(
                    connect=10.0,  # 连接超时
                    read=90.0,     # 读取超时
                    write=30.0,    # 写入超时
                    pool=30.0      # 连接池超时
                )
                async with httpx.AsyncClient(timeout=timeout_config) as client:
                    response = await client.post(
                        f"{server_status.url}/v1/chat/completions",
                        json=ollama_request
                    )
                    response.raise_for_status()
                    result = response.json()
                    
                    # 优化：记录成功和响应时间
                    response_time = time.time() - start_time
                    server_status.record_success("chat", response_time)

                    # 在返回响应前自行清理队列
                    if queue_item_added and not cleanup_done:
                        server_status.chat_queue.get_nowait()
                        server_status.chat_processing -= 1
                        cleanup_done = True

                        # GPU任务计数由统一任务管理器处理
                        queue_status = get_queue_status_bar(
                            server_name, 
                            server_status.chat_queue.qsize(), 
                            server_status.chat_processing, 
                            server_status.max_queue_size, 
                            "chat"
                        )
                        # 简化队列状态日志 - 只在调试模式下显示
                        if logging.getLogger().level <= logging.DEBUG:
                            logging.debug(f"服务器队列状态更新: {queue_status}")
                    
                    return ChatCompletionResponse(
                        model=request.model,
                        choices=[
                            ChatCompletionChoice(
                                index=0,
                                message=ChatMessage(
                                    role="assistant",
                                    content=result["choices"][0]["message"]["content"]
                                ),
                                finish_reason=result["choices"][0].get("finish_reason")
                            )
                        ],
                        usage={
                            "prompt_tokens": result.get("usage", {}).get("prompt_tokens", 0),
                            "completion_tokens": result.get("usage", {}).get("completion_tokens", 0),
                            "total_tokens": result.get("usage", {}).get("total_tokens", 0)
                        }
                    )
            except Exception as e:
                # 记录错误
                if server_status:
                    server_status.record_error("chat")
                
                # 在异常发生时自行清理队列，避免在finally中重复清理
                if queue_item_added and not cleanup_done:
                    try:
                        server_status.chat_queue.get_nowait()
                        server_status.chat_processing -= 1
                        cleanup_done = True
                        queue_status = get_queue_status_bar(
                            server_name, 
                            server_status.chat_queue.qsize(), 
                            server_status.chat_processing, 
                            server_status.max_queue_size, 
                            "chat"
                        )
                        # 简化队列状态日志 - 只在调试模式下显示
                        if logging.getLogger().level <= logging.DEBUG:
                            logging.debug(f"服务器队列状态更新: {queue_status}")
                    except Exception as cleanup_error:
                        logging.error(f"清理聊天队列时出错: {str(cleanup_error)}")
                
                # 重新抛出异常
                raise e
    except Exception as e:
        logging.error(f"聊天处理错误: {str(e)}")
        if server_status and queue_item_added and not 'cleanup_done' in locals():
            # 确保在错误情况下也清理队列，只有在前面未清理的情况下
            try:
                server_status.chat_queue.get_nowait()
                server_status.chat_processing -= 1
                queue_status = get_queue_status_bar(
                    server_name, 
                    server_status.chat_queue.qsize(), 
                    server_status.chat_processing, 
                    server_status.max_queue_size, 
                    "chat"
                )
                # 简化队列状态日志 - 只在调试模式下显示
                if logging.getLogger().level <= logging.DEBUG:
                    logging.debug(f"服务器队列状态更新: {queue_status}")
            except Exception as cleanup_error:
                logging.error(f"清理队列时出错: {str(cleanup_error)}")
        raise HTTPException(status_code=500, detail=str(e))

# 为了保持向后兼容性，添加重定向路由
@app.post("/api/generate")
@app.post("/api/chat")
async def legacy_chat_endpoints(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """
    重定向旧的接口到新的统一接口
    """
    try:
        # 获取原始请求数据
        raw_data = await request.json()
        
        # 转换为标准 ChatCompletionRequest 格式
        messages = []

        # 添加系统消息
        if raw_data.get("system"):
            messages.append(ChatMessage(role="system", content=raw_data["system"]))

        # 添加上下文消息
        if raw_data.get("context"):
            messages.append(ChatMessage(role="assistant", content=raw_data["context"]))

        # 添加用户消息
        if raw_data.get("prompt"):
            messages.append(ChatMessage(role="user", content=raw_data["prompt"]))

        # 如果有直接的messages字段，也要处理
        if raw_data.get("messages"):
            for msg in raw_data["messages"]:
                if isinstance(msg, dict) and msg.get("content"):
                    messages.append(ChatMessage(
                        role=msg.get("role", "user"),
                        content=msg["content"]
                    ))

        if not messages:
            raise HTTPException(status_code=400, detail="No valid messages provided")

        chat_request = ChatCompletionRequest(
            model=raw_data.get("model", "hngpt"),
            messages=messages,
            temperature=raw_data.get("temperature", 0.7),
            max_tokens=raw_data.get("max_tokens", 2000),
            stream=raw_data.get("stream", True)
        )
        
        # 调用统一接口
        return await unified_chat_completion(
            request=chat_request,
            credentials=credentials
        )
    except Exception as e:
        logging.error(f"Error in legacy endpoint: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 添加健康检查端点
@app.get("/health")
async def health_check():
    return {"status": "healthy"}

@app.get("/monitor")
async def gpu_monitor():
    """GPU监控页面"""
    import os
    file_path = "static/gpu-monitor.html"
    if not os.path.exists(file_path):
        raise HTTPException(status_code=404, detail=f"监控页面文件不存在: {file_path}")
    return FileResponse(file_path)

# 任务状态查询API
@app.get("/api/task/{task_id}/status")
async def get_task_status(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """查询任务状态"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 从统一任务管理器获取任务状态
    task_info = unified_task_manager.get_task_status(task_id)
    if not task_info:
        raise HTTPException(status_code=404, detail="Task not found")

    return task_info

@app.get("/monitor/api/status")
async def monitor_status():
    """合并的监控API - 包含状态和队列信息，减少请求频率"""
    try:
        # GPU状态信息 - 使用nvidia-smi获取详细信息
        gpu_info = []
        for gpu_id in range(unified_task_manager.gpu_count):
            try:
                # 使用精简的GPU信息获取
                gpu_data = get_gpu_info(gpu_id)
                status = unified_task_manager.gpu_status[gpu_id]

                # 添加任务信息
                gpu_info_item = {
                    "gpu_id": gpu_id,
                    "name": torch.cuda.get_device_name(gpu_id) if torch.cuda.is_available() else "Unknown",
                    "memory_total_mb": gpu_data.get('memory_total_mb', 11264),
                    "memory_used_mb": gpu_data.get('memory_used_mb', 0),
                    "memory_usage_percent": gpu_data.get('memory_utilization_percent', 0),
                    "utilization_percent": gpu_data.get('gpu_utilization_percent', 0),
                    "llm_chat_tasks": status.get('llm_chat_tasks', 0),
                    "llm_embedding_tasks": status.get('llm_embedding_tasks', 0),
                    "ocr_tasks": status.get('ocr_tasks', 0)
                }
                gpu_info.append(gpu_info_item)
            except Exception as e:
                gpu_info.append({
                    "gpu_id": gpu_id,
                    "name": "Unknown",
                    "error": str(e),
                    "allocated_memory_mb": 0,
                    "reserved_memory_mb": 0,
                    "memory_total_mb": 0,
                    "memory_used_mb": 0,
                    "memory_free_mb": 0,
                    "utilization_percent": 0,
                    "temperature_c": 0,
                    "power_draw_w": 0,
                    "llm_chat_tasks": 0,
                    "llm_embedding_tasks": 0,
                    "ocr_tasks": 0
                })

        queue_stats = get_route_stats()

        return {
            "status": "running",
            "timestamp": time.time(),
            "gpu_info": gpu_info,
            "queue_stats": queue_stats
        }
    except Exception as e:
        logging.error(f"监控API错误: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": time.time()
        }

# 新的轻量级监控接口，专为高频刷新优化
@app.get("/monitor/api/dashboard")
async def monitor_dashboard():
    """
    轻量级监控仪表板API - 专为高频刷新优化
    只返回关键指标，减少数据传输量和处理时间
    """
    try:
        # 只获取关键GPU指标
        gpu_summary = []
        for gpu_id in range(unified_task_manager.gpu_count):
            try:
                gpu_data = get_gpu_info(gpu_id)
                status = unified_task_manager.gpu_status[gpu_id]

                if not gpu_data:
                    # 如果无法获取GPU信息，使用默认值
                    gpu_data = {
                        'memory_total_mb': 11264,
                        'memory_used_mb': 0,
                        'memory_utilization_percent': 0,
                        'gpu_utilization_percent': 0
                    }

                gpu_summary.append({
                    "id": gpu_id,
                    "util": gpu_data.get('gpu_utilization_percent', 0),
                    "mem": gpu_data.get('memory_utilization_percent', 0),
                    "temp": gpu_data.get('temperature_c', 0),
                    "power": gpu_data.get('power_draw_w', 0),
                    "tasks": {
                        "chat": status.get('llm_chat_tasks', 0),
                        "embed": status.get('llm_embedding_tasks', 0),
                        "ocr": status.get('ocr_tasks', 0)
                    }
                })
            except Exception as e:
                gpu_summary.append({
                    "id": gpu_id,
                    "util": 0,
                    "mem": 0,
                    "temp": 0,
                    "power": 0,
                    "tasks": {"chat": 0, "embed": 0, "ocr": 0},
                    "error": str(e)
                })

        # 简化的任务统计
        unified_stats = unified_task_manager.get_task_stats()

        return {
            "ts": int(time.time()),  # 使用更短的字段名
            "gpus": gpu_summary,
            "tasks": {
                "total": unified_stats["total_tasks"],
                "active": unified_stats["processing_tasks"],
                "done": unified_stats["completed_tasks"],
                "failed": unified_stats["failed_tasks"]
            },
            "servers": len(SERVERS_CONFIG)
        }
    except Exception as e:
        logging.error(f"监控仪表板API错误: {str(e)}")
        return {
            "ts": int(time.time()),
            "error": str(e)
        }


@app.get("/ocr/status/{task_id}")
async def get_task_status(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """查询任务状态"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 注意：TaskRouter.task_results已被移除，因为从未被使用
    # 所有任务结果都通过UnifiedTaskManager管理

    # 检查传统任务结果（向后兼容）
    if task_id in task_results:
        result = task_results[task_id]
        return {
            "task_id": task_id,
            "status": result.status,
            "progress": result.progress,
            "result": result.result,
            "error": result.error
        }

    # 任务不存在
    raise HTTPException(status_code=404, detail="Task not found")

@app.get("/queue/stats")
async def get_queue_stats(
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """获取任务队列统计信息"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    return get_route_stats()

# 优化：添加系统状态监控端点
@app.get("/api/status")
async def system_status(credentials: HTTPAuthorizationCredentials = Depends(bearer)):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    # 服务器状态
    servers_status = {}
    for name, server in SERVERS_CONFIG.items():
        servers_status[name] = {
            "url": server.url,
            "is_healthy": True,  # 健康检查已删除，默认为健康
            "chat_healthy": True,  # 健康检查已删除，默认为健康
            "embeddings_healthy": True,  # 健康检查已删除，默认为健康
            "chat_processing": server.chat_processing,
            "embeddings_processing": server.embeddings_processing,
            "chat_queue_size": server.chat_queue.qsize(),
            "embeddings_queue_size": server.embeddings_queue.qsize(),
            "max_queue_size": server.max_queue_size,
            "max_concurrent_chat": server.max_concurrent_chat,
            "max_concurrent_embeddings": server.max_concurrent_embeddings,
            "avg_response_time": round(server.avg_response_time, 3),
            "total_requests": server.total_requests,
            "chat_errors": server.chat_errors,
            "embeddings_errors": server.embeddings_errors,
            "chat_load": server.chat_processing + server.chat_queue.qsize(),
            "embeddings_load": server.embeddings_processing + server.embeddings_queue.qsize()
        }

    # GPU状态 - 优化为监控页面格式
    gpu_info = []
    for gpu_id in range(unified_task_manager.gpu_count):
        try:
            gpu_data_info = get_gpu_info(gpu_id)
            if gpu_data_info:
                allocated = gpu_data_info.get('memory_used_mb', 0)
                reserved = gpu_data_info.get('memory_total_mb', 11264)
            else:
                # 使用默认值
                allocated = 0
                reserved = 11264
            status = unified_task_manager.gpu_status[gpu_id]

            gpu_data = {
                "gpu_id": gpu_id,
                "name": torch.cuda.get_device_name(gpu_id) if torch.cuda.is_available() else "Unknown",
                "allocated_memory_mb": allocated,
                "reserved_memory_mb": reserved,
                "free_memory_mb": reserved - allocated,
                "memory_usage_percent": (allocated / max(reserved, 1)) * 100,
                "utilization": f"{(allocated / max(reserved, 1)) * 100:.1f}%" if reserved > 0 else "0%",
                "llm_chat_tasks": status.get('llm_chat_tasks', 0),
                "llm_embedding_tasks": status.get('llm_embedding_tasks', 0),
                "ocr_tasks": status.get('ocr_tasks', 0)
            }
            gpu_info.append(gpu_data)
        except Exception as e:
            gpu_info.append({
                "gpu_id": gpu_id,
                "name": "Unknown",
                "error": str(e),
                "allocated_memory_mb": 0,
                "reserved_memory_mb": 0,
                "llm_tasks": 0,
                "ocr_tasks": 0,
                "layout_tasks": 0
            })

    # 任务统计
    task_stats = {
        "total_tasks": len(task_results),
        "processing_tasks": len([t for t in task_results.values() if t.status == "processing"]),
        "completed_tasks": len([t for t in task_results.values() if t.status == "completed"]),
        "failed_tasks": len([t for t in task_results.values() if t.status == "failed"])
    }

    # Ollama服务器状态（健康检查已删除）
    ollama_servers = []
    for name, server in SERVERS_CONFIG.items():
        ollama_servers.append({
            "name": name,
            "url": server.url,
            "healthy": True,  # 健康检查已删除，默认为健康
            "chat_healthy": True,  # 健康检查已删除，默认为健康
            "embeddings_healthy": True  # 健康检查已删除，默认为健康
        })

    # 集成性能监控数据
    performance_summary = unified_task_manager.get_performance_summary()

    return {
        "status": "running",
        "timestamp": time.time(),
        "gpu_info": gpu_info,
        "task_stats": task_stats,
        "ollama_servers": ollama_servers,
        "servers": servers_status,
        "total_servers": len(SERVERS_CONFIG),
        "healthy_servers": len(SERVERS_CONFIG),  # 健康检查已删除，所有服务器默认健康
        "total_gpus": unified_task_manager.gpu_count,
        "busy_gpus": sum(1 for status in unified_task_manager.gpu_status.values()
                       if status['llm_chat_tasks'] + status['llm_embedding_tasks'] + status['ocr_tasks'] > 0),
        # 新增性能监控数据
        "performance": performance_summary
    }

# 新增专用的性能监控API
@app.get("/api/performance")
async def get_performance_metrics(credentials: HTTPAuthorizationCredentials = Depends(bearer)):
    """获取详细的性能监控指标"""
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    return unified_task_manager.get_performance_summary()

# 添加重新加载用户列表的端点
@app.post("/api/admin/reload-users")
async def reload_users(
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token != "admin_key":
        raise HTTPException(
            status_code=403,
            detail="Only admin can reload users"
        )
    
    try:
        global authorized_users
        authorized_users = await get_authorized_users("authorized_users.txt")
        logging.info("User list reloaded successfully")
        return {"status": "success", "message": "User list reloaded"}
    except Exception as e:
        logging.error(f"Failed to reload users: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


# 修改 Base64Request 模型增加文件名和页码字段
class Base64Request(BaseModel):
    """Base64 OCR 请求模型"""
    image: str
    file_name: Optional[str] = ""
    page_no: Optional[int] = 1
    wait: bool = False
    timeout: float = 30.0
    test_mode: Optional[bool] = False
    enable_seal_hw: bool = False

    try:
        # pydantic v2
        @validator('timeout')
        def validate_timeout(cls, v):
            if v <= 0:
                raise ValueError("Timeout must be positive")
            return v
    except NameError:
        # pydantic v1
        @validator('timeout')
        def validate_timeout(cls, v):
            if v <= 0:
                raise ValueError("Timeout must be positive")
            return v

# 添加日期提取函数
def extract_dates(text: str) -> List[str]:
    """从文本中提取日期"""
    # 匹配常见的日期格式
    date_patterns = [
        r'(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})[日]?',  # 2019年7月1日 或 2019-07-01
        r'(\d{4})\.(\d{1,2})\.(\d{1,2})',              # 2019.7.1
    ]
    
    dates = []
    for pattern in date_patterns:
        matches = re.finditer(pattern, text)
        for match in matches:
            year, month, day = match.groups()
            # 格式化日期
            try:
                date_str = f"{int(year):04d}-{int(month):02d}-{int(day):02d} 00:00:00"
                dates.append(date_str)
            except ValueError:
                continue
    
    return sorted(list(set(dates)))  # 去重并排序






# 创建全局任务管理器实例
unified_task_manager = UnifiedTaskManager()

# 保持向后兼容的全局变量
ocr_tasks: Dict[str, asyncio.Task] = {}
task_results: Dict[str, TaskResult] = {}  # 修改这里不使用 defaultdict

# 添加一个资源锁
ocr_lock = Lock()




# 修改 OCR 文件上传接口
@app.post("/ocr/file/")
async def ocr_file(
    file: UploadFile,
    wait: str = Form(default="false", pattern="^(true|false)$"),
    timeout: str = Form(default="30.0", pattern="^(?:[0-9]*[.])?[0-9]+$"),
    enable_seal_hw: str = Form(default="false", pattern="^(true|false)$"),
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    logging.info(f"📥📥📥 [API] 接收到OCR-file请求 📥📥📥")
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        logging.info(f"[OCR] Processing file upload: {file.filename}")
        if not file.filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            raise HTTPException(status_code=400, detail="Unsupported file format")
        
        # 验证和转换参
        try:
            wait_bool = wait.lower() == "true"
            timeout_float = float(timeout)        
            if timeout_float <= 0:
                raise HTTPException(
                    status_code=422,
                    detail="Timeout must be positive"
                )
        except ValueError:
            raise HTTPException(
                status_code=422,
                detail="Invalid parameter format"
            )
        
        # 读取件内容
        contents = await file.read()
        logging.info(f"Read file content length: {len(contents)}")
        
        try:
            img = process_image_pil(contents)
            logging.info(f"Successfully processed image: {img.shape}")
        except Exception as e:
            logging.error(f"Failed to process image: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Invalid image data: {str(e)}")
        
        # 准备任务数据
        task_data = {
            'image': img,
            'timeout': timeout_float,
            'enable_seal_hw': enable_seal_hw.lower() == "true"
        }

        # 使用统一任务管理器创建任务
        task_id = await unified_task_manager.create_task('ocr', task_data)
        logging.info(f"Created OCR task ID: {task_id}")
        
        if wait_bool:
            # 同步处理 - 等待任务完成
            result = await unified_task_manager.get_task_result(task_id, timeout_float)

            if result and result.status == "completed":
                logging.info(f"✅ 任务 {task_id} 完成")
                return JSONResponse(content=result.result)
            elif result and result.status == "timeout":
                logging.warning(f"⏰ 任务 {task_id} 超时")
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "timeout",
                        "message": result.error,
                        "progress": 100
                    },
                    status_code=408
                )
            else:
                error_msg = result.error if result else "任务未找到"
                logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "failed",
                        "message": error_msg,
                        "progress": 100
                    },
                    status_code=500
                )
        else:
            # 异步处理 - 立即返回任务ID
            return JSONResponse(
                content={
                    "task_id": task_id,
                    "status": "processing",
                    "message": "任务已提交到队列，请使用/ocr/status/{task_id}查询结果",
                    "progress": 0
                },
                status_code=202
            )
            
    except Exception as e:
        logging.error(f"[OCR] Error in ocr_file: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# 修改 ocr_base64 接口使用统一任务管理器
@app.post("/ocr/base64/")
async def ocr_base64(
    request: Base64Request,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials

    # 显示接收到的OCR请求日志
    logging.info(f"📥📥📥 [API] 接收到OCR-Base64请求 📥📥📥")
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        logging.info("[OCR] Processing base64 request")
        img = process_image_pil(request.image)

        # 准备任务数据
        task_data = {
            'image': img,
            'timeout': request.timeout,
            'enable_seal_hw': request.enable_seal_hw,
            'file_name': request.file_name,
            'page_no': request.page_no,
            'test_mode': request.test_mode
        }

        # 使用统一任务管理器创建任务
        task_id = await unified_task_manager.create_task('ocr', task_data)
        logging.info(f"Created OCR base64 task ID: {task_id}")

        if request.wait:
            try:
                # 同步处理 - 等待任务完成
                result = await unified_task_manager.get_task_result(task_id, request.timeout)

                if result and result.status == "completed":
                    logging.info(f"✅ 任务 {task_id} 完成")
                    return JSONResponse(content=result.result)
                elif result and result.status == "timeout":
                    logging.warning(f"⏰ 任务 {task_id} 超时")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "timeout",
                            "message": result.error,
                            "progress": 100
                        },
                        status_code=408
                    )
                else:
                    error_msg = result.error if result else "任务未找到"
                    logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                    return JSONResponse(
                        content={
                            "task_id": task_id,
                            "status": "failed",
                            "message": error_msg,
                            "progress": 100
                        },
                        status_code=500
                    )

            except Exception as e:
                logging.error(f"[OCR] Error waiting for task {task_id}: {str(e)}")
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "failed",
                        "message": f"Task execution error: {str(e)}",
                        "progress": 100
                    },
                    status_code=500
                )
        else:
            return JSONResponse(
                content={
                    "task_id": task_id,
                    "status": "processing",
                    "message": "OCR processing started",
                    "progress": 0
                },
                status_code=202
            )

    except Exception as e:
        logging.error(f"[OCR] Error in ocr_base64: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ocr/binary/")
async def ocr_binary(
    image_data: bytes = Body(..., media_type="application/octet-stream"),
    wait: bool = Query(default=False),
    timeout: float = Query(default=30.0, gt=0),
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    logging.info(f"📥📥📥 [API] 接收到OCR-binary请求 📥📥📥")
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    try:
        logging.info(f"[OCR] Processing binary request content length:{len(image_data)}")
        img = process_image_pil(image_data)
        
        # 准备任务数据
        task_data = {
            'image': img,
            'timeout': timeout,
            'enable_seal_hw': False
        }

        # 使用统一任务管理器创建任务
        task_id = await unified_task_manager.create_task('ocr', task_data)
        logging.info(f"Created OCR binary task ID: {task_id}")
        
        if wait:
            # 同步处理 - 等待任务完成
            result = await unified_task_manager.get_task_result(task_id, timeout)

            if result and result.status == "completed":
                logging.info(f"✅ 任务 {task_id} 完成")
                return JSONResponse(content=result.result)
            elif result and result.status == "timeout":
                logging.warning(f"⏰ 任务 {task_id} 超时")
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "timeout",
                        "message": result.error,
                        "progress": 100
                    },
                    status_code=408
                )
            else:
                error_msg = result.error if result else "任务未找到"
                logging.error(f"❌ 任务 {task_id} 失败: {error_msg}")
                return JSONResponse(
                    content={
                        "task_id": task_id,
                        "status": "failed",
                        "message": error_msg,
                        "progress": 100
                    },
                    status_code=500
                )
        else:
            return JSONResponse(
                content={
                    "task_id": task_id,
                    "status": "processing",
                    "message": "OCR processing started",
                    "progress": 0
                },
                status_code=202
            )
            
    except Exception as e:
        logging.error(f"[OCR] Error in ocr_binary: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/ocr/result/{task_id}")
async def get_ocr_result(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")

    try:
        # 首先检查统一任务管理器中的任务
        if task_id in unified_task_manager.task_results:
            result = unified_task_manager.task_results[task_id]

            if result.status == "completed":
                return JSONResponse(content=result.result)
            elif result.status == "failed":
                return JSONResponse(
                    content={
                        "status": "failed",
                        "message": result.error,
                        "progress": result.progress
                    },
                    status_code=500
                )
            elif result.status == "timeout":
                return JSONResponse(
                    content={
                        "status": "timeout",
                        "message": result.error,
                        "progress": result.progress
                    },
                    status_code=408
                )
            else:  # processing
                return JSONResponse(
                    content={
                        "status": "processing",
                        "message": "OCR still processing",
                        "progress": result.progress
                    },
                    status_code=202
                )

        # 向后兼容：检查旧的任务管理方式
        if task_id in ocr_tasks:
            task = ocr_tasks[task_id]
            result = task_results.get(task_id)

            if not result:
                return JSONResponse(
                    content={
                        "status": "not_found",
                        "message": "Task result not found"
                    },
                    status_code=404
                )

            if task.done():
                # 清理任务
                ocr_tasks.pop(task_id, None)
                result = task_results.pop(task_id, None)
                return JSONResponse(content=asdict(result))
            else:
                return JSONResponse(
                    content={
                        "status": "processing",
                        "message": "OCR still processing",
                        "progress": result.progress
                    },
                    status_code=202
                )

        # 任务不存在
        return JSONResponse(
            content={
                "status": "not_found",
                "message": "Task not found"
            },
            status_code=404
        )

    except Exception as e:
        logging.error(f"Error getting OCR result: {str(e)}")
        return JSONResponse(
            content={
                "status": "error",
                "message": str(e)
            },
            status_code=500
        )

# 修改 Swagger UI 相关的配置和路由
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义 Swagger UI 页面"""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        swagger_js_url="/static/swagger-ui/swagger-ui-bundle.js",  # 修改路径
        swagger_css_url="/static/swagger-ui/swagger-ui.css",      # 修改路
        swagger_favicon_url="/static/favicon.ico",
        swagger_ui_parameters={
            "defaultModelsExpandDepth": -1,  # 隐 Models 部分
            "deepLinking": True,  # 启用深度链接
            "displayRequestDuration": True,  # 显示请求持续时间
            "filter": True,  # 启用过滤功能
            "tryItOutEnabled": True  # 启用 "Try it out" 功能
        },
        init_oauth={
            "clientId": "your-client-id",
            "clientSecret": "your-client-secret",
            "realm": "your-realms",
            "appName": "your-app-name",
            "scopeSeparator": " ",
            "scopes": "openid profile email",
            "additionalQueryStringParams": {}
        }
    )

def process_image_pil(image_data: Union[bytes, str]) -> np.ndarray:
    """处理图像数据，支持base64和二进制格式"""
    try:
        # 处理base64数据
        if isinstance(image_data, str):
            try:
                if ',' in image_data:
                    image_data = image_data.split(',', 1)[1]
                image_data = base64.b64decode(image_data.strip())
                logging.info(f"Base64 decoded length: {len(image_data)}")
            except Exception as e:
                raise ValueError(f"Invalid base64 data: {str(e)}")
        
        if not isinstance(image_data, bytes):
            raise ValueError(f"Expected bytes or base64 string, got {type(image_data)}")
        
        # 转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        if len(nparr) == 0:
            raise ValueError("Empty image data")
            
        # 使用OpenCV解码图像
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        if img is None:
            raise ValueError("Failed to decode image")
            
        logging.info(f"Original image shape: {img.shape}")
        
        # 基本格式转换
        if len(img.shape) == 2:  # 灰度图像
            img = cv2.cvtColor(img, cv2.COLOR_GRAY2BGR)
            logging.info("Converted grayscale to BGR")
        elif img.shape[2] == 4:  # RGBA图像
            # 使用白色背景进行alpha通道混合
            alpha = img[:, :, 3] / 255.0
            white_background = np.ones_like(img[:, :, :3]) * 255
            img_rgb = img[:, :, :3]
            img = (alpha[:, :, np.newaxis] * img_rgb + (1 - alpha[:, :, np.newaxis]) * white_background).astype(np.uint8)
            logging.info("Converted RGBA to BGR with white background")
        
        # 调整图像大小到960x960，保持宽高比
        height, width = img.shape[:2]
        target_size = 960
        
        # 计算缩放比例
        scale = min(target_size / width, target_size / height)
        new_width = int(width * scale)
        new_height = int(height * scale)
        
        # 使用INTER_AREA进行缩小，INTER_LINEAR进行放大
        interpolation = cv2.INTER_AREA if scale < 1 else cv2.INTER_LINEAR
        img = cv2.resize(img, (new_width, new_height), interpolation=interpolation)
        
        # 创建960x960的白色背景
        square_img = np.full((target_size, target_size, 3), 255, dtype=np.uint8)
        
        # 将调整后的图像放在中心位置
        y_offset = (target_size - new_height) // 2
        x_offset = (target_size - new_width) // 2
        square_img[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = img
        
        logging.info(f"Resized image shape: {square_img.shape}")
        
        # 检查图像是否为空或全黑
        if square_img.size == 0 or np.mean(square_img) < 5:
            raise ValueError("Image is empty or completely black")
            
        # 返回RGB格式的图像
        return cv2.cvtColor(square_img, cv2.COLOR_BGR2RGB)
        
    except Exception as e:
        logging.error(f"Error in process_image_pil: {str(e)}")
        raise ValueError(f"Image processing failed: {str(e)}")



# 添加重试装饰器函数
async def retry_with_backoff_stream(generator_func, max_retries=3, initial_delay=1):
    """
    流式请求的重试装饰器
    """
    last_error = None
    for attempt in range(max_retries):
        try:
            async for chunk in generator_func():
                yield chunk
            return
        except httpx.ConnectError as e:
            last_error = e
            if attempt == max_retries - 1:
                break
            delay = initial_delay * (2 ** attempt)
            logging.warning(f"Connection failed (attempt {attempt + 1}/{max_retries}), retrying in {delay}s...")
            await asyncio.sleep(delay)
            continue
        except Exception as e:
            logging.error(f"Unexpected error in retry_with_backoff_stream: {str(e)}")
            raise

    if last_error:
        error_response = {
            "error": f"Failed to connect after {max_retries} attempts: {str(last_error)}"
        }
        yield f"data: {json.dumps(error_response)}\n\n"

#  stream_chat_completion 函数中的错误处理
async def stream_chat_completion(request: dict, server_info: dict):
    """Stream chat completion responses"""
    async def make_request():
        try:
            # 提取GPU ID并显示清晰的流式请求日志
            try:
                server_url = server_info['url']
                gpu_id = int(server_url.split(':')[-1]) - 11434
                logging.info(f"🌊🌊🌊 [LLM-STREAM] 开始流式请求 → GPU{gpu_id} 🌊🌊🌊")
            except Exception as e:
                logging.info(f"🌊🌊🌊 [LLM-STREAM] 开始流式请求 → {server_info['url']} 🌊🌊🌊")
                logging.debug(f"GPU ID提取失败: {e}")

            logging.debug(f"请求内容: {json.dumps(request)}")
            
            # 设置更长的超时时间，特别是读取超时
            timeout_config = httpx.Timeout(
                connect=10.0,  # 连接超时
                read=120.0,    # 读取超时（增加到2分钟）
                write=30.0,    # 写入超时
                pool=30.0      # 连接池超时
            )

            async with httpx.AsyncClient(timeout=timeout_config) as client:
                async with client.stream(
                    'POST',
                    f"{server_info['url']}/v1/chat/completions",
                    json=request
                ) as response:
                    logging.info(f"流式请求获得响应: {response.status_code}")
                    
                    # 如果响应不成功，记录详细错误信息
                    if response.status_code != 200:
                        error_body = await response.aread()
                        error_text = error_body.decode('utf-8', errors='replace')
                        logging.error(f"流式请求失败: 状态码 {response.status_code}, 错误: {error_text}")
                        yield f"data: {{\"error\": \"服务器返回错误: {response.status_code}\", \"details\": \"{error_text}\"}}\n\n"
                        return
                    
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.strip():
                            try:
                                # 检查是否已经是 SSE 格式
                                if line.startswith('data: '):
                                    yield line + '\n\n'
                                    continue
                                    
                                chunk = json.loads(line)
                                logging.debug(f"Received chunk: {chunk}")  # 添加调试日志
                                
                                # 转换为 SSE 格式
                                if chunk.get("response"):
                                    # 标准格式
                                    yield f"data: {json.dumps(chunk)}\n\n"
                                    if chunk.get('done', False):
                                        yield "data: [DONE]\n\n"
                                elif chunk.get("choices"):
                                    # OpenAI 格式
                                    yield f"data: {json.dumps(chunk)}\n\n"
                                    if chunk['choices'][0].get('finish_reason'):
                                        yield "data: [DONE]\n\n"
                            except json.JSONDecodeError as e:
                                logging.error(f"Failed to parse streaming response: {line}, error: {str(e)}")
                                continue
        except httpx.ReadTimeout as e:
            error_message = f"流式请求读取超时: 服务器响应时间过长，请稍后重试"
            logging.error(f"流式请求ReadTimeout: {str(e)}")
            yield f"data: {{\"error\": \"{error_message}\", \"type\": \"timeout\"}}\n\n"
        except httpx.ConnectTimeout as e:
            error_message = f"流式请求连接超时: 无法连接到服务器，请检查网络"
            logging.error(f"流式请求ConnectTimeout: {str(e)}")
            yield f"data: {{\"error\": \"{error_message}\", \"type\": \"connection_timeout\"}}\n\n"
        except httpx.TimeoutException as e:
            error_message = f"流式请求超时: {str(e)}"
            logging.error(f"流式请求TimeoutException: {str(e)}")
            yield f"data: {{\"error\": \"{error_message}\", \"type\": \"timeout\"}}\n\n"
        except Exception as e:
            error_message = f"流式请求异常: {str(e)}, 类型: {type(e).__name__}"
            logging.error(error_message)
            yield f"data: {{\"error\": \"{error_message}\", \"type\": \"unknown\"}}\n\n"
    
    try:
        async for chunk in retry_with_backoff_stream(make_request):
            yield chunk
    except Exception as e:
        error_message = f"流式响应处理异常: {str(e)}, 类型: {type(e).__name__}"
        logging.error(error_message)
        yield f"data: {{\"error\": \"{error_message}\"}}\n\n"

# 添加一个关闭函数
def shutdown():
    logging.info("Shutting down server...")
    # 发送 SIGTERM 信号给当前进程
    pid = os.getpid()
    if sys.platform == "win32":
        # Windows 平台使用 taskkill
        os.system(f"taskkill /F /PID {pid}")
    else:
        # Unix 平台使用 kill
        os.kill(pid, signal.SIGTERM)

# 添加关闭接口
@app.post("/shutdown")
async def shutdown_server(
    request: ShutdownRequest,
    background_tasks: BackgroundTasks,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    token = credentials.credentials
    if token != "hngpt_admin@8888":
        raise HTTPException(
            status_code=403,
            detail="Only admin can shutdown the server"
        )
    
    if not request.confirm:
        raise HTTPException(
            status_code=400,
            detail="Shutdown requires confirmation"
        )
    
    logging.warning("Server shutdown initiated by admin")
    background_tasks.add_task(shutdown)
    return {"message": "Server is shutting down..."}

# 添加识别请求模型
class RecognizeRequest(BaseModel):
    image: str  # base64编码的图像

# 添加识别接口
@app.post("/api/recognize")
async def recognize_image(
    request: RecognizeRequest,
    credentials: HTTPAuthorizationCredentials = Depends(bearer)
):
    """
    识别图像中的印章和手写文字
    """
    token = credentials.credentials
    if token not in authorized_users:
        raise HTTPException(status_code=403, detail="Not authorized")
    
    try:
        # 解码base64图像
        image_data = base64.b64decode(request.image)
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        if img is None:
            raise HTTPException(status_code=400, detail="Invalid image data")
            
        # 执行识别
        seals, handwritings = recognizer.recognize(img)
        
        return {
            "status": "success",
            "seals": seals,
            "handwritings": handwritings
        }
        
    except Exception as e:
        logging.error(f"Recognition error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# ============================================================================
# 精简的GPU信息获取函数 - 只使用nvidia-smi
# ============================================================================

def get_gpu_count():
    """获取GPU数量"""
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi', '--list-gpus'],
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            return len(result.stdout.strip().split('\n'))
        else:
            # 回退到torch方法
            return torch.cuda.device_count() if torch.cuda.is_available() else 0
    except:
        return torch.cuda.device_count() if torch.cuda.is_available() else 0

def get_gpu_info(gpu_id=None):
    """使用nvidia-smi获取GPU信息，包含显卡名称

    Args:
        gpu_id: 指定GPU ID，如果为None则获取所有GPU信息

    Returns:
        dict: GPU信息，包含数量、显存利用率、GPU利用率、显卡名称等
    """
    try:
        import subprocess
        import re

        # 首先尝试使用query命令获取详细信息（包括GPU名称）
        try:
            query_result = subprocess.run([
                'nvidia-smi',
                '--query-gpu=index,name,memory.total,memory.used,utilization.gpu,temperature.gpu,power.draw,power.limit',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=10)

            if query_result.returncode == 0 and query_result.stdout.strip():
                gpu_info = {}
                for line in query_result.stdout.strip().split('\n'):
                    if line.strip():
                        parts = [p.strip() for p in line.split(',')]
                        if len(parts) >= 5:
                            try:
                                gpu_idx = int(parts[0])
                                gpu_name = parts[1]
                                memory_total = float(parts[2])
                                memory_used = float(parts[3])
                                gpu_util = float(parts[4]) if parts[4] != 'N/A' else 0

                                # 可选的温度和功耗信息
                                temperature = float(parts[5]) if len(parts) > 5 and parts[5] != 'N/A' else None
                                power_draw = float(parts[6]) if len(parts) > 6 and parts[6] != 'N/A' else None
                                power_limit = float(parts[7]) if len(parts) > 7 and parts[7] != 'N/A' else None

                                # 计算显存利用率
                                memory_util = (memory_used / memory_total * 100) if memory_total > 0 else 0

                                gpu_info[gpu_idx] = {
                                    'name': gpu_name,
                                    'memory_total_mb': memory_total,
                                    'memory_used_mb': memory_used,
                                    'memory_utilization_percent': memory_util,
                                    'gpu_utilization_percent': gpu_util,
                                    'temperature_c': temperature,
                                    'power_draw_w': power_draw,
                                    'power_limit_w': power_limit
                                }
                            except (ValueError, IndexError):
                                continue

                # 如果成功获取到信息，直接返回
                if gpu_info:
                    if gpu_id is not None:
                        return gpu_info.get(gpu_id)
                    else:
                        return {
                            'gpu_count': len(gpu_info),
                            'gpus': gpu_info
                        }
        except Exception as e:
            logging.debug(f"nvidia-smi query命令失败: {e}")

        # 回退到解析标准nvidia-smi输出
        result = subprocess.run(['nvidia-smi'], capture_output=True, text=True, timeout=10)

        if result.returncode == 0 and result.stdout.strip():
            lines = result.stdout.strip().split('\n')
            gpu_info = {}

            # 解析标准nvidia-smi输出格式
            for i, line in enumerate(lines):
                # 匹配GPU信息行，支持更多GPU品牌
                if '|' in line and ('NVIDIA' in line or 'GeForce' in line or 'RTX' in line or 'GTX' in line):
                    # GPU名称行，格式: |   0  NVIDIA GeForce RTX 2080 Ti     On  |
                    gpu_match = re.search(r'\|\s*(\d+)\s+(.+?)\s+(?:On|Off)', line)
                    if gpu_match:
                        gpu_idx = int(gpu_match.group(1))
                        gpu_name = gpu_match.group(2).strip()

                        # 查找下一行的内存和利用率信息
                        if i + 1 < len(lines):
                            next_line = lines[i + 1]
                            if '|' in next_line and 'MiB' in next_line and '%' in next_line:
                                # 解析内存和利用率
                                # 格式: |  0%   40C    P8             39W /  250W |    7921MiB /  22528MiB |      0%      Default |
                                memory_match = re.search(r'(\d+)MiB\s*/\s*(\d+)MiB', next_line)
                                # GPU利用率在最后一个 | 之前
                                util_match = re.search(r'\|\s*(\d+)%\s+Default', next_line)

                                if memory_match:
                                    memory_used = float(memory_match.group(1))
                                    memory_total = float(memory_match.group(2))
                                    gpu_util = float(util_match.group(1)) if util_match else 0

                                    memory_util = (memory_used / memory_total * 100) if memory_total > 0 else 0

                                    gpu_info[gpu_idx] = {
                                        'name': gpu_name,
                                        'memory_total_mb': memory_total,
                                        'memory_used_mb': memory_used,
                                        'memory_utilization_percent': memory_util,
                                        'gpu_utilization_percent': gpu_util
                                    }

            # 返回解析结果
            if gpu_info:
                if gpu_id is not None:
                    return gpu_info.get(gpu_id)
                else:
                    return {
                        'gpu_count': len(gpu_info),
                        'gpus': gpu_info
                    }


        # nvidia-smi失败
        logging.error("nvidia-smi解析失败，无法获取GPU信息")
        return None

    except Exception as e:
        logging.error(f"获取GPU信息失败: {str(e)}")
        return None



def check_cuda_environment():
    """简化的CUDA环境检查"""
    cuda_available = torch.cuda.is_available()
    gpu_count = get_gpu_count()

    logging.info(f"CUDA available: {cuda_available}")
    logging.info(f"GPU count: {gpu_count}")

    return cuda_available

if __name__ == "__main__":
    setup_logging("log")
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8888)
