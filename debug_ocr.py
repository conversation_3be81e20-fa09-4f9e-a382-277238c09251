#!/usr/bin/env python3
"""
调试OCR功能
"""

import requests
import base64
import json
import time

def test_ocr_debug():
    """调试OCR功能"""
    print("🔍 调试OCR功能")
    
    # 读取测试图片
    test_image_path = "/workspace/hngpt/tests/document.png"
    
    try:
        with open(test_image_path, "rb") as image_file:
            img_data = image_file.read()
            img_base64 = base64.b64encode(img_data).decode('utf-8')
        
        print(f"📄 测试图片: {test_image_path}")
        print(f"📏 图片大小: {len(img_data)} 字节")
        print(f"📏 Base64长度: {len(img_base64)} 字符")
        
        # 测试请求数据
        payload = {
            "image": img_base64
        }
        
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/json"
        }
        
        print(f"\n🚀 发送OCR请求...")
        start_time = time.time()
        
        # 发送请求
        response = requests.post(
            "http://localhost:8888/ocr",
            json=payload,
            headers=headers,
            timeout=60
        )
        
        response_time = time.time() - start_time
        
        print(f"📊 响应状态码: {response.status_code}")
        print(f"⏱️  响应时间: {response_time:.3f}s")
        print(f"📋 响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ OCR成功!")
                print(f"📝 结果类型: {type(result)}")
                if isinstance(result, dict):
                    print(f"📝 结果键: {list(result.keys())}")
                print(f"📝 完整结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析失败: {e}")
                print(f"📄 原始响应: {response.text[:500]}...")
        else:
            print(f"❌ OCR失败!")
            print(f"📄 错误响应: {response.text}")
            
        # 测试其他OCR端点
        print(f"\n🔍 测试其他OCR端点...")
        
        # 测试 /ocr/base64/
        print(f"测试 /ocr/base64/...")
        response2 = requests.post(
            "http://localhost:8888/ocr/base64/",
            json=payload,
            headers=headers,
            timeout=60
        )
        print(f"  /ocr/base64/ 状态码: {response2.status_code}")
        
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()

def test_server_connection():
    """测试服务器连接"""
    print("🔗 测试服务器连接...")
    
    try:
        # 测试根路径
        response = requests.get("http://localhost:8888/", timeout=5)
        print(f"  根路径状态码: {response.status_code}")
        
        # 测试健康检查（如果有的话）
        try:
            response = requests.get("http://localhost:8888/health", timeout=5)
            print(f"  健康检查状态码: {response.status_code}")
        except:
            print("  健康检查端点不存在")
            
        # 测试Chat端点
        chat_payload = {
            "model": "hngpt",
            "messages": [{"role": "user", "content": "Hello"}],
            "stream": False
        }
        headers = {
            "Authorization": "Bearer startfrom2023",
            "Content-Type": "application/json"
        }
        
        response = requests.post(
            "http://localhost:8888/v1/chat/completions",
            json=chat_payload,
            headers=headers,
            timeout=10
        )
        print(f"  Chat端点状态码: {response.status_code}")
        
        # 测试Embedding端点
        embedding_payload = {
            "model": "hngpt-embedding",
            "input": "test"
        }
        
        response = requests.post(
            "http://localhost:8888/v1/embeddings",
            json=embedding_payload,
            headers=headers,
            timeout=10
        )
        print(f"  Embedding端点状态码: {response.status_code}")
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")

if __name__ == "__main__":
    test_server_connection()
    print("\n" + "="*60 + "\n")
    test_ocr_debug()
