# 架构重构计划

## 🔍 当前问题分析

### 现有类的职责混乱
1. **UnifiedServerManager** - 职责过多
   - Ollama服务器连接管理
   - 队列和并发控制  
   - 任务生命周期管理
   - 性能统计
   - GPU任务计数

2. **SimpleTaskManager** - 职责不清
   - 只处理OCR任务执行
   - 被当作"统一任务管理器"使用
   - 与UnifiedServerManager功能重叠

3. **GPUResourceManager** - 功能重叠
   - GPU资源分配
   - 与UnifiedServerManager的GPU计数重复

4. **TaskRouter** - 未充分利用
   - 有全局队列但很多任务绕过它
   - GPU工作器没有真正发挥作用

## 🎯 重构目标：清晰的单一职责架构

### 新架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    RequestHandler                           │
│                 (FastAPI路由层)                              │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                LoadBalancer                                 │
│              (负载均衡和服务器选择)                            │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│            ConcurrencyController                            │
│              (全局并发控制和队列管理)                          │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              TaskExecutor                                   │
│            (实际任务执行和GPU调用)                            │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              GPUResourceManager                             │
│              (GPU资源分配和监控)                              │
└─────────────────────────────────────────────────────────────┘
```

### 各组件职责

1. **OllamaServer** - 纯粹的服务器连接
   - 管理单个Ollama服务器连接
   - 健康检查
   - 连接状态维护

2. **LoadBalancer** - 负载均衡
   - 服务器选择算法
   - 负载评分计算
   - 请求分发决策

3. **ConcurrencyController** - 并发控制
   - 全局并发限制
   - 队列管理
   - 槽位分配/释放

4. **TaskExecutor** - 任务执行
   - 实际调用Ollama API
   - 任务生命周期管理
   - 结果处理

5. **GPUResourceManager** - GPU资源管理
   - GPU状态监控
   - 资源分配
   - 内存管理

6. **MetricsCollector** - 性能监控
   - 响应时间统计
   - 成功率统计
   - GPU使用率监控

## 🚀 重构步骤

### 阶段1：创建新的清晰组件
- [x] 定义OllamaServer类
- [x] 定义LoadBalancer类  
- [x] 定义ConcurrencyController类
- [ ] 定义TaskExecutor类
- [ ] 定义MetricsCollector类

### 阶段2：逐步替换旧组件
- [ ] 替换服务器管理逻辑
- [ ] 替换负载均衡逻辑
- [ ] 替换并发控制逻辑
- [ ] 替换任务执行逻辑

### 阶段3：清理和优化
- [ ] 删除旧的混乱组件
- [ ] 优化性能
- [ ] 更新测试脚本
- [ ] 更新文档

## 💡 重构优势

1. **单一职责** - 每个类只负责一件事
2. **清晰依赖** - 组件间依赖关系明确
3. **易于测试** - 每个组件可独立测试
4. **易于扩展** - 新功能容易添加
5. **易于维护** - 问题定位更容易

## 🔧 兼容性考虑

- 保持现有API接口不变
- 逐步迁移，确保系统稳定运行
- 保留现有配置和监控接口
- 确保测试脚本继续工作

## 📊 预期效果

- 代码更清晰易懂
- 性能监控更准确
- 负载均衡更智能
- 并发控制更精确
- 问题排查更容易
