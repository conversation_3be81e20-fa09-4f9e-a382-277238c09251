# 统一GPU负载均衡器使用指南

## 🎯 概述

新的`UnifiedGPULoadBalancer`类解决了之前架构混乱的问题，提供了一个清晰、统一的GPU负载均衡解决方案。

## ✨ 核心特性

### 1. **基于nvidia-smi的实时负载评估**
- 实时获取GPU使用率和内存使用率
- 综合考虑硬件状态和并发负载
- 智能选择负载最低的GPU

### 2. **不同请求类型的并发限制**
```python
concurrent_limits = {
    "chat": 1,      # Chat请求：单卡并发1
    "embedding": 3, # Embedding请求：单卡并发3  
    "ocr": 1        # OCR请求：单卡并发1
}
```

### 3. **统一的负载评分算法**
```python
# 负载得分 = GPU使用率(40%) + 内存使用率(40%) + 并发负载(20%)
final_score = hardware_score + memory_score + concurrency_score
```

## 🚀 使用方法

### 1. **基本使用流程**

```python
# 1. 选择最佳GPU
gpu_id = await unified_gpu_balancer.get_best_gpu("chat")

# 2. 获取GPU槽位
if await unified_gpu_balancer.acquire_gpu_slot(gpu_id, "chat"):
    try:
        # 3. 执行实际请求
        result = await execute_request(gpu_id, request_data)
    finally:
        # 4. 释放GPU槽位
        await unified_gpu_balancer.release_gpu_slot(gpu_id, "chat")
```

### 2. **使用辅助函数**

```python
# 使用封装好的辅助函数
result = await process_request_with_unified_balancer(
    request_type="chat",
    request_data={"message": "Hello"}
)
```

## 📊 API接口

### 1. **获取GPU状态**
```bash
GET /api/gpu/status
```

返回示例：
```json
{
  "status": "success",
  "data": {
    "gpu_count": 4,
    "concurrent_limits": {
      "chat": 1,
      "embedding": 3,
      "ocr": 1
    },
    "current_loads": {
      "0": {"chat": 0, "embedding": 1, "ocr": 0},
      "1": {"chat": 1, "embedding": 0, "ocr": 0},
      "2": {"chat": 0, "embedding": 2, "ocr": 1},
      "3": {"chat": 0, "embedding": 0, "ocr": 0}
    },
    "gpu_details": {
      "0": {
        "name": "NVIDIA GeForce RTX 4090",
        "utilization_percent": 45.0,
        "memory_usage_percent": 60.5,
        "temperature_c": 65,
        "power_draw_w": 250
      }
    }
  }
}
```

### 2. **测试负载均衡器**
```bash
POST /api/test/unified-balancer?request_type=chat
Authorization: Bearer startfrom2023
```

## 🧪 测试脚本

运行测试脚本验证负载均衡器：

```bash
cd tests
python test_unified_gpu_balancer.py
```

测试内容：
- ✅ 单个请求类型测试
- ✅ 混合并发请求测试
- ✅ GPU分配统计分析
- ✅ 负载均衡效果验证

## 📈 负载均衡策略

### 1. **GPU选择算法**
1. 过滤掉已满的GPU（并发数达到限制）
2. 获取每个可用GPU的硬件状态
3. 计算综合负载得分
4. 选择得分最低的GPU

### 2. **负载得分计算**
```python
# GPU硬件使用率 (40%)
hardware_score = (utilization_percent / 100.0) * 0.4

# GPU内存使用率 (40%)  
memory_score = (memory_usage_percent / 100.0) * 0.4

# 当前并发负载 (20%)
concurrency_score = (current_load / max_load) * 0.2

# 最终得分（越低越好）
final_score = hardware_score + memory_score + concurrency_score
```

### 3. **并发控制**
- **Chat**: 每个GPU最多1个并发（LLM推理资源密集）
- **Embedding**: 每个GPU最多3个并发（相对轻量）
- **OCR**: 每个GPU最多1个并发（视觉处理资源密集）

## 🔧 配置说明

### 1. **修改并发限制**
```python
# 在UnifiedGPULoadBalancer.__init__()中修改
self.concurrent_limits = {
    "chat": 2,      # 增加Chat并发
    "embedding": 5, # 增加Embedding并发
    "ocr": 2        # 增加OCR并发
}
```

### 2. **调整负载评分权重**
```python
# 在_calculate_load_score()方法中修改权重
hardware_score = (utilization_percent / 100.0) * 0.5  # 增加硬件权重
memory_score = (memory_usage_percent / 100.0) * 0.3   # 减少内存权重
concurrency_score = load_ratio * 0.2                  # 保持并发权重
```

## 🎯 优势对比

### 旧架构问题：
- ❌ UnifiedServerManager职责过多
- ❌ SimpleTaskManager名不副实
- ❌ GPUResourceManager功能重叠
- ❌ TaskRouter没发挥作用

### 新架构优势：
- ✅ 单一职责：只负责GPU负载均衡
- ✅ 统一管理：所有请求类型统一处理
- ✅ 实时监控：基于nvidia-smi实时状态
- ✅ 智能分配：综合考虑多个负载因素
- ✅ 易于扩展：新增请求类型很简单

## 📝 使用建议

1. **逐步迁移**：先在新功能中使用，逐步替换旧的负载均衡逻辑
2. **监控效果**：通过`/api/gpu/status`接口监控分配效果
3. **调优参数**：根据实际负载情况调整并发限制和权重
4. **测试验证**：定期运行测试脚本验证负载均衡效果

## 🚀 下一步计划

1. **集成到现有接口**：将新负载均衡器集成到Chat、Embedding、OCR接口
2. **性能优化**：优化GPU状态获取的性能开销
3. **监控面板**：创建可视化的GPU负载监控面板
4. **自动调优**：基于历史数据自动调整负载均衡参数

这个新的统一负载均衡器解决了之前架构混乱的问题，提供了一个清晰、高效的GPU资源管理解决方案！
