#!/bin/bash

# HNGPT代码备份脚本
# 创建不包含大文件的代码压缩包

set -e

# 配置
BACKUP_NAME="hngpt-code-backup-$(date +%Y%m%d-%H%M%S)"
BACKUP_DIR="/tmp/${BACKUP_NAME}"
ARCHIVE_PATH="/workspace/hngpt/${BACKUP_NAME}.tar.gz"

echo "🚀 开始创建HNGPT代码备份..."
echo "备份名称: ${BACKUP_NAME}"
echo "目标路径: ${ARCHIVE_PATH}"

# 创建临时目录
mkdir -p "${BACKUP_DIR}"

# 复制核心代码文件
echo "📁 复制核心代码文件..."

# Python代码
cp -r /workspace/hngpt/*.py "${BACKUP_DIR}/" 2>/dev/null || true
cp -r /workspace/hngpt/app.conf* "${BACKUP_DIR}/" 2>/dev/null || true

# OCR相关代码（排除大文件）
mkdir -p "${BACKUP_DIR}/ocr"
cp -r /workspace/hngpt/ocr/*.py "${BACKUP_DIR}/ocr/" 2>/dev/null || true
cp -r /workspace/hngpt/ocr/cpp "${BACKUP_DIR}/ocr/" 2>/dev/null || true
# 排除build目录
rm -rf "${BACKUP_DIR}/ocr/cpp/build" 2>/dev/null || true

# 静态文件（排除大文件）
mkdir -p "${BACKUP_DIR}/static"
find /workspace/hngpt/static -type f -size -1M -exec cp --parents {} "${BACKUP_DIR}/" \; 2>/dev/null || true

# 配置和文档文件
cp /workspace/hngpt/README.md "${BACKUP_DIR}/" 2>/dev/null || true
cp /workspace/hngpt/.gitignore "${BACKUP_DIR}/" 2>/dev/null || true
cp /workspace/hngpt/authorized_users.txt "${BACKUP_DIR}/" 2>/dev/null || true

# 文档目录
cp -r /workspace/hngpt/docs "${BACKUP_DIR}/" 2>/dev/null || true

# 创建备份信息文件
cat > "${BACKUP_DIR}/BACKUP_INFO.md" << EOF
# HNGPT代码备份信息

## 备份时间
$(date '+%Y-%m-%d %H:%M:%S')

## 备份内容
- ✅ 所有Python源代码文件
- ✅ OCR C++源代码（不含编译产物）
- ✅ 静态资源文件（<1MB）
- ✅ 配置文件和文档
- ✅ Git配置文件

## 排除内容
- ❌ 大模型文件（PP-DocLayout-L/）
- ❌ 测试结果图片和日志
- ❌ 编译产物（*.so, *.trt, *.onnx）
- ❌ 临时文件和缓存
- ❌ Git历史记录

## 主要功能模块
1. **FastAPI应用主体** (app.py)
   - 统一任务管理器
   - 多GPU负载均衡
   - OCR和聊天API接口

2. **OCR模块** (ocr/)
   - C++高性能OCR引擎
   - Python接口封装
   - 性能测试和诊断工具

3. **静态资源** (static/)
   - Web界面文件
   - Swagger UI文档

4. **配置文件**
   - 应用配置 (app.conf)
   - 用户授权 (authorized_users.txt)
   - Git忽略规则 (.gitignore)

## 恢复说明
1. 解压缩包到目标目录
2. 安装Python依赖
3. 编译OCR C++模块
4. 下载所需的模型文件
5. 配置GPU环境

## 最近更新
- 🔇 日志中间件：过滤频繁监控请求
- 📊 GPU信息增强：支持显卡名称获取
- 🧪 OCR性能测试：100次测试统计分析
- 🔍 性能诊断工具：识别OCR瓶颈
- 🛠️ 应用关闭修复：移除错误调用
EOF

# 创建文件清单
echo "📋 生成文件清单..."
find "${BACKUP_DIR}" -type f | sort > "${BACKUP_DIR}/FILE_LIST.txt"

# 计算总大小
TOTAL_SIZE=$(du -sh "${BACKUP_DIR}" | cut -f1)
echo "📊 备份内容总大小: ${TOTAL_SIZE}"

# 创建压缩包
echo "🗜️ 创建压缩包..."
cd /tmp
tar -czf "${ARCHIVE_PATH}" "${BACKUP_NAME}/"

# 清理临时目录
rm -rf "${BACKUP_DIR}"

# 显示结果
ARCHIVE_SIZE=$(ls -lah "${ARCHIVE_PATH}" | awk '{print $5}')
echo ""
echo "✅ 代码备份创建完成！"
echo "📦 压缩包路径: ${ARCHIVE_PATH}"
echo "📊 压缩包大小: ${ARCHIVE_SIZE}"
echo ""
echo "🔍 压缩包内容预览:"
tar -tzf "${ARCHIVE_PATH}" | head -20
echo "..."
echo ""
echo "💡 使用方法:"
echo "  解压: tar -xzf ${BACKUP_NAME}.tar.gz"
echo "  查看: tar -tzf ${BACKUP_NAME}.tar.gz"
echo ""
echo "🎉 备份完成！"
