#!/usr/bin/env python3
"""
HNGPT-AI更新包制作工具
包含项目文件和Ollama更新
使用: python make_update_package.py
部署: tar -xzvf update.tar.gz -C /
"""

import os
import sys
import tarfile
import logging
import shutil
import tempfile
from datetime import datetime

def setup_logging():
    """设置日志"""
    formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)

def check_ollama_files():
    """检查系统中已安装的Ollama文件，兼容 /usr 与 /usr/local"""
    candidates_bin = ["/usr/bin/ollama", "/usr/local/bin/ollama"]
    candidates_lib = ["/usr/lib/ollama", "/usr/local/lib/ollama"]

    found_bin = next((p for p in candidates_bin if os.path.exists(p)), None)
    found_lib = next((p for p in candidates_lib if os.path.exists(p)), None)

    if found_bin:
        logging.info(f"✓ 发现Ollama可执行文件: {found_bin}")
        if found_lib:
            logging.info(f"✓ 发现Ollama库目录: {found_lib}")
        else:
            logging.warning("⚠️ 未找到Ollama库目录: /usr/lib/ollama 或 /usr/local/lib/ollama")
        return True
    else:
        logging.error("❌ 未找到Ollama可执行文件: /usr/bin/ollama 或 /usr/local/bin/ollama")
        logging.error("请确保Ollama已正确安装在系统中")
        return False

def create_hngpt_update_package():
    """创建HNGPT-AI更新包"""
    output_file = "update.tar.gz"
    
    logging.info("🚀 开始创建HNGPT-AI更新包")
    logging.info("=" * 50)
    
    # 检查系统中的Ollama文件
    if not check_ollama_files():
        return False
    
    try:
        # 删除旧的更新包
        if os.path.exists(output_file):
            os.remove(output_file)
            logging.info(f"已删除旧的更新包: {output_file}")
        
        # 需要打包的项目文件列表
        project_files = [
            # 核心文件
            "app.py",
            "app.conf", 
            "authorized_users.txt",
            "compile.py",          
            "static",
            "models/hngpt",
            "models/hngpt.modelfile",
            "models/hngpt-embedding",
            "models/hngpt-embedding.modelfile"
        ]
        
        files_to_pack = []
        
        # 1. 添加项目文件
        logging.info("📁 添加项目文件:")
        for item in project_files:
            if os.path.exists(item):
                if os.path.isdir(item):
                    # 目录 - 递归添加所有文件
                    for root, _, filenames in os.walk(item):
                        for filename in filenames:
                            src_path = os.path.join(root, filename)
                            # 目标路径: workspace/hngpt/...
                            dst_path = os.path.join("workspace/hngpt", src_path)
                            files_to_pack.append((src_path, dst_path))
                    logging.info(f"  ✓ {item}/ (目录)")
                else:
                    # 文件
                    dst_path = os.path.join("workspace/hngpt", item)
                    files_to_pack.append((item, dst_path))
                    logging.info(f"  ✓ {item}")
            else:
                logging.warning(f"  ⚠️ 跳过不存在的文件: {item}")
        
        # 2. 添加Ollama文件 (从系统安装位置打包到 /usr/local/)
        logging.info("\n🔧 添加Ollama文件:")

        # 系统中Ollama的安装位置（优先 /usr，其次 /usr/local）
        ollama_bin_src = "/usr/bin/ollama" if os.path.exists("/usr/bin/ollama") else "/usr/local/bin/ollama"
        ollama_lib_src = "/usr/lib/ollama" if os.path.exists("/usr/lib/ollama") else "/usr/local/lib/ollama"

        # Ollama可执行文件 -> /usr/bin/ollama（目标统一到 /usr）
        if os.path.exists(ollama_bin_src):
            files_to_pack.append((ollama_bin_src, "usr/bin/ollama"))
            logging.info("  ✓ 将可执行文件打包到 /usr/bin/ollama")
        else:
            logging.error(f"  ❌ 找不到Ollama可执行文件: {ollama_bin_src}")
            return False

        # Ollama库文件 -> /usr/lib/ollama/（目标统一到 /usr）
        if os.path.exists(ollama_lib_src):
            for root, _, filenames in os.walk(ollama_lib_src):
                for filename in filenames:
                    src_path = os.path.join(root, filename)
                    # 计算相对路径
                    rel_path = os.path.relpath(src_path, ollama_lib_src)
                    # 目标路径: /usr/lib/ollama/
                    dst_path = os.path.join("usr/lib/ollama", rel_path)
                    files_to_pack.append((src_path, dst_path))

            logging.info("  ✓ 将库文件打包到 /usr/lib/ollama/")
        else:
            logging.warning("  ⚠️ 找不到Ollama库目录，将跳过库文件")

        # 3. 创建更新包
        logging.info(f"\n📦 创建更新包: {output_file}")
        logging.info(f"总文件数: {len(files_to_pack)}")
        
        with tarfile.open(output_file, "w:gz") as tar:
            for src_path, dst_path in sorted(files_to_pack):
                logging.debug(f"添加: {dst_path}")
                tar.add(src_path, dst_path)
        
        # 4. 显示结果
        size = os.path.getsize(output_file)
        logging.info(f"✅ 更新包创建成功!")
        logging.info(f"📁 文件: {output_file}")
        logging.info(f"📊 大小: {size/1024/1024:.2f} MB")
        
        return True
        
    except Exception as e:
        logging.error(f"创建更新包时出错: {str(e)}")
        return False
        
    finally:
        # 不需要清理临时目录，因为直接从系统路径打包
        pass

def show_deployment_instructions():
    """显示部署说明"""
    print("\n" + "="*60)
    print("🚀 HNGPT-AI更新包部署说明")
    print("="*60)
    print("\n🔧 部署步骤:")
    print("1. 将 update.tar.gz 复制到目标服务器")
    print("2. 在目标服务器上执行以下命令:")
    print()
    print("   # 停止服务")
    print("   sudo supervisorctl stop all")
    print("   sudo pkill -f ollama")
    print()
    print("   # 备份当前版本 (可选)")
    print("   sudo cp -r /workspace/hngpt /workspace/hngpt.backup.$(date +%Y%m%d_%H%M%S)")
    print("   sudo cp -r /usr/lib/ollama /usr/lib/ollama.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true")
    print("   sudo cp -r /usr/local/lib/ollama /usr/local/lib/ollama.backup.$(date +%Y%m%d_%H%M%S) 2>/dev/null || true")
    print()
    print("   # 解压更新包 (一键更新)")
    print("   sudo tar -xzvf update.tar.gz -C /")
    print()
    print("   # 预清理旧版本 (重要)")
    print("   sudo rm -f /usr/bin/ollama /usr/local/bin/ollama")
    print("   sudo rm -rf /usr/lib/ollama /usr/local/lib/ollama")
    print()
    print("   # 解压后设置权限")
    print("   sudo chmod +x /usr/bin/ollama")
    print()
    print("   # 启动服务")
    print("   sudo supervisorctl start all")
    print()
    print("✅ 更新完成! HNGPT-AI和Ollama都已更新到最新版本")
    print()
    print("📍 Ollama更新位置:")
    print("   • /usr/bin/ollama (可执行文件)")
    print("   • /usr/lib/ollama/ (库文件)")
    print()
    print("💡 说明:")
    print("   - 统一安装到 /usr 目录，与官方手工安装一致")
    print("   - 如果需要兼容 /usr/local，可手动创建符号链接: ln -sf /usr/bin/ollama /usr/local/bin/ollama")

    print("\n💡 说明:")
    print("  - 更新包会自动覆盖项目文件和Ollama")
    print("  - 配置文件会被保留")
    print("  - 如有问题可以从备份恢复")

def main():
    """主函数"""
    setup_logging()
    
    print("🚀 HNGPT-AI更新包制作工具")
    print("=" * 40)
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查运行环境
    if not os.getcwd().endswith('/hngpt'):
        logging.error("❌ 请在hngpt项目目录下运行此脚本")
        logging.info(f"当前目录: {os.getcwd()}")
        sys.exit(1)
    
    # 检查关键文件
    required_files = ["app.py"]
    missing_files = [f for f in required_files if not os.path.exists(f)]

    if missing_files:
        logging.error(f"❌ 缺少必要文件: {', '.join(missing_files)}")
        sys.exit(1)

    # 检查系统中的Ollama（兼容 /usr 与 /usr/local）
    if not (os.path.exists("/usr/bin/ollama") or os.path.exists("/usr/local/bin/ollama")):
        logging.error("❌ 系统中未找到Ollama，请先安装Ollama")
        logging.info("手工安装: curl -LO https://ollama.com/download/ollama-linux-amd64.tgz && sudo tar -C /usr -xzf ollama-linux-amd64.tgz")
        sys.exit(1)

    logging.info("✅ 环境检查通过")
    
    # 创建更新包
    if create_hngpt_update_package():
        show_deployment_instructions()
    else:
        logging.error("❌ 更新包创建失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
